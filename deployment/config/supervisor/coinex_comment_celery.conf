[program:coinex_comment_celery_beat]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_comment/venv/bin/python patch_celery.py -A run_celery.celery beat -l info --schedule=/var/log/coinex_comment/celery-beat-schedule --pidfile=/var/run/coinex-comment-celery-beat.pid
directory = /var/www/coinex_comment
stdout_logfile = /var/log/coinex_comment/celery_beat.log
stderr_logfile = /var/log/coinex_comment/celery_beat_error.log
autostart = true
autorestart = true

[program:coinex_comment_default_worker]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_comment/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "default" -c 2 -n comment_default@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_comment/
stdout_logfile = /var/log/coinex_comment/comment_default_worker.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_comment_schedule_worker]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_comment/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "comment_schedule" -c 10 -n comment_schedule@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_comment/
stdout_logfile = /var/log/coinex_comment/comment_schedule_worker.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30


[program:coinex_comment_moderation]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_comment/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "comment_moderation" -c 15 -n moderation@%%h -P gevent
directory = /var/www/coinex_comment/
stdout_logfile = /var/log/coinex_comment/moderation_worker.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30


[program:coinex_comment_search_index]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_comment/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "search_index" -c 5 -n search_index@%%h
directory = /var/www/coinex_comment/
stdout_logfile = /var/log/coinex_comment/search_index_worker.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30

[program:coinex_comment_user_info]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_comment/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "user_info" -c 5 -n user_info@%%h
directory = /var/www/coinex_comment/
stdout_logfile = /var/log/coinex_comment/user_info_worker.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30


[program:coinex_comment_statistics]
environment = C_FORCE_ROOT="true"
command = /var/www/coinex_comment/venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "statistics" -c 2  -n statistics@%%h --without-gossip --without-mingle --events
directory = /var/www/coinex_comment/
stdout_logfile = /var/log/coinex_comment/statistics_worker.log
redirect_stderr = true
autostart = true
autorestart = true
stopasgroup = true
stopwaitsecs = 30


