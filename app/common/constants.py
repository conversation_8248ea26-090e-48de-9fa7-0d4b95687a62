from enum import Enum
from types import MappingProxyType
from typing import NamedTuple, Mapping

from enum import Enum


ADMIN_EXPORT_LIMIT = 50000


class CeleryQueues(str, Enum):
    """Celery 任务队列定义"""

    # 评论系统定时任务（批处理）
    # - process_comment_votes (5min)
    # - process_comment_report (1h)
    # - check_pending_moderations (1min)
    # - push_comment_interaction_info (3h)
    COMMENT_SCHEDULE = 'comment_schedule'

    # 评论审核即时任务
    # - initial_moderate
    COMMENT_MODERATION = 'comment_moderation'

    # 搜索索引更新
    # - create_comment_fulltext_search
    SEARCH_INDEX = 'search_index'

    # 用户数据更新
    # - update_user_info
    USER_INFO = 'user_info'

    # 数据统计分析
    # - update_comment_user_statistics_schedule
    STATISTICS = 'statistics'


class Platform(Enum):
    WEB = 'web'
    ANDROID = 'android'
    IOS = 'ios'


class Language(Enum):
    EN_US = DEFAULT = 'en_US'
    ZH_HANS_CN = 'zh_Hans_CN'
    ZH_HANT_HK = 'zh_Hant_HK'
    JA_JP = 'ja_JP'
    RU_KZ = 'ru_KZ'
    KO_KP = 'ko_KP'
    ID_ID = 'id_ID'
    ES_ES = 'es_ES'
    FA_IR = 'fa_IR'
    TR_TR = 'tr_TR'
    VI_VN = 'vi_VN'
    AR_AE = 'ar_AE'
    FR_FR = 'fr_FR'
    PT_PT = 'pt_PT'
    DE_DE = 'de_DE'
    TH_TH = 'th_TH'
    IT_IT = 'it_IT'
    PL_PL = 'pl_PL'

    @classmethod
    def from_lang2(cls, lang2: str) -> 'Type[Language] | None':
        local = LANGUAGE_REGION_MAP.get(lang2)
        if local is None:
            return None
        return cls[f'{lang2}_{local}'.upper()]


# 定义语言和对应的默认区域值
LANGUAGE_REGION_MAP = {
    'en': 'US',  # 默认英语
    'zh_Hans': 'CN',  # 简体中文
    'zh_Hant': 'HK',  # 繁体中文
    'ja': 'JP',  # 日语
    'ru': 'KZ',  # 俄语
    'ko': 'KP',  # 韩语
    'id': 'ID',  # 印尼语
    'es': 'ES',  # 西班牙语
    'fa': 'IR',  # 波斯语
    'tr': 'TR',  # 土耳其语
    'vi': 'VN',  # 越南语
    'ar': 'AE',  # 阿拉伯语
    'fr': 'FR',  # 法语
    'pt': 'PT',  # 葡萄牙语
    'de': 'DE',  # 德语
    'th': 'TH',  # 泰语
    'it': 'IT',  # 意大利语
    'pl': 'PL',  # 波兰语
}

_LN = NamedTuple('LanguageNames',
                 [('english', str),
                  ('chinese', str),
                  ('native', str)], )

# https://en.wikipedia.org/wiki/List_of_language_names
LANGUAGE_NAMES: Mapping[Language, _LN] = MappingProxyType({
    Language.EN_US: _LN('English', '英语', 'English'),
    Language.ZH_HANS_CN: _LN('Chinese Simplified', '简体中文', '简体中文'),
    Language.ZH_HANT_HK: _LN('Chinese Traditional', '繁体中文', '繁體中文'),
    Language.JA_JP: _LN('Japanese', '日语', '日本語'),
    Language.RU_KZ: _LN('Russian', '俄语', 'Русский'),
    Language.KO_KP: _LN('Korean', '韩语', '한국어'),
    Language.ID_ID: _LN('Indonesian', '印尼语', 'Bahasa Indonesia'),
    Language.ES_ES: _LN('Spanish', '西班牙语', 'Español'),
    Language.FA_IR: _LN('Persian', '波斯语', 'فارسی'),
    Language.TR_TR: _LN('Turkish', '土耳其语', 'Türk'),
    Language.VI_VN: _LN('Vietnamese', '越南语', 'Tiếng Việt'),
    Language.AR_AE: _LN('Arabic', '阿拉伯语', 'عربى'),
    Language.FR_FR: _LN('French', '法语', 'Français'),
    Language.PT_PT: _LN('Portuguese', '葡萄牙语', 'Português'),
    Language.DE_DE: _LN('German', '德语', 'Deutsch'),
    Language.TH_TH: _LN('Thai', '泰语', 'ไทย'),
    Language.IT_IT: _LN('Italian', '意大利语', 'Italiano'),
    Language.PL_PL: _LN('Polish', '波兰语', 'Polski'),
})


def language_en_names():
    return {lang: ln.english for lang, ln in LANGUAGE_NAMES.items()}


def language_cn_names():
    return {lang: ln.chinese for lang, ln in LANGUAGE_NAMES.items()}


class Translation:

    def __init__(self, t: dict[Language, str]):
        if Language.EN_US not in t:
            raise ValueError(f'Language {Language.EN_US} must exist')
        self.t = t

    def get(self, lang: Language) -> str:
        if lang in self.t:
            return self.t[lang]
        return self.t[Language.EN_US]


TRANSLATION_REPORT_TYPE_FRAUD = Translation({
    Language.EN_US: 'Fraud/Scam (Potentially harmful or phishing links, etc.)',
    Language.ZH_HANS_CN: '欺诈/诈骗（潜在的有害或网络钓鱼链接等）',
    Language.ZH_HANT_HK: '欺詐/詐騙（潛在的有害或網絡釣魚鏈接等）',
    Language.JA_JP: '詐欺/スキャム（潜在的に有害なフィッシングリンクなど）',
    Language.RU_KZ: 'Мошенничество/Обман (потенциально вредные или фишинговые ссылки и т. д.)',
    Language.KO_KP: '사기/사기 (잠재적으로 유해한 피싱 링크 등)',
    Language.ID_ID: 'Penipuan/Scam (potensi berbahaya atau tautan phishing, dll.)',
    Language.ES_ES: 'Fraude/Estafa (potencialmente dañino o enlaces de phishing, etc.)',
    Language.FA_IR: 'کلاهبرداری/تقلب (لینک‌های فیشینگ یا مضر بالقوه و غیره)',
    Language.TR_TR: 'Dolandırıcılık/Sahtekarlık (potansiyel olarak zararlı veya kimlik avı bağlantıları vb.)',
    Language.VI_VN: 'Lừa đảo/Gian lận (có thể gây hại hoặc liên kết lừa đảo, v.v.)',
    Language.AR_AE: 'احتيال/نصب (روابط ضارة محتملة أو روابط تصيد، إلخ)',
    Language.FR_FR: 'Fraude/Escroquerie (liens de phishing ou nuisibles potentiels, etc.)',
    Language.PT_PT: 'Fraude/Golpe (potencialmente prejudicial ou links de phishing, etc.)',
    Language.DE_DE: 'Betrug/Täuschung (potenziell schädliche oder Phishing-Links usw.)',
    Language.TH_TH: 'การฉ้อโกง/การหลอกลวง (ลิงก์ฟิชชิงที่อาจเป็นอันตรายหรืออื่น ๆ)',
    Language.IT_IT: 'Frode/Truffa (potenzialmente dannoso o link di phishing, ecc.)',
    Language.PL_PL: 'Oszustwo/Wyłudzenie (potencjalnie szkodliwe lub linki phishingowe itp.)',
})

TRANSLATION_REPORT_TYPE_MALICIOUS = Translation({
    Language.EN_US: 'Malicious/Negative',
    Language.ZH_HANS_CN: '恶意/消极消息',
    Language.ZH_HANT_HK: '惡意/消極消息',
    Language.JA_JP: '悪意/ネガティブ',
    Language.RU_KZ: 'Злонамеренный/Негативный',
    Language.KO_KP: '악의적/부정적',
    Language.ID_ID: 'Berbahaya/Negatif',
    Language.ES_ES: 'Malicioso/Negativo',
    Language.FA_IR: 'بدخواهانه/منفی',
    Language.TR_TR: 'Kötü niyetli/Olumsuz',
    Language.VI_VN: 'Độc hại/Tiêu cực',
    Language.AR_AE: 'خبيث/سلبي',
    Language.FR_FR: 'Malveillant/Négatif',
    Language.PT_PT: 'Malicioso/Negativo',
    Language.DE_DE: 'Böswillig/Negativ',
    Language.TH_TH: 'เป็นอันตราย/เชิงลบ',
    Language.IT_IT: 'Malevolo/Negativo',
    Language.PL_PL: 'Złośliwy/Negatywny',
})

TRANSLATION_REPORT_TYPE_SPAM = Translation({
    Language.EN_US: 'Spam',
    Language.ZH_HANS_CN: '垃圾消息',
    Language.ZH_HANT_HK: '垃圾消息',
    Language.JA_JP: 'スパム',
    Language.RU_KZ: 'Спам',
    Language.KO_KP: '스팸',
    Language.ID_ID: 'Spam',
    Language.ES_ES: 'Spam',
    Language.FA_IR: 'هرزنامه',
    Language.TR_TR: 'Spam',
    Language.VI_VN: 'Spam',
    Language.AR_AE: 'رسائل مزعجة',
    Language.FR_FR: 'Spam',
    Language.PT_PT: 'Spam',
    Language.DE_DE: 'Spam',
    Language.TH_TH: 'สแปม',
    Language.IT_IT: 'Spam',
    Language.PL_PL: 'Spam',
})

TRANSLATION_REPORT_TYPE_FAKE = Translation({
    Language.EN_US: 'Fake Interaction',
    Language.ZH_HANS_CN: '虚假互动',
    Language.ZH_HANT_HK: '虛假互動',
    Language.JA_JP: '偽のインタラクション',
    Language.RU_KZ: 'Поддельное взаимодействие',
    Language.KO_KP: '가짜 상호작용',
    Language.ID_ID: 'Interaksi Palsu',
    Language.ES_ES: 'Interacción Falsa',
    Language.FA_IR: 'تعامل جعلی',
    Language.TR_TR: 'Sahte Etkileşim',
    Language.VI_VN: 'Tương tác giả',
    Language.AR_AE: 'تفاعل مزيف',
    Language.FR_FR: 'Interaction Factice',
    Language.PT_PT: 'Interação Falsa',
    Language.DE_DE: 'Gefälschte Interaktion',
    Language.TH_TH: 'การมีส่วนร่วมปลอม',
    Language.IT_IT: 'Interazione Falsa',
    Language.PL_PL: 'Fałszywa Interakcja',
})

TRANSLATION_REPORT_TYPE_OTHER = Translation({
    Language.EN_US: 'Other',
    Language.ZH_HANS_CN: '其他',
    Language.ZH_HANT_HK: '其他',
    Language.JA_JP: 'その他',
    Language.RU_KZ: 'Другое',
    Language.KO_KP: '기타',
    Language.ID_ID: 'Lainnya',
    Language.ES_ES: 'Otro',
    Language.FA_IR: 'سایر',
    Language.TR_TR: 'Diğer',
    Language.VI_VN: 'Khác',
    Language.AR_AE: 'آخر',
    Language.FR_FR: 'Autre',
    Language.PT_PT: 'Outro',
    Language.DE_DE: 'Andere',
    Language.TH_TH: 'อื่นๆ',
    Language.IT_IT: 'Altro',
    Language.PL_PL: 'Inne',
})

TRANSLATION_ERROR = Translation({
    Language.EN_US: 'Translation failed',
    Language.ZH_HANS_CN: '翻译失败',
    Language.ZH_HANT_HK: '翻譯失敗',
    Language.JA_JP: '翻訳失敗',
    Language.RU_KZ: 'Ошибка перевода',
    Language.KO_KP: '번역 실패',
    Language.ID_ID: 'Terjemahan gagal',
    Language.ES_ES: 'Error de traducción',
    Language.FA_IR: 'ترجمه ناموفق',
    Language.TR_TR: 'Çeviri başarısız',
    Language.VI_VN: 'Dịch thất bại',
    Language.AR_AE: 'فشل الترجمة',
    Language.FR_FR: 'Échec de la traduction',
    Language.PT_PT: 'Falha na tradução',
    Language.DE_DE: 'Übersetzung fehlgeschlagen',
    Language.TH_TH: 'การแปลล้มเหลว',
    Language.IT_IT: 'Traduzione fallita',
    Language.PL_PL: 'Tłumaczenie nie powiodło się'
})

COMPETITOR_DOMAIN = ['binance.com', 'coinbase.com', 'bybit.com', 'okx.com', 'bitfinex.com', 'kraken.com',
                     'htx.com', 'crypto.com', 'gate.io', 'mexc.com', 'jgz.com', 'bithumb.com', 'gemini.com',
                     'bitmex.com', 'megabit.vip', 'coinw.com', 'bitflyer.com', 'bitstamp.net', 'upbit.com',
                     'kucoin.com', 'poloniex.com', 'deribit.com', 'xex.vip', 'sunbit.vip', 'sun7936.me', 'fbxex.com',
                     'bigone.com', 'big.one', 'digifinex.tv', 'digifinex.com', 'weex.com', 'bikaglobal.one',
                     'aaa.comobitcoin.com','bbb.comobitcoin.com', 'coinone.co.kr', 'wang.tel', 'bikingex0.com', 'deepcoin.com',
                     'tpros.xyz','bvox.com', 'bitvenus.me', 'eeee.com', 'trubit.com', 'luno.com', 'silkpro.io', 'sklud.com',
                     'sklud.com', 'silkpro.io', 'hotcoin.com', 'doex.com', 'xt.com', 'xt.com', 'bitdu.com',
                     'bitdu.store','bitwasabi.com', 'bitmart.news', 'bitmart.com', 'bitmart.news'"jgz.com","custerex.com","tebbit.io",
                     "bitmake.com","jbex.cc","btcbox.co.jp","bwfx.pro","bifinance.com","zaif.jp","ftk.com","coinup.io","ueex.com","ueex.bet","coinup.world",
                     "jucoin.com","tebbit.vip","jgz555.com","websea.com","bcwex.co","bitcoinwin.io",
                     "8v.com","hkex.la","morganglobal.me","morganglobal.online","exmo.com","exmo.me",
                     "parrots.one","parrots.buzz","bitbank.cc","klickl.com","klickl.io","bibvip.com",
                     "bibvip.net","ceex.com","ibit.global","coincheck.com","mbex.me","bitop.com","salavi.com",
                     "3ex.global","3ex.tech","safex.trading","safex.hk","hitbtc.com","lbank.com","asdexcn.co","ascendex.com",
                     "ulinkex.ai","ulinkex3.com"
                     ]
COMPETITOR_KEY_WORDS= ['Bitget', '火币', 'Bitfinex', 'FTX', '币安', '欧易', 'OKX', 'Bithumb', '幣安', 'Gate', 'HTX', 'Gate.io', 'KuCoin', 'Kraken', 'Binance', 'Bybit']


TARGET_DIGITS_EMOJIS = {"0️⃣", "1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣", "7️⃣", "8️⃣", "9️⃣", "🔟"}

# todo morrey 更新翻译
MSG_TITLE_SELF_UP_TITLE = Translation({
    Language.EN_US: 'My like',
    Language.ZH_HANS_CN: '我的点赞',
    Language.ZH_HANT_HK: '',
    Language.JA_JP: '',
    Language.RU_KZ: '',
    Language.KO_KP: '',
    Language.ID_ID: '',
    Language.ES_ES: '',
    Language.FA_IR: '',
    Language.TR_TR: '',
    Language.VI_VN: '',
    Language.AR_AE: "",
    Language.FR_FR: '',
    Language.PT_PT: '',
    Language.DE_DE: '',
    Language.TH_TH: '',
    Language.IT_IT: '',
    Language.PL_PL: '',
})


MSG_TITLE_OTHER_UP_TITLE = Translation({
    Language.EN_US: 'Like',
    Language.ZH_HANS_CN: '赞',
    Language.ZH_HANT_HK: '',
    Language.JA_JP: '',
    Language.RU_KZ: '',
    Language.KO_KP: '',
    Language.ID_ID: '',
    Language.ES_ES: '',
    Language.FA_IR: '',
    Language.TR_TR: '',
    Language.VI_VN: '',
    Language.AR_AE: "",
    Language.FR_FR: '',
    Language.PT_PT: '',
    Language.DE_DE: '',
    Language.TH_TH: '',
    Language.IT_IT: '',
    Language.PL_PL: '',
})


MSG_TITLE_SELF_DOWN_TITLE = Translation({
    Language.EN_US: 'My dislike',
    Language.ZH_HANS_CN: '我的踩',
    Language.ZH_HANT_HK: '',
    Language.JA_JP: '',
    Language.RU_KZ: '',
    Language.KO_KP: '',
    Language.ID_ID: '',
    Language.ES_ES: '',
    Language.FA_IR: '',
    Language.TR_TR: '',
    Language.VI_VN: '',
    Language.AR_AE: "",
    Language.FR_FR: '',
    Language.PT_PT: '',
    Language.DE_DE: '',
    Language.TH_TH: '',
    Language.IT_IT: '',
    Language.PL_PL: '',
})


MSG_TITLE_OTHER_DOWN_TITLE = Translation({
    Language.EN_US: 'Dislike',
    Language.ZH_HANS_CN: '踩',
    Language.ZH_HANT_HK: '',
    Language.JA_JP: '',
    Language.RU_KZ: '',
    Language.KO_KP: '',
    Language.ID_ID: '',
    Language.ES_ES: '',
    Language.FA_IR: '',
    Language.TR_TR: '',
    Language.VI_VN: '',
    Language.AR_AE: "",
    Language.FR_FR: '',
    Language.PT_PT: '',
    Language.DE_DE: '',
    Language.TH_TH: '',
    Language.IT_IT: '',
    Language.PL_PL: '',
})


MSG_TITLE_SELF_COMMENT_TITLE = Translation({
    Language.EN_US: 'My comment',
    Language.ZH_HANS_CN: '我的评论',
    Language.ZH_HANT_HK: '',
    Language.JA_JP: '',
    Language.RU_KZ: '',
    Language.KO_KP: '',
    Language.ID_ID: '',
    Language.ES_ES: '',
    Language.FA_IR: '',
    Language.TR_TR: '',
    Language.VI_VN: '',
    Language.AR_AE: "",
    Language.FR_FR: '',
    Language.PT_PT: '',
    Language.DE_DE: '',
    Language.TH_TH: '',
    Language.IT_IT: '',
    Language.PL_PL: '',
})


MSG_TITLE_SELF_REPLY_TITLE = Translation({
    Language.EN_US: 'My reply',
    Language.ZH_HANS_CN: '我的回复',
    Language.ZH_HANT_HK: '',
    Language.JA_JP: '',
    Language.RU_KZ: '',
    Language.KO_KP: '',
    Language.ID_ID: '',
    Language.ES_ES: '',
    Language.FA_IR: '',
    Language.TR_TR: '',
    Language.VI_VN: '',
    Language.AR_AE: "",
    Language.FR_FR: '',
    Language.PT_PT: '',
    Language.DE_DE: '',
    Language.TH_TH: '',
    Language.IT_IT: '',
    Language.PL_PL: '',
})


MSG_TITLE_OTHER_REPLY_TITLE = Translation({
    Language.EN_US: 'Reply',
    Language.ZH_HANS_CN: '回复',
    Language.ZH_HANT_HK: '',
    Language.JA_JP: '',
    Language.RU_KZ: '',
    Language.KO_KP: '',
    Language.ID_ID: '',
    Language.ES_ES: '',
    Language.FA_IR: '',
    Language.TR_TR: '',
    Language.VI_VN: '',
    Language.AR_AE: "",
    Language.FR_FR: '',
    Language.PT_PT: '',
    Language.DE_DE: '',
    Language.TH_TH: '',
    Language.IT_IT: '',
    Language.PL_PL: '',
})


MSG_TITLE_OTHER_AT_TITLE = Translation({
    Language.EN_US: 'Mention',
    Language.ZH_HANS_CN: '提及',
    Language.ZH_HANT_HK: '',
    Language.JA_JP: '',
    Language.RU_KZ: '',
    Language.KO_KP: '',
    Language.ID_ID: '',
    Language.ES_ES: '',
    Language.FA_IR: '',
    Language.TR_TR: '',
    Language.VI_VN: '',
    Language.AR_AE: "",
    Language.FR_FR: '',
    Language.PT_PT: '',
    Language.DE_DE: '',
    Language.TH_TH: '',
    Language.IT_IT: '',
    Language.PL_PL: '',
})


MSG_TITLE_SELF_TIP_TITLE = Translation({
    Language.EN_US: 'My Tips',
    Language.ZH_HANS_CN: '我的赠送',
    Language.ZH_HANT_HK: '',
    Language.JA_JP: '',
    Language.RU_KZ: '',
    Language.KO_KP: '',
    Language.ID_ID: '',
    Language.ES_ES: '',
    Language.FA_IR: '',
    Language.TR_TR: '',
    Language.VI_VN: '',
    Language.AR_AE: "",
    Language.FR_FR: '',
    Language.PT_PT: '',
    Language.DE_DE: '',
    Language.TH_TH: '',
    Language.IT_IT: '',
    Language.PL_PL: '',
})


MSG_TITLE_OTHER_TIP_TITLE = Translation({
    Language.EN_US: 'Received Tips',
    Language.ZH_HANS_CN: '收到赠送',
    Language.ZH_HANT_HK: '',
    Language.JA_JP: '',
    Language.RU_KZ: '',
    Language.KO_KP: '',
    Language.ID_ID: '',
    Language.ES_ES: '',
    Language.FA_IR: '',
    Language.TR_TR: '',
    Language.VI_VN: '',
    Language.AR_AE: "",
    Language.FR_FR: '',
    Language.PT_PT: '',
    Language.DE_DE: '',
    Language.TH_TH: '',
    Language.IT_IT: '',
    Language.PL_PL: '',
})

