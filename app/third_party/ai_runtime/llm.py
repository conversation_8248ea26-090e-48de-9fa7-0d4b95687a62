import json
from dataclasses import dataclass
from enum import Enum
from typing import Mapping, Any, Iterator
import httpx
import json_repair
import logging
from openai import OpenAI, AzureOpenAI
from anthropic import AnthropicBedrock, AsyncAnthropicBedrock
import boto3
from ollama import Client as OllamaClient

from app import config
from anthropic._tokenizers import sync_get_tokenizer
import tiktoken

_logger = logging.getLogger(__name__)

class ParseError(ValueError):
    pass


class PlatformLLM:

    name = ""
    @dataclass
    class Result:
        content: str | dict | list
        input_tokens: int
        output_tokens: int

    class ModelType(Enum):
        CHEAP = 10
        POWERFUL = 20

    @staticmethod
    def extract_json(text):
        # 从文本中提取JSON字符串
        try:
            # 尝试解析JSON字符串
            return json_repair.loads(text)
        except ValueError:
            # 如果不是有效的JSON字符串,则忽略
            raise ParseError(f'LLM parse result failed: JSON string parse error.\n{text}')

    @staticmethod
    def json_prompt(origin_prompt):
        format_prompt = f"""
    Please export content in JSON format. 
    - Ensure all JSON output is valid and follows the official JSON specification.
    - Enclose all keys and values in double quotes.
    - Avoid using double quotes in string values.
    - Separate all key-value pairs within objects with commas, except for the last pair.
    - Separate all elements in arrays with commas, except for the last element.
    - Do not include comments in the JSON output.
    - Properly escape special characters in strings (e.g., ", \, \n, \r, \t, \b, \f).
    - Use true, false, and null (lowercase) for boolean and null values.
    - Format numbers without quotes unless they are intended to be strings.
    - Maintain proper indentation and formatting for readability.
    - Ensure all opening brackets, braces, and quotes are properly closed.
    - Do not insert line breaks or extra whitespace within string values.
    After generating JSON, please verify that it is valid by mentally parsing it or using a JSON validator if available.
If you're unsure about the validity, please state so and offer to make corrections if needed.
    """
        if origin_prompt:
            return f'{origin_prompt}\n{format_prompt}'
        else:
            return format_prompt

    def __init__(self, async_call=False):
        self.client = None
        self.default_model = None
        self.cheap_model = None
        self.powerful_model = None

        self.input_cost = 5  # 默认输入成本, 单位 美元/1M tokens，或者 美厘/1K tokens
        self.output_cost = 15  # 默认输出成本，单位 美元/1M tokens，或者 美厘/1K tokens

        self.async_call = async_call

    def chat(self, user: str | list, system: str = None, model_type=ModelType.CHEAP,
             temperature: float = 0, max_tokens: int = 4000,
             json_output: bool = False,
             stop: str = None) -> Result:

        llm_kwargs = self._prepare_args(user, system, model_type, temperature, max_tokens, json_output, stop)

        response = self._create_completion(llm_kwargs)
        return self._parse_response(response, json_output)

    async def achat(self, user: str | list, system: str = None, model_type=ModelType.CHEAP,
             temperature: float = 0, max_tokens: int = 4000,
             json_output: bool = False,
             stop: str = None) -> Result:
        llm_kwargs = self._prepare_args(user, system, model_type, temperature, max_tokens, json_output, stop)

        response = await self._async_completion(llm_kwargs)
        return self._parse_response(response, json_output)
    

    def get_model_type(self, model_type: str |  ModelType | None):
        if isinstance(model_type, str):
            model_type = self.ModelType[model_type.upper()]
        return model_type
    
    def get_model_name(self, model_type: str |  ModelType | None):
        if isinstance(model_type, str):
            model_type = self.ModelType[model_type.upper()]
        model_type = self.get_model_type(model_type)
        return self.default_model if model_type is None \
                else self.cheap_model if model_type == self.ModelType.CHEAP \
                else self.powerful_model

    def _prepare_args(self, user: str | list, system: str, model_type: ModelType,
                      temperature: float, max_tokens: int,
                      json_output: bool,
                      stop: str):
        llm_kwargs = locals()
        llm_kwargs.pop('self')

        llm_kwargs['model'] = self.default_model if model_type is None \
            else self.cheap_model if model_type == PlatformLLM.ModelType.CHEAP \
            else self.powerful_model
        llm_kwargs.pop('model_type')

        llm_kwargs['messages'] = [{"role": "user", "content": user}]
        llm_kwargs.pop('user')

        if json_output:
            llm_kwargs['system'] = self.json_prompt(system)

        prepared_kwargs = self._prepare_args_specific(llm_kwargs)

        prepared_kwargs.pop('json_output')
        prepared_kwargs = {k: v for k, v in prepared_kwargs.items() if v is not None}
        return prepared_kwargs

    def _prepare_args_specific(self, kwargs):
        raise NotImplementedError('Subclass must implement abstract method `_prepare_args_specific`')

    def _create_completion(self, kwargs):
        raise NotImplementedError('Subclass must implement abstract method `_create_completion`')

    async def _async_completion(self, kwargs):
        raise NotImplementedError('Subclass must implement abstract method `_create_completion`')

    def _parse_response(self, response, json_output) -> Result:
        raise NotImplementedError('Subclass must implement abstract method `_parse_response`')

    def count_tokens(self, text: str):
        raise NotImplementedError('Subclass must implement abstract method `count_tokens`')


class _PlatformGPTFamily(PlatformLLM):
    def _prepare_args_specific(self, kwargs):
        system = kwargs.get('system')
        if system:
            messages = kwargs.get('messages')
            messages.insert(0, {"role": "system", "content": system})
        kwargs.pop('system')
        return kwargs

    def _create_completion(self, kwargs) -> dict:
        return self.client.chat.completions.create(**kwargs)

    async def _async_completion(self, kwargs):
        completion = await self.client.messages.create(**kwargs)
        return completion

    def _parse_response(self, response, json_output) -> PlatformLLM.Result:
        resp_text = response.choices[0].message.content
        return PlatformLLM.Result(
            content=self.extract_json(resp_text) if json_output else resp_text,
            input_tokens=response.usage.prompt_tokens,
            output_tokens=response.usage.completion_tokens
        )

    def count_tokens(self, text: str):
        encoding = tiktoken.encoding_for_model(self.powerful_model)
        token_count = len(encoding.encode(text))
        return token_count


class PlatformOpenaiGPT(_PlatformGPTFamily):
    def __init__(self, async_call=False):
        super().__init__(async_call)
        self.client = self._create_openai_client()
        self.default_model = 'gpt-3.5-turbo'
        self.cheap_model = self.default_model
        self.powerful_model = 'gpt-4o'

    @classmethod
    def _create_openai_client(cls):
        openai_config: dict = config.get('OPENAI')
        if not openai_config:
            raise ValueError('Openai config not found!')

        openai_token = openai_config.get('TOKEN')
        openai_proxy = openai_config.get('PROXY', None)
        if openai_proxy:
            return OpenAI(api_key=openai_token, http_client=httpx.Client(proxy=openai_proxy))
        else:
            return OpenAI(api_key=openai_token)

    def _prepare_args_specific(self, kwargs):
        if kwargs.get('json_output'):
            "只有 openai GPT 支持 response_format 参数"
            kwargs['response_format'] = {"type": "json_object"}
        return super()._prepare_args_specific(kwargs)


class PlatformKimi(_PlatformGPTFamily):
    def __init__(self):
        super().__init__()

        moonshot_config: dict = config.get('MOONSHOT')
        if not moonshot_config:
            raise ValueError('Moonshot config not found!')

        self.client = OpenAI(
            api_key=moonshot_config.get('TOKEN'),
            base_url="https://api.moonshot.cn/v1",
        )
        self.default_model = 'moonshot-v1-32k'
        self.cheap_model = 'moonshot-v1-8k'
        self.powerful_model = 'moonshot-v1-128k'


class PlatformAzureGPT(_PlatformGPTFamily):

    name = "GPT"

    def __init__(self, async_call=False):
        super().__init__(async_call)

        azure_config: dict = config.get('AZURE_AI')
        if not azure_config:
            raise ValueError('Azure OpenAI config not found!')

        self.client = AzureOpenAI(
            api_key=azure_config.get('API_KEY'),
            api_version="2023-12-01-preview",
            azure_endpoint=azure_config.get('ENDPOINT'),
            # base_url=f"{AZURE_OPENAI_ENDPOINT}/openai/deployments/{deployment_name}",
        )

        self.cheap_model = 'gpt-35-turbo'
        self.powerful_model = 'gpt-4o'
        self.default_model = self.cheap_model

    def _prepare_args_specific(self, kwargs):
        if kwargs.get('stop'):
            raise ValueError('Azure OpenAI does not support stop parameter.')
        return super()._prepare_args_specific(kwargs)


class _PlatformClaudeFamily(PlatformLLM):
    def __init__(self, async_call=False):
        super().__init__(async_call)

        self.cheap_model = 'us.anthropic.claude-3-haiku-20240307-v1:0'
        # self.powerful_model = 'anthropic.claude-3-sonnet-20240229-v1:0'
        self.powerful_model = 'us.anthropic.claude-3-5-sonnet-20240620-v1:0'
        self.default_model = self.powerful_model

        bedrock_config = config.get('AWS_BEDROCK')
        if not bedrock_config:
            raise ValueError('AWS Bedrock config not found!')
        self.service_name: str = bedrock_config.get('SERVICE')  # 连接bedrock-runtime类型节点
        self.region_name = bedrock_config['REGION']
        self.access_key = bedrock_config.get('ACCESS_KEY')
        self.secret_key = bedrock_config.get('SECRET_KEY')

    def _prepare_args_specific(self, kwargs):
        return kwargs

    def _create_completion(self, kwargs) -> dict:
        raise NotImplementedError('Subclass must implement abstract method `_create_completion`')

    def _parse_response(self, response, json_output) -> PlatformLLM.Result:
        raise NotImplementedError('Subclass must implement abstract method `_parse_response`')

    def count_tokens(self, text: str):
        tokenizer = sync_get_tokenizer()
        encoded_text = tokenizer.encode(text)  # type: ignore
        return len(encoded_text.ids)  # type: ignore


class PlatformAwsClaude(_PlatformClaudeFamily):
    def __init__(self):
        super().__init__()

        # import ssl
        self.client = boto3.client(
            service_name=self.service_name,  # 连接bedrock-runtime类型节点
            region_name=self.region_name,
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
            # ssl_context=ssl.create_default_context()
        )

    def _prepare_args_specific(self, kwargs):
        kwargs["anthropic_version"] = "bedrock-2023-05-31"
        # 按照 aws 的文档，这个参数是 required, 而且必须是这个值
        # （https://docs.aws.amazon.com/bedrock/latest/userguide/model-parameters-anthropic-claude-messages.html）
        return super()._prepare_args_specific(kwargs)

    def _create_completion(self, kwargs) -> dict:
        model_id = kwargs.pop('model')
        response = self.client.invoke_model(
            body=json.dumps(kwargs),
            modelId=model_id,
            accept='application/json',
            contentType='application/json'
        )
        resp_bytes = response.get('body').read()
        resp_str = str(resp_bytes, 'utf-8')
        return json.loads(resp_str)

    def _parse_response(self, response, json_output) -> PlatformLLM.Result:
        resp_text = response.get('content')[0].get('text')
        return PlatformLLM.Result(
            content=self.extract_json(resp_text) if json_output else resp_text,
            input_tokens=response['usage']['input_tokens'],
            output_tokens=response['usage']['output_tokens']
        )


class PlatformAnthropicBedrockClaude(_PlatformClaudeFamily):

    name = "CLAUDE"

    def __init__(self, async_call=False):
        super().__init__(async_call)

        # import ssl
        if async_call:
            self.client = AsyncAnthropicBedrock(
                aws_region=self.region_name,
                aws_access_key=self.access_key,
                aws_secret_key=self.secret_key,
            )
        else:
            self.client = AnthropicBedrock(
                aws_region=self.region_name,
                aws_access_key=self.access_key,
                aws_secret_key=self.secret_key,
            )

    def _create_completion(self, kwargs):
        return self.client.with_options(max_retries=2).messages.create(**kwargs)

    async def _async_completion(self, kwargs):
        return await self.client.with_options(max_retries=2).messages.create(**kwargs)

    def _parse_response(self, response, json_output) -> PlatformLLM.Result:
        resp_text = response.content[0].text
        return PlatformLLM.Result(
            content=self.extract_json(resp_text) if json_output else resp_text,
            input_tokens=response.usage.input_tokens,
            output_tokens=response.usage.output_tokens
        )


class PlatformOllama(PlatformLLM):
    def __init__(self):
        super().__init__()

        self.client = OllamaClient(config.get('OLLAMA').get('ENDPOINT'))

        self.default_model = 'qwen2:7b'
        self.cheap_model = self.default_model
        self.powerful_model = 'qwen2:7b'

    def _prepare_args_specific(self, kwargs):
        system = kwargs.get('system')
        if system:
            messages = kwargs.get('messages')
            messages.insert(0, {"role": "system", "content": system})
        kwargs.pop('system')

        if kwargs.get('json_output'):
            "Ollama 的参数叫另外的名字"
            kwargs['format'] = 'json'

        if 'temperature' in kwargs:
            options = kwargs.setdefault('options', {})
            options['temperature'] = kwargs.pop('temperature')

        if 'max_tokens' in kwargs:
            options = kwargs.setdefault('options', {})
            options['max_tokens'] = kwargs.pop('max_tokens')

        return kwargs

    def _create_completion(self, kwargs) -> Mapping[str, Any] | Iterator[Mapping[str, Any]]:
        try:
            return self.client.chat(**kwargs)
        except Exception as e:
            _logger.exception(f'create completion exception: {e}')
            raise e

    def _parse_response(self, response, json_output) -> PlatformLLM.Result:
        resp_text = response['message']['content']
        return PlatformLLM.Result(
            content=self.extract_json(resp_text) if json_output else resp_text,
            input_tokens=0,
            output_tokens=0
        )
