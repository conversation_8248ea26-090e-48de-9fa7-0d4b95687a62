# -*- coding: utf-8 -*-

from app.utils.date_ import current_timestamp
from .base import HashCache, StringCache


class AlertUserTimeCache(StringCache):

    def __init__(self, msg_id: str):
        """
        use string to format kwargs, mix different type may has same keys.
        """
        super().__init__(msg_id)

    def set_cache(self, ex: int):
        self.set('1', ex=ex)

class CommentUserStatsUpdateInfoCache(StringCache):

    def __init__(self):
        super().__init__(None)


class CommentStatsUpdateInfoCache(StringCache):

    def __init__(self):
        super().__init__(None)


class CommentUserDisableCountCache(HashCache):

    TTL = 3600 * 48
    INTERVAL = 60 * 15

    def __init__(self, ts: int = None):
        if not ts:
            ts = current_timestamp(to_int=True)
            ts = ts - ts % self.INTERVAL
        super().__init__(f'{str(ts)}')