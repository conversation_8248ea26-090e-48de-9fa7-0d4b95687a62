from app.models.event import CommentEvent
from .base import HashCache


class CommentMessageUnreadCountCache(HashCache):
    """用户未读消息计数缓存
    使用一个统一的Hash存储所有用户的未读消息总数
    key: comment_unread
    field: user_id
    value: 用户的未读消息总数
    """
    ttl = 3600 * 24  # 24小时
    
    def __init__(self):
        super().__init__('comment_unread')
        
    def reload_user(self, user_id: str):
        """重新加载指定用户的未读消息数"""
        # 统计用户所有未读消息总数
        total = CommentEvent.query.filter(
            user_id=user_id,
            read_status=CommentEvent.ReadStatus.UNREAD
        ).scalar()
                
        if total:
            self.hset(user_id, str(total))
            self.expire(self.ttl)
            
    def incr_unread(self, user_id: int, incr: int = 1):
        """增加用户的未读消息数"""
        self.hincrby(str(user_id), incr)
    
    def decr_unread(self, user_id: int, decr: int = 1):
        """减少用户的未读消息数"""
        new_total = self.hincrby(str(user_id), -decr)
        if new_total <= 0:
            self.hdel(str(user_id))
        
    def clear_unread(self, user_id: int):
        """清空用户的未读消息数"""
        self.hdel(str(user_id))
        
    def get_unread_count(self, user_id: int) -> int:
        """获取用户的未读消息总数
            
        Returns:
            int: 未读消息总数
        """
        count = self.hget(str(user_id))
        return int(count) if count else 0
