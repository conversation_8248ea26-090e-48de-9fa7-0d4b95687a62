from decimal import Decimal
import logging
from typing import Optional

from sqlalchemy import desc, func

from app.business.lock import load_if_not_exists
from app.common.constants import Language
from app.exceptions import InvalidArgument
from app.models import db
from app.models.comment import Comment, SCORE_COLS
from .base import Hash<PERSON><PERSON>, SetCache, SortedSetCache, String<PERSON>ache, ExecutionCheckpointCache, EmptyCollectionMixin

_logger = logging.getLogger(__name__)


SortType = Comment.SortType


class CommentCache(HashCache):
    """单条评论缓存
    使用Hash存储评论的详细信息
    key格式: comment:{comment_id}
    """
    _ttl: int = 3600 * 24  # 1天

    def __init__(self, comment_id: int):
        self.comment_id = comment_id
        super().__init__(str(comment_id))

    def reload(self):
        """重新加载评论信息"""
        # 构建锁的key，包含所有区分不同实例的参数
        comment = Comment.query.get(self.comment_id)
        if comment:
            self.save_comment(comment)

    def read_aside(self):
        data = self.read()
        if data:
            return data
        else:
            self.reload()
        return self.read()

    def save_comment(self, comment: Comment):
        """保存评论信息
        ！！！注意：这里的数据是直接存储的评论对象的属性，可能会覆盖各种计数，谨慎使用
        """
        comment_dict = comment.to_dict(enum_to_name=True, value_to_str=True)
        # 添加互动数据
        comment_dict['vote_count'] = comment.up_count - comment.down_count
        # 将字典值转换为字符串
        self.save(comment_dict)
        self.expire(self._ttl)

    def save_comment_with_keys(self, comment: Comment, keys: [str]):
        """保存评论信息"""
        comment_dict = comment.to_dict(enum_to_name=True, value_to_str=True)
        # 添加互动数据
        comment_dict['vote_count'] = str(comment.up_count - comment.down_count)
        # 将字典值转换为字符串
        update_dict = {}
        for key in keys:
            if key in comment_dict:
                update_dict[key] = comment_dict[key]
        load_if_not_exists(self)

        self.hmset(update_dict)
        self.expire(self._ttl)

    def update_vote_count(self, up_count: int, down_count: int):
        """更新赞成数"""
        load_if_not_exists(self)

        self.hset('up_count', str(up_count))
        self.hset('down_count', str(down_count))
        self.hset('vote_count', str(up_count - down_count))
        self.expire(self._ttl)

    def update_status(self, status: Comment.CommentStatus):
        """更新评论状态"""
        load_if_not_exists(self)
        
        self.hset('status', status.name)
        self.expire(self._ttl)

    def update(self, data_to_be_updated: dict):
        """更新评论信息"""
        load_if_not_exists(self)
        # 将字典值转换为字符串
        update_dict = {key: str(value) for key, value in data_to_be_updated.items()}
        self.hmset(update_dict)
        self.expire(self._ttl)

    def incr_vote_count(self, up_incr: int, down_incr: int) -> int:
        """原子性地增加赞成数
        
        Args:
            up_incr: 增加的赞成数
            down_incr: 增加的反对数
        Returns:
            int: 更新后的赞成总数
        """
        load_if_not_exists(self)
        # 使用 HINCRBY 命令原子性地增加 vote_count 字段的值
        self.hincrby('up_count', up_incr)
        self.hincrby('down_count', down_incr)
        self.expire(self._ttl)
        return self.hincrby('vote_count', up_incr - down_incr)
        
    def get_vote_count(self) -> int:
        """获取当前赞成数"""
        load_if_not_exists(self)
        count = self.hget('vote_count')
        return int(count) if count else 0

    def get_reply_count(self) -> int:
        """获取当前回复数"""
        load_if_not_exists(self)
        count = self.hget('reply_count')
        return int(count) if count else 0

    def incr_reply_count(self, incr: int) -> int:
        """原子性地增减回复数
        
        Args:
            incr: 增加的回复数
        Returns:
            int: 更新后的回复总数
        """
        load_if_not_exists(self)
        self.expire(self._ttl)
        return self.hincrby('reply_count', incr)
    
    def update_tip_data(self, tip_count: int, tip_user_count: int, tip_amount: Decimal):
        """更新打赏数据"""
        load_if_not_exists(self)
        
        self.hset('tip_count', str(tip_count))
        self.hset('tip_user_count', str(tip_user_count))
        self.hset('tip_amount', str(tip_amount))
        

    @classmethod
    def get_score(cls, data: dict, sort_type):
        # 根据排序类型获取排序分数
        if sort_type not in SortType:
            raise InvalidArgument(f'sort_type is invalid: {sort_type}')
        return float(data.get(Comment.score_attr_name(sort_type)))

    def get_comment(self, aside=True) -> Optional[Comment]:
        if aside:
            data = self.read_aside()
        else:
            data = self.read()

        if not data:
            return None
        return Comment.from_dict(data, name_to_enum=True)

    @classmethod
    def get_comments_from_ids(cls, comment_ids: list[int]) -> list[Comment]:
        """从缓存列表 ids 中批量获取多条评论"""
        # 批量获取缓存数据
        cache_data = cls.batch_read([str(comment_id) for comment_id in comment_ids])
        
        # 处理未命中的缓存
        comments = []
        for comment_id in comment_ids:
            if str(comment_id) in cache_data:
                comment = Comment.from_dict(cache_data[str(comment_id)], name_to_enum=True)
                comments.append(comment)
            else:
                comment_cache = cls(comment_id)
                comment_cache.reload()
                comment = comment_cache.get_comment()
                comments.append(comment)
        
        return comments


class CommentListCache(EmptyCollectionMixin, SortedSetCache):
    """统一的评论列表缓存"""
    _ttl: int = 3600 * 24  # 24小时
    _MAX_SIZE: int = 1000  # 最大缓存条数
    
    def __init__(self, business: Comment.Business = None, business_id: str = None,
                 lang: Language = None, sort_type: Comment.SortType = Comment.SortType.NEW,
                 root_id: Optional[int] = None):
        """
        Args:
            business: 业务代码，比如 'COIN'
            business_id: 业务ID，比如 '1', 'AB126FD'
            sort_type: 排序类型 (hot/new/top)
            root_id: 回复列表需要的父评论ID (如果是评论列表则为None)
            lang: 语言，它和 root_id 是互斥的，如果有 lang，说明是一级评论列表；如果有 root_id，说明是回复列表
        """
        self.business = business
        self.business_id = business_id
        self.lang = lang
        self.sort_type = sort_type
        self.root_id = root_id

        # 构建缓存key
        if self.root_id:
            if self.sort_type != Comment.SortType.TOP:
                raise InvalidArgument('sort_type must be TOP for reply list')
            key = f'reply:{self.root_id}:{self.sort_type.name}'
        else:
            key = f'root:{self.business.name}:{self.business_id}:{self.lang.name}:{self.sort_type.name}'
            
        super().__init__(key)

    def reload(self):
        """重新加载评论列表"""
        comments = Comment.get_comments(
            business=self.business,
            business_id=self.business_id,
            lang=self.lang,
            sort_type=Comment.SortType(self.sort_type),
            root_id=self.root_id,
            limit=self._MAX_SIZE,
            with_entities=[Comment.id, SCORE_COLS[self.sort_type]]
        )

        # 根据排序类型计算分数
        comment_scores = {
            str(comment.id): comment.get_score(self.sort_type)
            for comment in comments
        }

        # === 关键修正：无论有无数据都要 save ===
        self.save(comment_scores)
        self.expire(self._ttl)

        if comment_scores:
            # 保存不存在的评论缓存
            missing_caches = {}
            for comment in comments:
                comment_cache = CommentCache(comment.id)
                if not comment_cache.exists():
                    missing_caches[comment.id] = comment_cache

            # If we have missing comments, load them in a single query
            missing_comments = Comment.query.filter(Comment.id.in_(missing_caches.keys())).all()
            for missing_comment in missing_comments:
                missing_cache = missing_caches[missing_comment.id]
                missing_cache.save_comment(missing_comment)
        return True

    def paginate(self, *, last_score: float = None, last_id: int = None, limit=50) -> list[int]:
        """基于游标的分页查询
        
        Args:
            last_score: 上一页最后一条记录的分数
            last_id: 上一页最后一条记录的ID
            limit: 每页数量
        Returns:
            list: 评论ID列表
        """
        load_if_not_exists(self)

        if last_score is None:
            return [int(id_) for id_ in self.zrev_range(0, limit - 1)]
        
        last_id = str(last_id)
        
        # 其他页    
        # 多取2条记录用于处理相同分数的情况
        items = self.zrev_range_by_score(last_score, float('-inf'), 0, limit + 2, withscores=True)
        
        if not items:
            return []
            
        # 找到上一页最后一条记录的位置
        start_pos = 0
        if last_id:
            for i, (item_id, score) in enumerate(items):
                if item_id == last_id:
                    start_pos = i + 1
                    break
        
        # 返回从该位置开始的 limit 条记录
        return [int(item_id) for item_id, _ in items[start_pos:start_pos + limit]]

    def remove_comment(self, comment_id: int):
        """从缓存中删除指定评论
        
        Args:
            comment_id: 要删除的评论ID
        """
        # 这里不需要判断是否存在，因为 zrem 针对不存在的缓存不会报错
        self.zrem(str(comment_id))
        self.expire(self._ttl)

    def change_vote(self, comment_id: int, new_score: float) -> bool:
        """更新评论的投票分数
        Args:
            comment_id: 评论ID
            new_score: 新的热度分数
            
        Returns:
            bool: 是否仍在缓存中
        """
        # 检查评论是否在列表中
        load_if_not_exists(self)

        old_score = self.zscore(str(comment_id))
        if old_score is None:
            # 评论不在列表中，检查是否分数够高可以加入
            min_score = self.get_min_score()
            if new_score > min_score or self.zcard() < self._MAX_SIZE:
                self.zadd({str(comment_id): float(new_score)})
                self.expire(self._ttl)
                return True
            return False
            
        # 评论在列表中，判断新分数是否够高
        if new_score > self.get_min_score() or self.zrev_rank(str(comment_id)) < self._MAX_SIZE:
            # 更新分数
            self.zadd({str(comment_id): float(new_score)})
            self.expire(self._ttl)
            return True
        else:
            # 分数太低，从列表中移除
            self.zrem(str(comment_id))
            self.expire(self._ttl)
            return False
            
    def get_min_score(self) -> float:
        """获取当前列表中的最低分数"""
        load_if_not_exists(self)
        result = self.zrange(0, 0, withscores=True)
        return float(result[0][1]) if result else float('-inf')

    @classmethod
    def get_instances_from_comment(cls, comment: Comment):
        """根据评论对象获取对应的3个缓存列表：HOT、NEW、TOP"""
        if comment.root_id:
            # 回复列表
            return [cls(
                root_id=comment.root_id,
                sort_type=sort_type
            ) for sort_type in [Comment.SortType.TOP]]     # 回复列表目前只支持 TOP 排序
        else:
            # 评论列表
            return [cls(
                business=comment.business,
                business_id=comment.business_id,
                lang=comment.lang,
                sort_type=sort_type
            ) for sort_type in Comment.SortType]

    def add_comment(self, comment: Comment):
        """添加评论到列表缓存"""
        load_if_not_exists(self)
        min_score = self.get_min_score()
        score = comment.get_score(self.sort_type)
        added_count = 0
        if self.zcard() >= self._MAX_SIZE:
            if score > min_score:
                self.zrem_range(0, 0)  # 删除列表中最后一条
                added_count = self.zadd({str(comment.id): score})
            # else:
                # 分数低于最低分数，且缓存已满，丢弃
        else:
            added_count = self.zadd({str(comment.id): score})

        if added_count > 0:
            self.expire(self._ttl)
        return added_count

    def get_max_score_comment_id(self) -> int | None:
        """获取当前列表中分数最高的评论ID"""
        load_if_not_exists(self)
        highest_comments = self.zrange(0, 0, withscores=True)
        if highest_comments:
            return int(highest_comments[0][0])
        return None


class UserPendingCommentsCache(EmptyCollectionMixin, SetCache):
    """用户待审核评论缓存
    使用Set存储待审核的评论ID
    key格式: pending_comments:{user_id}
    """
    _ttl = 3600 * 24  # 24小时

    def __init__(self, user_id: int):
        self.user_id = user_id
        super().__init__(f"{user_id}")

    def reload(self):
        """重新加载用户的待审核评论"""
        pending_comments = db.session.query(Comment).filter_by(
            user_id=self.user_id,
            status=Comment.CommentStatus.CREATED
        ).order_by(desc(Comment.created_at)).all()

        comment_ids = set()
        for comment in pending_comments:
            comment_ids.add(comment.id)
            comment_cache = CommentCache(comment.id)
            if comment_cache.exists():
                continue
            comment_cache.save_comment(comment)

        self.save(comment_ids)
        self.expire(self._ttl)

    def add_comment(self, comment: Comment):
        """添加新评论到待审核列表

        Args:
            comment: 评论对象
        """
        self.sadd(str(comment.id))
        CommentCache(comment.id).save_comment(comment)

    def remove_comment(self, comment_id: int):
        """从待审核列表中删除评论
        Args:
            comment_id: 评论ID
        """
        self.srem(str(comment_id))

    def query_comments(
        self,
        *,
        business: str = None,
        business_id: str = None,
        lang: Language = None,
        root_id: int = None,
        sort_type: SortType = None,
    ) -> list[Comment]:
        load_if_not_exists(self)

        all_ids = self.read()
        all_comments = [comment for id in all_ids if (comment := CommentCache(int(id)).get_comment()) is not None]
        filtered_comments = []
        for comment in all_comments:
            # 按条件过滤出需要的评论
            if root_id:
                if comment.root_id == root_id:
                    filtered_comments.append(comment)
            else:
                if comment.business == business and comment.business_id == business_id and comment.lang == lang\
                        and comment.parent_id is None:
                    filtered_comments.append(comment)

        if sort_type:
            # 按照排序类型对待审核评论进行排序
            filtered_comments.sort(
                    key=lambda x: x.get_score(sort_type),
                    reverse=True
            )
        return filtered_comments


class ProcessCommentVoteCache(ExecutionCheckpointCache):
    """评论投票统计任务上次执行信息, evaluation_time为秒级时间戳"""
    pass


class ProcessCommentReportCache(ExecutionCheckpointCache):
    """评论举报统计任务上次执行信息, evaluation_time为秒级时间戳"""
    pass


class ProcessCommentCountCache(ExecutionCheckpointCache):
    """一级评论数统计任务上次执行信息, evaluation_time为秒级时间戳"""
    pass


class CommentCountCache(StringCache):
    """业务评论计数缓存
    key格式: comment_business_count:{business}:{business_id}:{lang}
    """
    _ttl: int = 3600 * 24 * 30  # 30天

    def __init__(self, business: str, business_id: str, lang: Language):
        self.business = business
        self.business_id = business_id
        self.lang = lang
        key = f"{business}:{business_id}:{lang.name}"
        super().__init__(key)

    def reload(self):
        """重新加载评论计数"""
        count = db.session.query(func.count(Comment.id)).filter_by(
            business=self.business,
            business_id=self.business_id,
            lang=self.lang,
            status=Comment.CommentStatus.PUBLISHED,
            root_id=None  # 只统计一级评论
        ).scalar()

        if count is not None:
            self.set(str(count))
            self.expire(self._ttl)

    def get_count(self) -> int:
        """获取评论数量"""
        load_if_not_exists(self)
        count = self.get()
        return int(count or 0)

    def incr(self, amount: int = 1) -> int:
        """增加评论数量"""
        load_if_not_exists(self)
        self.expire(self._ttl)
        return super().incr(amount)

    def decr(self, amount: int = 1) -> int:
        """减少评论数量"""
        load_if_not_exists(self)
        self.expire(self._ttl)
        return super().decr(amount)
