# -*- coding: utf-8 -*-
import json
from enum import Enum
from decimal import Decimal
from datetime import datetime
from typing import List, Union, Optional
from ..utils import current_timestamp, batch_iter
from .base import HashCache, HyperLogLogCache, SortedSetCache, StringCache


class TimedHashCache(HashCache):
    default_interval: int = 3600

    def __init__(self, pk: str, *, interval: int = None):
        super().__init__(pk)
        self._interval = interval or self.default_interval

    def count(self) -> int:
        self._clear_outdated()
        return self.hlen()

    def list_values(self) -> List[str]:
        self._clear_outdated()
        return list(self.value.values())

    def add_value(self, value: str = '',
                  date: Union[float, Decimal, datetime] = None) -> str:
        self._clear_outdated()
        return self._add_value(value, date)

    def add_value_if_fewer_than(self, count: int, value: str = '',
                                time: Union[float, int, datetime] = None
                                ) -> bool:
        if self.count() >= count:
            return False
        self._add_value(value, time)
        return True

    def _add_value(self, value, time):
        if time is None:
            time = current_timestamp()
        elif isinstance(time, datetime):
            time = time.timestamp()

        self.hset(str(time), value)
        self.expire(self._interval)

    def _clear_outdated(self):
        start = current_timestamp() - self._interval
        keys = [x for x in self.hkeys() if float(x) < start]
        if keys:
            self.hdel(*keys)


class FrequencyCache(TimedHashCache):

    def __init__(self, key: str, interval: int):
        super().__init__(key, interval=interval)
