# -*- coding: utf-8 -*-
import json
import time


from typing import (
    Any, Dict, List, Set, Mapping, Optional, Tuple,
)

from app import redis as _redis
import logging
from app.utils import camel_to_underscore

_logger = logging.getLogger(__name__)

# 空集合标识常量
EMPTY_COLLECTION_MARKER = "__EMPTY_COLLECTION__"


class BaseCacheMeta(type):
    _base_keys = set()

    def __new__(mcs, name, bases, dct):
        base_key = mcs._class_name_to_base_key(name)
        if base_key in mcs._base_keys:
            raise ValueError(f'duplicate redis key {base_key}')

        dct['_base_key'] = base_key
        mcs._base_keys.add(base_key)

        return super().__new__(mcs, name, bases, dct)

    @classmethod
    def _class_name_to_base_key(mcs, name: str) -> str:
        return camel_to_underscore(name.removeprefix('_').removesuffix('Cache'))


class _BaseCache(metaclass=BaseCacheMeta):
    _base_key: str  # set by metaclass
    redis = _redis

    def __init__(self, pk: Optional[str] = None):
        self._pk = pk
        self._key = self._pk_to_key(pk)

    def __repr__(self):
        return f'{type(self).__name__}({self._pk!r})'

    @classmethod
    def _pk_to_key(cls, pk: Optional[str]) -> str:
        if pk is None:
            return cls._base_key
        return f'{cls._base_key}:{pk}'

    @property
    def key(self) -> str:
        return self._key

    def read(self):
        raise NotImplementedError

    def save(self, value):
        raise NotImplementedError

    def delete(self) -> int:
        return self.redis.delete(self._key)

    def exists(self) -> bool:
        return bool(self.redis.exists(self._key))

    def expire(self, seconds: int) -> bool:
        return self.redis.expire(self._key, seconds)

    def ttl(self) -> int:
        return self.redis.ttl(self._key)

    def expireat(self, when: int) -> bool:
        return bool(self.redis.expireat(self._key, when))

    @property
    def value(self):
        return self.read()

    @value.setter
    def value(self, value):
        self.save(value)

    def reload(self):
        # 需要在业务缓存类中实现
        raise NotImplementedError

    def _get_empty_value(self) -> Any:
        """返回空值"""
        return None


class EmptyCollectionMixin:
    """空集合缓存穿透防护 Mixin
    
    为集合类型缓存提供对应的空集合标记缓存
    通过重写 read / save / delete / expire 方法实现自动加载和防穿透
    注意：
    1. 当集合元素被逐一删除，直至删空后会导致 redis 清理掉对应的键，本 Mixin 并不能防止这种情况下的缓存穿透，也无法将其标识为 Empty
    2. 当前没有覆写 hget/hmget/smembers/lpop/zrange 这类读取集合元素的方法，意味着如果业务缓存对象直接调用它们，并不会检测空集合标记，
       但这对于当前业务来说是够用了，建议不要在业务缓存之外，独立调用这些方法
    3. 本 Mixin 会通过覆写 expire 方法，来保持 marker 缓存和主缓存同步失效，以避免缓存不更新
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 确保有 _ttl 属性
        if not hasattr(self, '_ttl'):
            self._ttl = 3600
        
        self._empty_marker_key = f"empty_marker:{self.key}"

    def exists(self) -> bool:
        """重写 exists 方法，检查空集合标记"""
        return super().exists() or self._is_marked_as_empty()

    def _is_marked_as_empty(self) -> bool:
        """检查是否已标记为空集合"""
        return bool(self.redis.exists(self._empty_marker_key))
    
    def _mark_as_empty(self, ttl: int = None):
        """标记为空集合"""
        self.redis.set(self._empty_marker_key, "1", ex=ttl or self._ttl)
    
    def _clear_empty_marker(self):
        """清除空集合标记"""
        self.redis.delete(self._empty_marker_key)
    
    def read(self):
        """重写 read 方法，处理空集合标记"""
        if super().exists():
            # 如果缓存存在，直接返回
            return super().read()

        # 如果是空集合标记，返回对应的空集合
        if self._is_marked_as_empty():
            return self._get_empty_value()
        
        # 调用父类的 read 方法
        return super().read()

    def save(self, value):
        """重写 save 方法，当保存非空数据时清除空集合标记"""
        # 调用父类的 save 方法
        result = super().save(value)
        
        # 如果保存的是非空数据，清除空集合标记
        if value:  # 对于不同集合类型，value 可能是 dict/set/list 等
            self._clear_empty_marker()
        else:
            self._mark_as_empty()
            
        return result

    def delete(self) -> int:
        """重写 delete 方法，同时删除空集合标记"""
        # 删除真实缓存
        result = super().delete()
        # 删除空集合标记
        self._clear_empty_marker()
        return result
    
    def expire(self, seconds: int) -> bool:
        self.redis.expire(self._empty_marker_key, seconds)
        return super().expire(seconds)


class BytesCache(_BaseCache):

    def read(self) -> Optional[bytes]:
        return self.get()

    def save(self, value: bytes):
        self.set(value)

    def get(self) -> Optional[bytes]:
        return self.redis.get(self._key)

    def set(self, value: bytes, *, ex: int = None, nx: bool = None) -> Optional[bool]:
        return self.redis.set(self._key, value, ex=ex, nx=nx)

    def incr(self, amount: int = 1) -> int:
        return self.redis.incr(self._key, amount)


class StringCache(_BaseCache):

    def read(self) -> Optional[str]:
        return self.get()

    def save(self, value: str):
        self.set(value)

    def get(self) -> Optional[str]:
        value = self.redis.get(self._key)
        if value is not None:
            value = value.decode()
        return value

    def set(self, value: str, *, ex: int = None, nx: bool = None) -> Optional[bool]:
        return self.redis.set(self._key, value, ex=ex, nx=nx)

    def incr(self, amount: int = 1) -> int:
        return self.redis.incr(self._key, amount)
    
    def decr(self, amount: int = 1) -> int:
        return self.redis.decr(self._key, amount)


class BitsCache(_BaseCache):

    def read(self) -> int:
        return self.bit_count()

    # Not Implemented: save

    def get_bit(self, offset: int) -> bool:
        value = self.redis.getbit(self._key, offset)
        return bool(value)

    def set_bit(self, offset: int, value: bool) -> bool:
        """return: value before set_bit"""
        value = self.redis.setbit(self._key, offset, int(value))
        return bool(value)

    def bit_count(self) -> int:
        return self.redis.bitcount(self._key)


class HashCache(_BaseCache):

    def read(self) -> Dict[str, str]:
        return self.hgetall()

    def save(self, mapping: Mapping[str, str], delete=True):
        if delete:
            if not mapping:
                self.delete()
                return
            old_keys = set(self.hkeys())
            new_keys = set(str(key) for key in mapping.keys())
            if del_keys := old_keys - new_keys:
                self.hdel(*del_keys)
        self.hmset(mapping)

    def hlen(self) -> int:
        return self.redis.hlen(self._key)

    def hkeys(self) -> List[str]:
        return [key.decode() for key in self.redis.hkeys(self._key)]

    def hexists(self, key: str) -> bool:
        return self.redis.hexists(self._key, key)

    def hget(self, key: str) -> Optional[str]:
        value = self.redis.hget(self._key, key)
        if value is not None:
            value = value.decode()
        return value

    def hmget(self, keys: List[str]) -> List[str]:
        values = self.redis.hmget(self._key, keys)
        return [v.decode() if v is not None else None for v in values]

    def hmget_with_keys(self, keys: List[str]) -> List[Tuple[str, str]]:
        values = self.redis.hmget(self._key, keys)
        return [(key, value.decode()) for key, value in zip(keys, values) if value is not None]

    def hgetall(self) -> Dict[str, str]:
        mapping = self.redis.hgetall(self._key)
        return {k.decode(): v.decode() for k, v in mapping.items()}

    def hset(self, key: str, value: str) -> int:
        return self.redis.hset(self._key, key, value)

    def hmset(self, mapping: Mapping[str, str]) -> int:
        self.redis.hset(self._key, mapping=mapping)

    def hdel(self, *keys: str) -> int:
        return self.redis.hdel(self._key, *keys)

    def hincrby(self, key: str, amount: int) -> int:
        return self.redis.hincrby(self._key, key, amount)

    @classmethod
    def batch_read(cls, pks: List[str], fields: Optional[List[str]] = None) -> Dict[str, Dict[str, str]]:
        """批量获取多个主键对应的哈希表数据
        
        Args:
            pks: 主键列表
            fields: 要获取的字段列表，如果为 None 则获取所有字段
            
        Returns:
            Dict[str, Dict[str, str]]: 以主键为key，哈希表数据为value的字典
            例如: {
                "1": {"field1": "value1", "field2": "value2"},
                "2": {"field1": "value3", "field2": "value4"}
            }
        """
        if not pks:
            return {}
            
        pipe = cls.redis.pipeline()
        cache_keys = [cls._pk_to_key(pk) for pk in pks]
        
        if fields is None:
            # 获取所有字段
            for key in cache_keys:
                pipe.hgetall(key)
            
            results = pipe.execute()
            return {
                pk: {k.decode(): v.decode() for k, v in data.items()} 
                for pk, data in zip(pks, results) 
                if data  # 只返回非空的结果
            }
        else:
            # 只获取指定字段
            for key in cache_keys:
                pipe.hmget(key, fields)
                
            results = pipe.execute()
            return {
                pk: {field: value.decode() for field, value in zip(fields, values) if value is not None}
                for pk, values in zip(pks, results)
                if any(v is not None for v in values)  # 至少有一个字段有值
            }
    
    def _get_empty_value(self) -> Any:
        return {}


class SetCache(_BaseCache):

    def read(self) -> Set[str]:
        return self.smembers()

    def save(self, values: Set[str]):
        if not values:
            self.delete()
            return
        old_keys = self.smembers()
        new_keys = {str(x) for x in values}
        self.sadd(*values)
        if del_keys := (old_keys - new_keys):
            self.srem(*del_keys)

    def scard(self) -> int:
        return self.redis.scard(self._key)

    def smembers(self) -> Set[str]:
        return {v.decode() for v in self.redis.smembers(self._key)}

    def sismember(self, value: str) -> bool:
        return self.redis.sismember(self._key, value)

    def sadd(self, *values: str) -> int:
        return self.redis.sadd(self._key, *values)

    def srem(self, *values: str) -> int:
        return self.redis.srem(self._key, *values)

    def _get_empty_value(self) -> Any:
        return set()

class ListCache(_BaseCache):

    def read(self) -> List[str]:
        return self.lrange(0, -1)

    def save(self, values: List[str]):
        # TODO: atomic
        self.delete()
        if values:
            self.rpush(*values)

    def llen(self) -> int:
        return self.redis.llen(self._key)

    def lrange(self, start: int, end: int) -> List[str]:
        values = self.redis.lrange(self._key, start, end)
        return [v.decode() for v in values]

    def lpop(self) -> Optional[str]:
        value = self.redis.lpop(self._key)
        if value is not None:
            value = value.decode()
        return value

    def rpush(self, *values: str) -> int:
        return self.redis.rpush(self._key, *values)

    def lpop_batch(self, max_length: int = 1000) -> List[str]:
        values = []
        while len(values) < max_length:
            value = self.lpop()
            if value is None:
                break
            values.append(value)
        return values

    def _get_empty_value(self) -> Any:
        return []

class HyperLogLogCache(_BaseCache):

    def read(self) -> int:
        return self.pfcount()

    # Not Implemented: save

    def pfadd(self, *values: str) -> int:
        return self.redis.pfadd(self._key, *values)

    def pfcount(self) -> int:
        return self.redis.pfcount(self._key)

    def pfmerge(self, *other) -> int:
        return self.redis.pfmerge(self._key, *other)


class SortedSetCache(_BaseCache):

    def read(self) -> List[str]:
        return self.zrange(0, -1)
    
    def save(self, mapping: Mapping[str, float]):
        if not mapping:
            self.delete()
            return
        olds = set(self.read())
        news = set(str(x) for x in mapping.keys())
        self.zadd(mapping)
        if dels := olds - news:
            self.zrem(*dels)

    def zcard(self) -> int:
        return self.redis.zcard(self._key)
    
    def zadd(self, mapping: Mapping[str, float],  nx: bool = False,) -> int:
        return self.redis.zadd(self._key, mapping, nx=nx)

    def zscore(self, value: str) -> Optional[float]:
        return self.redis.zscore(self._key, value)
    
    def zrange(self, start: int, end: int, withscores=False) -> List[str] | List[Tuple[str, float]]:
        result = self.redis.zrange(self._key, start, end, withscores=withscores)
        if withscores:
            return [(v[0].decode(), v[1]) for v in result]
        return [v.decode() for v in result]
    
    def zrange_by_score(self, min: float, max: float) -> List[str]:
        return [v.decode() for v in self.redis.zrangebyscore(self._key, min, max)]

    def zrev_range(self, start: int, end: int, withscores: bool = False,) -> List[str] | List[Tuple[str, float]]:
        result = self.redis.zrevrange(self._key, start, end, withscores=withscores)
        if withscores:
            return [(v[0].decode(), v[1]) for v in result]
        return [v.decode() for v in result]

    def zrem(self, *values: str) -> int:
        return self.redis.zrem(self._key, *values)
    
    def zrem_range(self, start: int, end: int) -> int:
        return self.redis.zremrangebyrank(self._key, start, end)

    def zrev_range_by_score(self, max_score: float, min_score: float, start: int, num: int, withscores: bool = False) -> List[str] | List[Tuple[str, float]]:
        # 检查是否是空集合标识
        result = self.redis.zrevrangebyscore(self._key, max_score, min_score, start, num, withscores=withscores)
        if withscores:
            return [(v[0].decode(), v[1]) for v in result]
        return [v.decode() for v in result]

    def zrev_rank(self, value, with_score=False):
        return self.redis.zrevrank(self._key, value, with_score)
    
    def _get_empty_value(self) -> List[str]:
        return []


class ExecutionCheckpointCache(StringCache):
    """任务执行检查点缓存，记录上次执行时间，evaluation_time为秒级时间戳"""

    ttl = 86400 * 5  # 缺省 5天内都没有更新，则不再使用该缓存作为更新检查点，子类可以覆盖这个设定

    def __init__(self):
        super().__init__(None)

    def get_last_evaluation(self) -> int:
        if not (data := self.read()):
            return 0

        data = json.loads(data)
        return data.get('evaluation_time', 0)

    def save_evaluation_time(self, evaluation_time: int):
        data = {'evaluation_time': evaluation_time}
        self.set(json.dumps(data), ex=self.ttl)

