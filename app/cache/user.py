from collections import defaultdict
from enum import Enum
from app.cache.base import HashCache
from app.models import UserInfo


class UserInfoCache(HashCache):
    """用户信息缓存"""

    class InfoType(Enum):
        UserName = "user_name"
        AccountName = "account_name"
        Avatar = "avatar"
        SignedOff = "signed_off"

    SignedOffBizName = "user_sign_off"

    @staticmethod
    def format_hash_key(user_id: int, info_type: InfoType):
        return f"{user_id}-{info_type.value}"

    def set_user_info(self, user_id, user_name=None, account_name=None, avatar=None):
        if user_id is None:
            return

        user_info_map = {}
        if user_name is not None:
            user_info_map[self.format_hash_key(user_id, self.InfoType.UserName)] = user_name

        if account_name is not None:
            user_info_map[self.format_hash_key(user_id, self.InfoType.AccountName)] = account_name

        if avatar is not None:
            user_info_map[self.format_hash_key(user_id, self.InfoType.Avatar)] = avatar

        self.hmset(user_info_map)

    def get_user_info(self, user_ids: set[int], only: InfoType | None = None):
        if not user_ids:
            return {}

        if only is not None:
            info_types = [only]
        else:
            info_types = self.InfoType

        hash_keys = []
        for info_type in info_types:
            for user_id in user_ids:
                hash_keys.append(self.format_hash_key(user_id, info_type))

        res = self.hmget_with_keys(hash_keys)
        user_info_map = defaultdict(lambda: {
            self.SignedOffBizName: False
        })
        for k, v in res:
            user_id, info_type = k.split('-')
            if info_type == self.InfoType.SignedOff.value:
                info_type = self.SignedOffBizName
                if v == "1":
                    v = True

            user_info_map[int(user_id)][info_type] = v

        return user_info_map

    def set_signed_off(self, user_id, signed_off: bool):
        self.hset(self.format_hash_key(user_id, self.InfoType.SignedOff), str(int(signed_off)))

    def reload_all(self):
        """
        用于全量用户信息缓存丢失后的数据预热
        """
        offset = 0
        limit = 1000
        while (user_infos := UserInfo.query.with_entities(
            UserInfo.user_id,
            UserInfo.name,
            UserInfo.account_name,
            UserInfo.avatar,
        ).offset(offset * limit).limit(limit).all()):
            for user_info in user_infos:
                self.set_user_info(
                    user_info.user_id,
                    user_info.name,
                    user_info.account_name,
                    user_info.avatar
                )

            offset += 1
