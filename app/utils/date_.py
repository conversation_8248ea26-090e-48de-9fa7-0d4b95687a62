# -*- coding: utf-8 -*-
import time
from datetime import datetime, date, tzinfo, timezone, timedelta
from typing import Union

from dateutil import tz
from dateutil.parser import parse as parse_datetime
from dateutil.tz import UTC, gettz
from dateutil.relativedelta import relativedelta


def now(tz: tzinfo = None) -> datetime:
    return datetime.now(tz or UTC)


def today_datetime(tz: tzinfo = None) -> datetime:
    now_ = now(tz)
    return datetime(now_.year, now_.month, now_.day, tzinfo=UTC)


def today(tz: tzinfo = None) -> date:
    return now(tz).date()


def min_utc_datetime() -> datetime:
    min_datetime = datetime.min
    min_utc_dt = min_datetime.replace(tzinfo=UTC)
    return min_utc_dt


def convert_datetime(dt: datetime, date_field: str) -> datetime:
    fields = ["year", "month", 'day', 'hour', 'minute', 'second', 'microsecond']
    defaults = [None, 1, 1, 0, 0, 0, 0]
    default_dict = dict(zip(fields, defaults))
    if date_field not in fields:
        raise TypeError
    ins_dict = {}
    index = fields.index(date_field)
    for field in fields[:index+1]:
        ins_dict[field] = getattr(dt, field)
    for field in fields[index+1:]:
        ins_dict[field] = default_dict[field]
    ins_dict['tzinfo'] = UTC
    return datetime(
       **ins_dict
    )


def current_datetime(s_field: str):
    return convert_datetime(now(), s_field)


def current_timestamp(*, to_int: bool = False) -> Union[float, int]:
    timestamp = datetime.now().timestamp()
    if to_int:
        timestamp = int(timestamp)
    return timestamp


def timezone_to_offset(tz: Union[str, tzinfo]) -> int:
    if isinstance(tz, str):
        tz = gettz(tz)
    return int(tz.utcoffset(datetime.today()).total_seconds())


def date_to_datetime(value: date) -> datetime:
    return datetime(value.year, value.month, value.day, tzinfo=UTC)


def timestamp_to_datetime(timestamp: float, tz: tzinfo = None) -> datetime:
    return datetime.fromtimestamp(timestamp, tz or UTC)


def timestamp_to_date(timestamp: float, tz: tzinfo = None) -> date:
    return timestamp_to_datetime(timestamp, tz).date()


def str_to_datetime(text: str, tz: tzinfo = None) -> datetime:
    dt = parse_datetime(text)
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=UTC)
    if tz is not None:
        dt = dt.astimezone(tz)
    return dt


def datetime_to_str(dt: datetime, offset_minutes: int = 0,
                    fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=UTC)
    dt = dt.astimezone(timezone(timedelta(minutes=offset_minutes)))
    return dt.strftime(fmt)


def datetime_to_time(datetime):
    return int(time.mktime(datetime.timetuple()))


def today_timestamp_utc() -> int:
    _now = datetime.utcnow()
    return int(datetime(_now.year, _now.month, _now.day, tzinfo=UTC
                        ).timestamp())


def next_month(year: int, month: int, day: int = 1) -> date:
    return date(year, month, day) + relativedelta(months=1)


def last_month(year: int, month: int, day: int = 1) -> date:
    return date(year, month, day) + relativedelta(months=-1)


def last_month_range(dt: Union[datetime, date]) -> (date, date):
    last_month_first_day = last_month(dt.year, dt.month, 1)
    this_month_first_day = date(dt.year, dt.month, 1)
    last_month_end_day = this_month_first_day + relativedelta(days=-1)
    return last_month_first_day, last_month_end_day


def datetime_to_utc8_str(dt: datetime, fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=UTC)
    dt = dt.astimezone(tz.gettz("UTC+8"))
    return dt.strftime(fmt)


def this_month():
    today_ = today()
    return date(today_.year, today_.month, 1)


def yesterday():
    return today() - timedelta(days=1)


def datetime_to_ms(dt: datetime):
    return int(dt.timestamp() * 1000 // 1)


def date_to_ms(dt: date):
    c_dt = date_to_datetime(dt)
    return int(c_dt.timestamp() * 1000 // 1)


def utc_0_clock_to_beijing_0_clock(date_: datetime) -> datetime:
    return date_ - timedelta(hours=8)


def month_first_day(date_: datetime) -> datetime:
    return datetime(date_.year, date_.month, 1)
