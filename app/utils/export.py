from dataclasses import dataclass
import io
from typing import Any, Dict, List, Union, Tuple
from flask import Response, g, send_file
from openpyxl import Workbook
from app.common.constants import Language
from app.utils.date_ import now

def _get_field_value(item, field):
    if isinstance(item, dict):
        return item.get(field)
    return getattr(item, field)

@dataclass
class ExcelExporter:
    data_list: List[Dict[str, Any]] = None
    fields: List[str] = None,
    headers: List[str] = None,
    size: int = 1000000

    def _create_wb(self) -> Workbook:
        from app.utils import batch_iter

        data_list = self.data_list or []
        wb = Workbook(write_only=True)
        if not data_list and self.headers:
            ws = wb.create_sheet()
            ws.append(self.headers)
        for chunk_data in batch_iter(data_list, self.size):
            ws = wb.create_sheet()
            if self.headers:
                ws.append(self.headers)
            for item in chunk_data:
                ws.append([_get_field_value(item, k) for k in self.fields])
        return wb

    def export_streams(self) -> io.BytesIO:
        wb = self._create_wb()
        stream = io.BytesIO()
        wb.save(stream)
        stream.seek(0)
        return stream

    def export_file(self, filename: str = 'tmp'):
        wb = self._create_wb()
        wb.save(f'{filename}.xlsx')

def export_xlsx(filename: str, data_list: List[Dict[str, Any]],
                export_headers: Tuple[Dict[Union[Language, str], str], ...]
                ) -> Response:
    fields = [item["field"] for item in export_headers]
    headers = [item[Language.ZH_HANS_CN] for item in export_headers]
    stream = ExcelExporter(
        data_list=data_list or [],
        fields=fields,
        headers=headers
    ).export_streams()
    now_date = now().strftime("%Y%m%d")
    return send_file(
        stream,
        download_name=f'{now_date}_{filename}.xlsx',
        as_attachment=True
    )