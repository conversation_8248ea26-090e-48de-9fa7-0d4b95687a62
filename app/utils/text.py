import html
import re
from json import dumps as json_dumps
from functools import partial
import unicodedata
import emoji

from app.common.constants import Language
from app.utils.parser import JsonEncoder
import langid

from zhon.cedict import simplified, traditional


_RE_CAMEL = re.compile(r'((?<=[a-z0-9])[A-Z]|(?!^)(?<!_)[A-Z](?=[a-z]))')


def my_json_dumps(data, *args, **kwargs):
    return json_dumps(data, cls=JsonEncoder, *args, **kwargs)


compact_json_dumps = partial(my_json_dumps, separators=(',', ':'))


def camel_to_underscore(text: str) -> str:
    return _RE_CAMEL.sub(r'_\1', text).lower()


def chinese_classify(text):
    """
    """
    # 统计文本中繁体字的个数
    s_count = sum(c in simplified for c in text)
    t_count = sum(c in traditional for c in text)
    return 'zh_Hant' if t_count > s_count else 'zh_Hans'


# 语系分组定义
# 只包含未在 Language 中定义的语言代码
SLAVIC_LANGUAGES = {
    'bg',  # 保加利亚语
    'uk',  # 乌克兰语
    'sr',  # 塞尔维亚语
    'hr',  # 克罗地亚语
    'sl',  # 斯洛文尼亚语
    'mk',  # 马其顿语
    'bs',  # 波斯尼亚语
    'be'   # 白俄罗斯语
}  # 不包含 'ru'(俄语)

ROMANCE_LANGUAGES = {
    'ro',  # 罗马尼亚语
    'ca',  # 加泰罗尼亚语
    'gl',  # 加利西亚语
    'oc'   # 奥克语
}  # 不包含 'es'(西班牙语), 'fr'(法语), 'it'(意大利语), 'pt'(葡萄牙语)

GERMANIC_LANGUAGES = {
    'nl',  # 荷兰语
    'af',  # 南非语
    'da',  # 丹麦语
    'no',  # 挪威语
    'sv'   # 瑞典语
}  # 不包含 'de'(德语)

MALAY_LANGUAGES = {
    'ms',  # 马来语
    'jv'   # 爪哇语
}  # 不包含 'id'(印尼语)

INDO_IRANIC_LANGUAGES = {   # 印度-伊朗语族
    'ur',# 乌都语/乌尔都语
    'ps' # 普什图语
}

# 未定义语言的默认映射
LANGUAGE_FAMILY_FALLBACK = {
    'ru': SLAVIC_LANGUAGES,     # 斯拉夫语族映射到俄语
    'id': MALAY_LANGUAGES,      # 马来语族映射到印尼语
    'de': GERMANIC_LANGUAGES,   # 日耳曼语族映射到德语
    'es': ROMANCE_LANGUAGES,    # 罗曼语族映射到西班牙语
    'fa': INDO_IRANIC_LANGUAGES,    # 印伊语族映射到波斯语
}


def detect_lang(text: str) -> Language | None:
    """检测文本的语言
    Args:
        text: 文本内容
    Returns:
        Language: 语言枚举值
    """
    if not text:
        return None

    text_without_emoji = remove_emoji(text)
    if not text_without_emoji:
        return None

    # 使用langid库检测语言
    lang_code, confidence = langid.classify(text_without_emoji)

    # 处理中文
    if lang_code == 'zh':
        lang_code = chinese_classify(text_without_emoji)
    else:
        # 先尝试直接转换检测到的语言
        detected_lang = Language.from_lang2(lang_code)
        if detected_lang is not None:
            return detected_lang

        # 如果是未定义的语言，尝试进行语族映射
        for target_lang, related_langs in LANGUAGE_FAMILY_FALLBACK.items():
            if lang_code in related_langs:
                lang_code = target_lang
                break

    return Language.from_lang2(lang_code)

def remove_special_char(text: str, *check_funcs) -> str:
    """移除文本中的标点符号
    Args:
        text: 文本内容
    Returns:
        str: 移除标点符号后的文本
    """
    return ''.join(c for c in text if not any(check_func(c) for check_func in check_funcs))

def remove_emoji(text: str) -> str:
    """删除文本中的所有 emoji（官方库实现，支持所有标准 emoji）"""
    # emoji 无法用上面的单字符判断，所以用官方库实现
    # 有一个叫做 ‘语素簇’ 的概念，一个 emoji 可能由多个字符组成，比如 👌 由 ‘👌’ 和 ‘\u200d’ 组成
    # 还有这样的：👨‍👩‍👧‍👦，由 ‘👨’ 和 ‘\u200d’ 和 ‘👩’ 和 ‘\u200d’ 和 ‘👧’ 和 ‘\u200d’ 和 ‘👦’ 组成
    # 所以这里不能简单判断
    return emoji.replace_emoji(text, replace='')

def sanitize(content: str) -> str:
    """净化内容，防止XSS攻击
    Args:
        content: 原始内容
    Returns:
        str: 经过净化的内容
    """
    # 1. HTML转义，防止XSS攻击
    content = html.escape(content)

    # 2. 只移除特定的危险Unicode字符，保留表情符号
    dangerous_chars = ['\u0000', '\u200b', '\ufeff']
    for char in dangerous_chars:
        content = content.replace(char, '')

    # 3. 移除控制字符，但保留表情符号
    # Unicode表情符号范围：
    # Basic Emoticons: 1F600-1F64F
    # Misc Symbols: 2600-26FF
    # Dingbats: 2700-27BF
    # Transport & Map Symbols: 1F680-1F6FF
    # Supplemental Symbols and Pictographs: 1F900-1F9FF

    content = ''.join(c for c in content if ord(c) >= 32 or c == '\n' or is_emoji(c))
    return content


def clean_whitespace(content: str) -> str:
    """清理多余的空白和换行
    1. 把连续的换行，替换为一个换行
    2. 去掉首尾的换行和空白
    Args:
        content: 原始内容
    Returns:
        str: 经过清理的内容
    """
    content = content.replace("\r", "\n")
    content = re.sub(r'\n{3,}', '\n\n', content)
    content = content.strip()
    return content


def check_simple_text(text: str) -> bool:
    """检测文本是否为极简字符串（和语言无关）

    判断文本是否属于以下情况：
    1. 仅包含阿拉伯数字
    2. 仅包含表情
    3. 仅包含标点符号
    4. 仅包含@用户（或者它的转义形式 {{mention:username}}）
    5. 空字符(空格、制表符、换行符等)
    6. 以上情况的组合

    Args:
        text: 待检测的文本

    Returns:
        bool: 是否为极简字符串
    """
    if not text:
        return True

    # 如果全是 emoji 和标点符号则返回 True
    pure_text = remove_emoji(text)
    pure_text = remove_special_char(pure_text, is_punctuation)    
    if not pure_text or pure_text.isspace():
        return True

    # 只匹配 @用户形式、数字和空白字符
    pattern = r'^[\d\s]*((@\w*)[\d\s]*)*$'
    if re.match(pattern, pure_text):
        return True

    pattern = r'^[\d\s]*((\{\{mention:\w*}})[\d\s]*)*$'
    return bool(re.match(pattern, pure_text))


def is_punctuation(c: str) -> bool:
    """判断字符是否为标点符号或符号

    包括Unicode标点符号(P)和符号(S)类别，如$, @, #等
    """
    if c in ['@', '{', '}', ':']:
        # @, {, }, : 可能是用户名的一部分，不属于标点符号
        return False

    category = unicodedata.category(c)
    # P开头的是标点符号，S开头的是符号(包括货币符号、数学符号等)
    return category.startswith('P') or category.startswith('S')


