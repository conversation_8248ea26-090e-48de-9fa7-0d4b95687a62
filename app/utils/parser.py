import marshmallow as ma
# -*- coding: utf-8 -*-
import json
from datetime import datetime, date
from decimal import Decimal
from enum import Enum, EnumMeta
from types import MappingProxyType

from flask.json.provider import DefaultJ<PERSON>NProvider
# from flask_babel.speaklater import LazyString
from dateutil.tz import UTC
import marshmallow as ma


class JsonEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, set):
            return list(obj)
        if isinstance(obj, datetime):
            return int(obj.timestamp())
        if isinstance(obj, date):
            dt = datetime(obj.year, obj.month, obj.day, tzinfo=UTC)
            return int(dt.timestamp())
        if isinstance(obj, Decimal):
            return f'{obj.normalize():f}'
        if isinstance(obj, MappingProxyType):
            return dict(obj)
        if isinstance(obj, Enum):
            return obj.value
        if isinstance(obj, EnumMeta):
            return {e.name: e.value for e in obj}
        # if isinstance(obj, LazyString):
        #     return str(obj)
        return DefaultJSONProvider.default(obj)


# copy from webargs v6.1.1, v7+ remove this func, but we used
def dict2schema(dct, *, schema_class=ma.Schema):
    """Generate a `marshmallow.Schema` class given a dictionary of
    `Fields <marshmallow.fields.Field>`.
    """
    if hasattr(schema_class, "from_dict"):  # marshmallow 3
        return schema_class.from_dict(dct)
    attrs = dct.copy()

    class Meta:
        strict = True

    attrs["Meta"] = Meta
    return type("", (schema_class,), attrs)

