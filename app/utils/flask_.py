# -*- coding: utf-8 -*-

from functools import wraps
from flask import Flask
# noinspection PyProtectedMember
from flask.ctx import AppContext, has_app_context
from flask.globals import _cv_app


def require_app_context(func=None, *, app: Flask = None):
    def dec(_func):
        @wraps(_func)
        def wrapper(*args, **kwargs):
            nonlocal app
            if has_app_context():
                return _func(*args, **kwargs)
            if app is None:
                from app import app
            with app.app_context():
                return _func(*args, **kwargs)
        return wrapper

    if func is not None:
        return dec(func)
    return dec


def copy_current_app_context(func):
    _app_ctx = _cv_app.get()

    @wraps(func)
    def wrapper(*args, **kwargs):
        with copy_app_context(_app_ctx):
            return func(*args, **kwargs)
    return wrapper


def copy_app_context(ctx: AppContext) -> AppContext:
    new_ctx = ctx.app.app_context()
    new_ctx.g = ctx.g
    new_ctx.url_adapter = ctx.url_adapter

    return new_ctx


def auto_close_db_session(func):
    """when func runs in greenlet or not in request context,
       it must be decorated with this decorator"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        from app.models import db
        try:
            return func(*args, **kwargs)
        finally:
            db.session.remove()
    return wrapper
