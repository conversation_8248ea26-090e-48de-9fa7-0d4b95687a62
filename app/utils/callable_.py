# -*- coding: utf-8 -*-

from functools import partial
from types import FunctionType, MethodType
from typing import Dict, Iterable, Union, Any
from inspect import getfullargspec
from .text import compact_json_dumps


def func_to_str(func: Union[FunctionType, MethodType]):
    return f'{func.__module__}.{func.__qualname__}'


def func_args_to_str(func: Union[FunctionType, MethodType],
                     args: tuple = (),
                     kwargs: Dict[str, Any] = None,
                     *,
                     only: Iterable[Union[int, str]] = None
                     ) -> str:
    # noinspection PyTypeChecker
    """
    Same arguments can be passed in different ways to a function.
    Suppose we have defined a function:
        def f(a, b=0):
            pass
    Then the following calls will have the same effect although the arguments
      are different in each call:
        f(1, 0)
        f(1)
        f(1, b=0)
        f(a=1)
        f(a=1, b=0)
    That's why simply using `args` and `kwargs` in their original forms to
      generate a unique key can cause problems to solve which we must normalise
      the arguments by classifying them into keyword and non-keyword categories
      as well as sorting dictionaries by their keys.
    Further more, methods that belong to classes are supported as well, where
      `A().f(0)` and `A.f(A(), 0)` are desirably converted to the same key.

    Usage example for methods belonging to classes:
    >>> class A:
    >>>     def __init__(self, x):
    >>>         self.x = x
    >>>     def f(self, a):
    >>>         pass
    >>>
    >>> func_args_to_str(A(0).f, (1,))  # '[1],{}'
    >>> func_args_to_str(A.f, (A(0), 1))  # '[1],{}'
    """
    if kwargs is None:
        kwargs = {}

    is_bound_method = isinstance(func, MethodType)

    func_arg_spec = getfullargspec(func)
    as_args = func_arg_spec.args
    as_defaults = func_arg_spec.defaults or ()

    idx_only, kw_only = set(), set()
    as_n2i = {n: i for i, n in enumerate(as_args)}
    for o in only or ():
        if isinstance(o, int):
            idx_only.add(o)
        else:
            i = as_n2i.get(o)
            if i is not None:
                idx_only.add(i)
            else:
                kw_only.add(o)

    idx_args = [None] * len(as_args)
    idx_args[len(as_args)-len(as_defaults):] = as_defaults
    idx_args[is_bound_method:len(args)+is_bound_method] = args

    kw_args = dict(func_arg_spec.kwonlydefaults or ())
    for k, v in kwargs.items():
        i = as_n2i.get(k)
        if i is None:
            kw_args[k] = v
        else:
            idx_args[i] = v

    if only:
        idx_args = [v for i, v in enumerate(idx_args) if i in idx_only]
        kw_args = {k: v for k, v in kw_args.items() if k in kw_only}

    if ((is_bound_method or (as_args and as_args[0] in ('self', 'cls', 'mcs')))
            and (not idx_only or 0 in idx_only)
            and idx_args):
        idx_args.pop(0)

    return ','.join(map(partial(compact_json_dumps, sort_keys=True),
                        [idx_args, kw_args]))
