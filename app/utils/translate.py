from bs4 import BeautifulSoup
from app.common.constants import Language
from app.utils.iterable import batch_iter
from app.utils.http_client import RESTClient


class BaseTranslate(object):

    def __init__(self, source_lang: Language, target_lang: Language):
        self.source_lang = source_lang
        self.target_lang = target_lang

    def translate(self, text: str) -> str:
        raise NotImplementedError

    def translate_html(self, html: str) -> str:
        raise NotImplementedError


class GoogleTranslate(BaseTranslate):
    HOST = 'https://translation.googleapis.com'
    URL = '/language/translate/v2'

    LANG_MAP = {
        Language.EN_US: 'en',
        Language.ZH_HANS_CN: 'zh-CN',
        Language.ZH_HANT_HK: 'zh-TW',
        Language.JA_JP: 'ja',
        Language.RU_KZ: 'ru',
        Language.KO_KP: 'ko',
        Language.ID_ID: 'id',
        Language.ES_ES: 'es',
        Language.FA_IR: 'fa',
        Language.TR_TR: 'tr',
        Language.VI_VN: 'vi',
        Language.AR_AE: 'ar',
        Language.FR_FR: 'fr',
        Language.PT_PT: 'pt',
        Language.DE_DE: 'de',
        Language.TH_TH: 'th',
        Language.IT_IT: 'it',
        Language.PL_PL: 'pl',
    }

    @staticmethod
    def _ensure_language_enum(lang) -> Language:
        """确保输入是 Language 枚举类型"""
        return lang if isinstance(lang, Language) else Language[lang]

    def __init__(self, key: str, source_lang: Language, target_lang: Language):
        source_lang = self._ensure_language_enum(source_lang)
        target_lang = self._ensure_language_enum(target_lang)
        super().__init__(source_lang, target_lang)
        self.key = key
        self.client = RESTClient(self.HOST)

    def translate(self, text: str) -> str:
        lines = [line for line in text.splitlines() if line != '']
        result = []
        for text_iter in batch_iter(lines, 100):
            data = {
                'q': text_iter,
                'source': self.LANG_MAP[self.source_lang],
                'target': self.LANG_MAP[self.target_lang],
                'key': self.key,
            }
            result.extend([
                translation['translatedText']
                for translation in self.client.post(self.URL, data=data).get('data', {}).get('translations', [])
            ])
        return '\n'.join(result)

    def translate_html(self, html: str) -> str:
        soup = BeautifulSoup(html, 'html.parser')
        text = '\n'.join([content.__str__() for content in soup.contents])
        return self.translate(text)
