# -*- coding: utf-8 -*-

from functools import wraps
import logging
from logging import (
    get<PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
)
from traceback import format_exc
from .callable_ import func_to_str

import socket
import json
import zlib

_logger = getLogger(__name__)


class UDPStringHandler(Handler):

    def __init__(self, host: str, port: int):
        super().__init__()
        self._address = (host, port)
        self._sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    def emit(self, record: LogRecord):
        msg = self.format(record)
        if isinstance(msg, str):
            msg = msg.encode('utf-8')
        # noinspection PyBroadException
        try:
            self._sock.sendto(msg, self._address)
        except Exception:
            pass


# see https://github.com/severb/graypy/blob/master/graypy/handler.py
class GELFUDPHandler(Handler):

    # whitelist fields for `extra` when using `logger.error("", extra={})`
    EXTRA_FIELDS = (
        "project",
        "service",
        "module",
        "method",
        "path",
        "task",
        "consume",
        "request_path",
        "response_size",
    )

    SYSLOG_LEVELS = {
        logging.CRITICAL: 2,
        logging.ERROR: 3,
        logging.WARNING: 4,
        logging.INFO: 6,
        logging.DEBUG: 7
    }

    UDP_MAX_LENGTH = 60000

    def __init__(self, host: str, port: int):
        super().__init__()
        self._address = (host, port)
        self._sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self._compress = True

    def emit(self, record: LogRecord):
        try:
            msg = self.make(record)
            self._sock.sendto(msg, self._address)
        except Exception:
            pass

    def make(self, record: LogRecord) -> bytes:
        gelf = {
            "version": "1.0",
            "level": self.SYSLOG_LEVELS.get(record.levelno, record.levelno),
            "level_name": record.levelname,
            "short_message": self.format(record)[:self.UDP_MAX_LENGTH],
            "file": record.pathname,
            "line": record.lineno
        }
        for key, value in record.__dict__.items():
            if key in self.EXTRA_FIELDS:
                gelf["_%s" % key] = value

        packed = json.dumps(gelf, separators=",:")
        packed = packed.encode('utf-8')
        pickle = zlib.compress(packed) if self._compress else packed
        return pickle


def log_call(func=None, *,
             logger: Logger = None,
             log_result: bool = True,
             log_exception: bool = True):
    def dec(_func):
        func_name = func_to_str(_func)

        @wraps(_func)
        def wrapper(*args, **kwargs):
            nonlocal logger, func_name
            if logger is None:
                logger = _logger

            logger.info('%s [args]: %s %s', func_name, args, kwargs)

            try:
                result = _func(*args, **kwargs)
            except Exception:
                if log_exception:
                    logger.error('%s [error]: %s', func_name, format_exc())
                raise
            if log_result:
                logger.info('%s [result]: %s', func_name, result)
            return result

        return wrapper

    if func is not None:
        return dec(func)

    return dec