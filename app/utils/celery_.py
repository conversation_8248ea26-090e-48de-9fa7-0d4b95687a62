# -*- coding: utf-8 -*-

from types import FunctionType
from typing import Union

from celery.schedules import crontab

from app.utils import func_to_str


def scheduled(schedule: Union[int, crontab], *, queue: str = None):
    def dec(func):
        nonlocal queue
        _task = celery_task(func, queue=queue)

        from app.config.celery import beat_schedule
        _task_name = func_to_str(func)
        beat_schedule[_task_name] = {
            'task': _task_name,
            'schedule': schedule
        }
        return _task

    return dec


def celery_task(func=None, *, queue: str = None):
    """
    Usage:
        @task
        def bla_bla_bla():
          pass

        @task(queue='some_queue')
        def bla_bla_bla():
          pass
    """

    def dec(_func):
        if type(_func) is not FunctionType:
            raise TypeError(f'only functions are supported, '
                            f'but {_func!r} is a {type(_func)}')

        nonlocal queue
        if queue:
            _route_function(_func, queue)

        from app import celery
        return celery.task(_func, typing=False)

    if func is not None:
        return dec(func)

    return dec


def _route_prefix(prefix: str, queue: str):
    from app.config.celery import router, celery_queues

    router.set_queue_for_prefix(prefix, queue)
    if queue not in celery_queues:
        celery_queues[queue] = dict(
            exchange=queue,
            exchange_type='direct',
            binding_key=queue
        )


def _route_function(func: FunctionType, queue: str):
    _route_prefix(func_to_str(func), queue)


def route_module_to_celery_queue(module_name: str, queue: str):
    _route_prefix(module_name, queue)
