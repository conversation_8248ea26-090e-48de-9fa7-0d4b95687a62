# -*- coding: utf-8 -*-

from pkgutil import walk_packages
from importlib import import_module
from logging import Logger
from traceback import format_exc
from typing import Iterable, Callable, Any

import sys
import inspect


class NamedObject:

    """Like `x = object()`, but now `x` has a name."""

    def __init__(self, name: str, *, is_null: bool = False):
        self._name = name
        self._is_null = is_null

    def __repr__(self):
        return f'<{self._name}>'

    def __bool__(self):
        return not self._is_null


class WhyNot:

    """Useful when you'd like to attach a reason to a `bool`."""

    def __init__(self, v: bool, reason: Any = None):
        self._v = v
        self._reason = reason

    def __bool__(self):
        return self._v

    @property
    def reason(self):
        return self._reason


class DefaultDictWithArg(dict):

    def __init__(self, value_getter: Callable, *, expand_args: bool = False):
        super().__init__()
        self._value_getter = value_getter
        self._expand_args = expand_args

    def __repr__(self):
        return f'{type(self).__name__}' \
               f'({super().__repr__()}, value_getter={self._value_getter})'

    def __missing__(self, key):
        get = self._value_getter
        self[key] = value = get(key) if not self._expand_args else get(*key)
        return value


# Thanks to:
# https://docs.djangoproject.com/en/3.0/_modules/django/utils/decorators/
class _ClassProperty:

    def __init__(self, method=None):
        self.fget = method

    def __get__(self, instance, cls=None):
        return self.fget(cls)

    def getter(self, method):
        self.fget = method
        return self


# noinspection SpellCheckingInspection
classproperty = _ClassProperty


def auto_import(base_mod_name: str,
                *,
                logger: Logger = None,
                excluded: Iterable[str] = None,
                recursive: bool = False,
                delete_me: bool = False):
    """
    Usage:
        auto_import(__name__)
    """
    excluded = set(excluded) if excluded is not None else set()

    base_mod = sys.modules[base_mod_name]
    namespace = vars(base_mod)

    for _, mod_name, is_pkg in walk_packages(base_mod.__path__):
        if mod_name in excluded:
            continue

        # noinspection PyBroadException
        try:
            mod = import_module(f'{base_mod_name}.{mod_name}')
        except Exception:
            if logger is not None:
                logger.error('\n'.join([
                    f'Failed to load module {mod_name!r} due to:',
                    format_exc()
                ]))
            continue
        namespace[mod_name] = mod

        if is_pkg and recursive:
            auto_import(mod.__name__, logger=logger, recursive=True)

    if delete_me:  # cleaning up pollution
        this_method_name = inspect.currentframe().f_code.co_name
        namespace.pop(this_method_name, None)
