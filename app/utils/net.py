# -*- coding: utf-8 -*-

from urllib.parse import urlparse, quote, urlencode, parse_qs, urlunparse
from logging import getLogger

import re

_logger = getLogger(__name__)

_RE_EMAIL_USER = re.compile(r"^[-+\w\.]+$")
_RE_DOMAIN = re.compile(r"^[-\w]+(\.[-\w]+)+$")


def url_join(base: str, api: str, **params):
    slash = '/'
    parts = [base]
    if not base.endswith(slash):
        if not api.startswith(slash):
            parts.append(slash)
    elif api.startswith(slash):
        api = api[1:]
    parts.append(api)

    def format_param(_k, _v):
        if isinstance(_v, bytes):
            _logger.warning(f'param value should not be bytes: {_k}={_v!r}')
            _v = _v.decode()
        return f'{_k}={quote(str(_v))}'

    if params:
        parts.append('?')
        parts.append('&'.join(format_param(k, v) for k, v in params.items()))

    return ''.join(parts)


def add_query_param(url: str, param_name: str, param_value: str) -> str:
    # 解析 URL
    parsed_url = urlparse(url)
    # 获取原始 query 参数
    query_params = parse_qs(parsed_url.query)
    # 添加或更新指定的参数
    query_params[param_name] = [param_value]

    # 重新构建 query 字符串
    encoded_query = urlencode(query_params, doseq=True)

    # 构建新的 URL
    new_url = urlunparse(
        (parsed_url.scheme, parsed_url.netloc, parsed_url.path, parsed_url.params, encoded_query, parsed_url.fragment))

    return new_url


def validate_url(url: str) -> bool:
    if url.strip() != url:
        return False
    parsed = urlparse(url)
    if not parsed.netloc:
        return False
    return True


def get_url_base(url: str) -> str:
    url = url.strip()
    parsed = urlparse(url)
    return f'{parsed.scheme}://{parsed.netloc}'


def validate_email(email: str) -> bool:
    if not email or email.count('@') != 1 or len(email) > 64:
        return False
    user_part, domain_part = email.split('@')
    return _RE_EMAIL_USER.match(user_part) and _RE_DOMAIN.match(domain_part)
