# -*- coding: utf-8 -*-
from functools import wraps
from collections import deque
from enum import EnumMeta
from typing import List, Iterable, Iterator, Callable, Generator, Any, NamedTuple, TypeVar, Dict, \
    Optional

from gevent.pool import Pool
from gevent import spawn

from .flask_ import copy_current_app_context, auto_close_db_session
from flask_sqlalchemy.query import Query

T = TypeVar('T')


def batch_iter(iterable: Iterable[T], n: int
               ) -> Generator[List[T], None, None]:
    if n <= 0:
        raise ValueError('`n` must be greater than 0')

    batch = []
    for x in iterable:
        if len(batch) >= n:
            yield batch
            batch = []
        batch.append(x)
    if batch:
        yield batch


def exhaust(iterator: Iterator):
    # This is more space-efficient than `list(iterator)`
    deque(iterator, maxlen=0)


def g_map(func: Callable, *iterables: Iterable,
          size: int = None, ordered: bool = False, fail_safe: Any = None
          ) -> list:
    if fail_safe is not None:
        @wraps(func)
        def _func(*args, **kwargs):
            # noinspection PyBroadException
            try:
                return func(*args, **kwargs)
            except Exception:
                return fail_safe() if callable(fail_safe) else fail_safe
    else:
        _func = func

    _func = auto_close_db_session(_func)
    _func = copy_current_app_context(_func)

    pool = Pool(size)
    return list(
        (pool.imap if ordered else pool.imap_unordered)(_func, *iterables))


def spawn_greenlet(func, *args, **kwargs):
    func = auto_close_db_session(func)
    func = copy_current_app_context(func)
    return spawn(func, *args, **kwargs)


def list_enum_names(enum_class: EnumMeta) -> List[str]:
    return [e.name for e in enum_class]


def list_enum_values(enum_class: EnumMeta) -> List[str]:
    return [e.value for e in enum_class]


def group_by(func: Callable[[T], Any], iterable: Iterable[T]
             ) -> Dict[Any, List[T]]:
    result = {}
    for t in iterable:
        key = func(t)
        if not (rs := result.get(key)):
            rs = result[key] = []
        rs.append(t)
    return result


def first(func: Callable[[T], bool], iterable: Iterable[T]) -> Optional[T]:
    for t in iterable:
        if func(t):
            return t
    return None

class Pagination(NamedTuple):
    items: List[T]
    has_next: bool
    total: int


def pagination(query: Query, limit: int, page: int | None = None) -> Pagination:
    # 如果page未传入，则Pagination.total 为查询结果集的总数
    if page:
        result = query.paginate(page=page, per_page=limit + 1)
        has_next = len(result.items) > limit
        return Pagination(query.items[:limit], query.has_next, query.total)
    else:
        query = query.limit(limit + 1)
        items = query.all()
        has_next = len(items) > limit
        return Pagination(items[:limit], has_next, len(items))
