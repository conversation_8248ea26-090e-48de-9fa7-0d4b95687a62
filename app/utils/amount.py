# -*- coding: utf-8 -*-
from decimal import Decimal, ROUND_DOWN, Context
from typing import Union

AmountType = Union[Decimal, str, int]

def amount_to_str(amount: AmountType, decimals: int = None,
                  rounding: str = ROUND_DOWN, *,
                  with_separator: bool = False) -> str:
    amount = Decimal(amount)
    if decimals is not None:
        amount = quantize_amount(amount, decimals, rounding)
    return f'{amount.normalize():{",f" if with_separator else "f"}}'

def quantize_amount(amount: AmountType, decimals: int,
                    rounding: str = ROUND_DOWN,
                    *, precision: int = None
                    ) -> Decimal:
    context = Context(prec=precision) if precision is not None else None
    return Decimal(amount).quantize(Decimal(10) ** -decimals, rounding,
                                    context=context)

