from .base import ErrorWithResponseCode
from app.common.constants import TRANSLATION_ERROR, Language


class CommonError(ErrorWithResponseCode):

    response_code = 1
    message_template = '错误'


class InvalidArgument(ErrorWithResponseCode):

    response_code = 2
    message_template = '参数错误'


class InvalidAccount(ErrorWithResponseCode):

    response_code = 33
    message_template = '错误账号'


class ServiceUnavailable(ErrorWithResponseCode):

    response_code = 35
    message_template = '服务不可用'


class RecordNotFound(ErrorWithResponseCode):

    response_code = 200


class FrequencyExceeded(ErrorWithResponseCode):

    response_code = 213
    message_template = "操作太频繁"


class CommentUserBanned(ErrorWithResponseCode):

    response_code = 7001
    message_template = "无法发表评论，你被禁言，{banned_date}后可发表评论"


class CommentUserBannedForever(ErrorWithResponseCode):

    response_code = 7002
    message_template = '无法发表评论，你被禁言'


class CommentReplyUserBanned(ErrorWithResponseCode):

    response_code = 7003
    message_template = "无法发表回复，你被禁言，{banned_date}后可发表回复"


class CommentReplyUserBannedForever(ErrorWithResponseCode):

    response_code = 7004
    message_template = '无法发表回复，你被禁言'


class CommentDisabled(ErrorWithResponseCode):

    response_code = 7005
    message_template = '当前内容已违规，无法进行互动'


class DataNotReady(ErrorWithResponseCode):
    response_code = 95
    message_template = "Data Not Ready"


class TranslationError(ErrorWithResponseCode):

    response_code = 7006
    message_template = "翻译失败"


class CommentNotFound(ErrorWithResponseCode):

    response_code = 7007
    message_template = "当前内容不存在"


class CommentDeleted(ErrorWithResponseCode):

    response_code = 7008
    message_template = "当前内容已删除"


class UserNotAgreeCommentTOS(ErrorWithResponseCode):
    # 用户未签署协议
    response_code = 7009


class CommentLangMismatch(ErrorWithResponseCode):
    # 评论语言和语区不匹配
    response_code = 7010
    message_template = "英语频道仅可发表英文内容，无法发表其他语言内容。"


class CommentDuplicate(ErrorWithResponseCode):
    # 评论内容重复
    response_code = 7012
    message_template = "内容相似啦，试试换个表达方式吧～"
