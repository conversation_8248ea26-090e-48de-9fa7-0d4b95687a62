# -*- coding: utf-8 -*-
from enum import Enum
from typing import <PERSON><PERSON><PERSON>
from logging import getLogger
from flask_sqlalchemy import SQLAlchemy as _SQLAlchemy
from flask_sqlalchemy.model import Model
from sqlalchemy import (
    TypeDecorator,
    types,
    Column,
    Integer,
    func,
    Enum as FieldEnum,
    DECIMAL,
)
from sqlalchemy.dialects.postgresql import TIMESTAMP, TSVECTOR
from sqlalchemy.orm import Session
from dateutil.tz import UTC
import json
from sqlalchemy import Integer, Float, Boolean, DateTime, Date, String, JSON
from datetime import datetime


from ..utils import now

_logger = getLogger(__name__)


# noinspection PyAbstractClass
class TimeStampUTC(TypeDecorator):

    impl = TIMESTAMP
    cache_ok = True

    def process_result_value(self, value, dialect):
        if value is None:
            return None
        return value.replace(tzinfo=UTC)


# noinspection PyAbstractClass
class StringEnum(TypeDecorator):
    impl = types.Enum

    cache_ok = True

    limit_length = 64

    def __init__(self, *args, **kwargs):
        kwargs.update(native_enum=False, length=self.limit_length)
        super(StringEnum, self).__init__(*args, **kwargs)

    def process_bind_param(self, value: str | Enum, dialect):
        if value is None:
            # Keep the same as Enum._enum_init._valid_lookup
            return value

        if isinstance(value, str):
            enum_names = [v.name for v in self.enum_class]
            if value not in enum_names:
                raise ValueError
        elif isinstance(value, self.enum_class):
            value = value.name
        else:
            raise ValueError

        self._validate_length(value)
        return value

    def _validate_length(self, value):
        if len(value) > self.limit_length:
            raise ValueError(f'Expected length is {self.limit_length}')


class FullTextSearch(TypeDecorator):
    impl = TSVECTOR
    cache_ok = True

    def process_bind_param(self, value, dialect):
        if value is None:
            return None

        if isinstance(value, str):
            return value
        elif isinstance(value, list):
            return " ".join(value)
        else:
            raise ValueError

    # def bind_expression(self, bindparam):
    #     return CustomWrap(bindparam, func.to_tsvector)

    def match(self, query_string):
        return super().match(func.to_tsquery(query_string))


T = TypeVar('T', bound=Model)


class SQLAlchemy(_SQLAlchemy):
    DECIMAL_AMOUNT = DECIMAL(precision=26, scale=8)  # 此类型用于加密货币(资产、交易量、手续费等)
    DECIMAL_PRICE = DECIMAL(precision=26, scale=12)  # 只用于价格
    POSTGRESQL_TIMESTAMP_6 = TimeStampUTC(timezone=True, precision=6)
    StringEnum = StringEnum
    FullTextSearch = FullTextSearch

    session: Session

    def session_add_and_commit(self, obj: T) -> T:
        self.session.add(obj)
        self.session.commit()
        return obj

    def session_add_and_flush(self, obj: T) -> T:
        self.session.add(obj)
        self.session.flush()
        return obj


db = SQLAlchemy()


def row_to_dict(row, *,
                with_hook: bool = True,
                enum_to_name: bool = False, value_to_str: bool = False):
    result = {(name := col.name):
              (v.name
               if isinstance(v := getattr(row, name), Enum) and enum_to_name
               else _to_str(col, v) if value_to_str else v)
              for col in type(row).__table__.columns}

    if with_hook:
        hook = getattr(row, '_row_to_dict_hook_', None)
        if hook is not None:
            # noinspection PyBroadException
            try:
                hook(result)
            except Exception:
                pass

    return result


def _to_str(col, v) -> dict:
    result = {}
    col_type = col.type
        
    if isinstance(col_type, (DateTime, Date, TimeStampUTC)):
        return int(v.timestamp())
    elif isinstance(col_type, JSON):
        return json.dumps(v)
    else:
        return str(v)


def dict_to_row(data: dict, row_class, *,
                with_hook: bool = True,
                name_to_enum: bool = False):
    """Convert a dictionary to a SQLAlchemy model instance.
    
    Args:
        data: Dictionary containing the data
        row_class: The SQLAlchemy model class to instantiate
        with_hook: Whether to call the _dict_to_row_hook_ if it exists
        name_to_enum: Whether to convert string enum names back to enum values
    
    Returns:
        An instance of row_class populated with the data
    """
    
    row = row_class()
    
    for col in row_class.__table__.columns:
        name = col.name
        if name not in data:
            continue
        
        value = data[name]
        if value is None or value == 'None':
            continue

        col_type = col.type
        # Handle enum types
        if name_to_enum and isinstance(col_type, (FieldEnum, StringEnum)):
            if '.' in value:
                # 临时性处理，这是因为之前的一个bug,导致缓存中记录的不是枚举值，而是枚举类的全名，比如 CommentStatus.PUBLISHED
                module_name, enum_name = value.rsplit('.', 1)
                value = col_type.enum_class[enum_name]
            else:
                value = col_type.enum_class[value]
        # Handle basic types
        elif isinstance(col_type, (Integer, Float)):
            value = col_type.python_type(value)
        elif isinstance(col_type, Boolean):
            if isinstance(value, str):
                value = value.lower() in ('true', '1', 'yes', 'on')
            else:
                value = bool(value)
        elif isinstance(col_type, (DateTime, Date, TimeStampUTC)):
            if isinstance(value, int):
                value = value
            elif isinstance(value, float):
                value = int(value)
            elif isinstance(value, str):
                try:
                    value = int(value)
                except ValueError:
                    value = int(datetime.fromisoformat(value).replace(tzinfo=UTC).timestamp())
        elif isinstance(col_type, JSON):
            if isinstance(value, str):
                value = json.loads(value)
        elif isinstance(col_type, String):
            value = str(value)
        setattr(row, name, value)

    if with_hook:
        hook = getattr(row, '_dict_to_row_hook_', None)
        if hook is not None:
            # noinspection PyBroadException
            try:
                hook(data)
            except Exception:
                pass

    return row


class ModelBase(db.Model):
    __abstract__ = True

    id = Column(Integer, primary_key=True)

    created_at = Column(db.POSTGRESQL_TIMESTAMP_6, default=now)
    updated_at = Column(db.POSTGRESQL_TIMESTAMP_6, default=now, onupdate=now)

    def to_dict(self, *, with_hook: bool = True, enum_to_name: bool = False, value_to_str = False):
        return row_to_dict(self, with_hook=with_hook,
                           enum_to_name=enum_to_name, value_to_str=value_to_str)


    @classmethod
    def from_dict(cls, data: dict, *, with_hook: bool = True,
                  name_to_enum: bool = False) -> T:
        """从字典中初始化对象"""
        return dict_to_row(data, row_class=cls, with_hook=with_hook,
                           name_to_enum=name_to_enum)

    @classmethod
    def get_or_create(cls, auto_commit=False, **kwargs) -> T:
        filters = [getattr(cls, k) == v for k, v in kwargs.items()]
        if record := cls.query.filter(*filters).first():
            return record
        record = cls()
        for k, v in kwargs.items():
            setattr(record, k, v)
        if auto_commit:
            return db.session_add_and_commit(record)
        return record


__all__ = 'db', 'row_to_dict', 'dict_to_row', 'ModelBase'
