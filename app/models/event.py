from enum import Enum as PyEnum
from typing import List, Optional, TYPE_CHECKING
from datetime import datetime
from sqlalchemy import (
    Column, Integer, String, Text,
    Index, func
)
from sqlalchemy.dialects.postgresql import JSONB

from app.common.constants import Language
from .base import db, ModelBase

if TYPE_CHECKING:
    from .comment import Comment


class CommentEvent(ModelBase):
    """评论事件表，记录用户产生的所有动作历史轨迹
    如果用户 A 发表评论，并且 @ 用户 B，那么会在这个表中产生 2 条记录，分别是 A 的 COMMENT 事件和 B 的 AT 事件
    当前互动消息接口也是查询这个表获取数据的
    """

    class EventType(PyEnum):
        UP = 'up'            # 点赞
        DOWN = 'down'        # 踩
        COMMENT = 'comment'  # 评论
        REPLY = 'reply'      # 回复
        AT = 'at'            # 提及
        TIP = 'tip'          # 打赏

    class EventSource(PyEnum):
        SELF = 'self'      # 自己的动作
        OTHERS = 'others'    # 其他用户与我的互动

    class ReadStatus(PyEnum):
        UNREAD = 'unread'  # 未读状态
        READ = 'read'      # 已读状态

    user_id: int = Column(Integer, nullable=False)
    other_user_id: int = Column(Integer, nullable=False)
    comment_id: int = Column(Integer, nullable=False)
    type: EventType = Column(db.StringEnum(EventType), nullable=False)
    source: EventSource = Column(db.StringEnum(EventSource), nullable=False)
    lang: Language = Column(db.StringEnum(Language), nullable=False)        # 评论语言，这里冗余存储，避免每次都要从 comment 表中查询
    read_status: ReadStatus = Column(db.StringEnum(ReadStatus), default=ReadStatus.UNREAD)
    self_interact: bool = Column(db.Boolean, nullable=False, default=False)
    extra = Column(JSONB, default=dict)

    __table_args__ = (
        Index('ix_events_user_created', user_id, 'created_at'),
        Index('ix_events_user_status_created', user_id, read_status, 'created_at'),
    )
