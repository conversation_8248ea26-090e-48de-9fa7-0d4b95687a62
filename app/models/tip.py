
import logging

from sqlalchemy import Column, Integer

from .base import ModelBase, db

_logger = logging.getLogger(__name__)


class CommentTip(ModelBase):
    """评论打赏资产记录表
    由于 backend 会处理资产变更状态，这个表里都是成功的打赏记录
    """
    send_user_id = Column(Integer, nullable=False, index=True)    # 发打赏用户ID
    receive_user_id = Column(Integer, nullable=False, index=True) # 收打赏用户ID
    comment_id = Column(Integer, nullable=False, index=True)  	  # 打赏评论ID
    amount = Column(db.DECIMAL_AMOUNT, nullable=False)      		  # 打赏金额（CET）
    price = Column(db.DECIMAL_PRICE, nullable=False)      		      # 打赏完成时刻的价格
    asset = db.Column(db.String(32), nullable=False) # 币种
    asset_id = db.Column(db.Integer, nullable=False) # 币种 id（防止币种重名，需要记录 id，当前使用币种资料id）
    balance_transfer_id = Column(Integer, nullable=True, unique=True) # 资产变更记录ID

