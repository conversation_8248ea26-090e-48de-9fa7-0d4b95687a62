from sqlalchemy import (
    Column, Integer, func
)

from app.utils.iterable import batch_iter

from .base import ModelBase, db
from app.models.tip import CommentTip


class CommentStatistics(ModelBase):
    """评论维度统计表"""
    # 评论维度的统计值，以后都记在这里，本来 up_count、down_count、report_count 也应该属于统计值，
    # 但它们早期都是都是放在 comment 表里了，而且跟核心业务更紧密，暂时不动
    comment_id = Column(Integer, nullable=False, unique=True)    # 评论id，一一对应
    tip_count = Column(Integer, default=0)  # 打赏次数
    tip_user_count = Column(Integer, default=0)  # 打赏人数（去重）
    tip_amount = Column(db.DECIMAL_AMOUNT, default=0)  # 打赏总金额

    @classmethod
    def update_tip_by_comment(cls, comment_id: int):
        """更新评论打赏数据
        从 CommentTip 表中统计指定 comment_id 的打赏次数、打赏人数和打赏总金额
        注意：目前的实现，没有考虑打赏有多币种的情况
        Args:
            comment_id: 评论ID

        Returns:
            更新后的 CommentStatistics 对象
        """

        # 查询该评论的统计记录，不存在则创建
        stat = cls.query.filter(cls.comment_id == comment_id).first()
        if not stat:
            stat = cls(comment_id=comment_id)
            db.session.add(stat)
        # 一次查询获取所有统计值(预计数据量较少,因此全量查询,后期可按需更新为增量更新)
        result = CommentTip.query.filter(CommentTip.comment_id == comment_id).with_entities(
                    func.count().label("tip_count"),
                    func.count(db.distinct(CommentTip.send_user_id)).label("tip_user_count"),
                    func.sum(CommentTip.amount).label("tip_amount"),
            ).first()

        # 更新统计值
        stat.tip_count = result.tip_count
        stat.tip_user_count = result.tip_user_count or 0
        stat.tip_amount = result.tip_amount or 0

        db.session.commit()
        return stat
    
    @classmethod
    def get_comment_statistics_map(cls, comment_ids: list[int]):
        result = {}
        for ids in batch_iter(comment_ids, 5000):
            tmp = cls.query.filter(
                cls.comment_id.in_(ids)
            ).with_entities(
                cls.comment_id,
                cls.tip_count,
                cls.tip_user_count,
                cls.tip_amount,
            ).all()
            result.update({item.comment_id: {
                'tip_count': item.tip_count,
                'tip_user_count': item.tip_user_count,
                'tip_amount': item.tip_amount,
            } for item in tmp})
            for id_ in ids:
                if id_ not in result:
                    result[id_] = {
                        'tip_count': 0,
                        'tip_user_count': 0,
                        'tip_amount': 0,
                    }
        return result


class CommentUserStatistics(ModelBase):
    """用户维度评论统计表"""
    user_id = Column(Integer, nullable=False, unique=True)
    comment_count = Column(Integer, nullable=False, default=0) # 发表评论数（评论+回复）
    reply_count = Column(Integer, nullable=False, default=0) # 收到回复数
    up_count = Column(Integer, nullable=False, default=0)
    down_count = Column(Integer, nullable=False, default=0)
    report_count = Column(Integer, nullable=False, default=0)
    warning_count = Column(Integer, nullable=False, default=0)
    block_count = Column(Integer, nullable=False, default=0)

    send_tip_count = Column(Integer, default=0)  # 发打赏次数
    send_tip_user_count = Column(Integer, default=0)  # 收打赏人数（去重）
    send_tip_amount = Column(db.DECIMAL_AMOUNT, default=0)  # 发打赏总金额
    receive_tip_count = Column(Integer, default=0)  # 收打赏次数
    receive_tip_user_count = Column(Integer, default=0)  # 收打赏人数（去重）
    receive_tip_amount = Column(db.DECIMAL_AMOUNT, default=0)  # 收打赏总金额
