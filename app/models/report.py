
import logging

from sqlalchemy import Column, Integer

from .base import ModelBase, db

_logger = logging.getLogger(__name__)


class DailyCommentTipReport(ModelBase):
    report_date = db.Column(db.Date, nullable=False, unique=True)
    send_tip_count = Column(Integer, default=0)  # 发打赏次数
    send_tip_user_count = Column(Integer, default=0)  # 收打赏人数（去重）
    send_tip_amount = Column(db.DECIMAL_AMOUNT, default=0)  # 发打赏总金额（数量）
    send_tip_usd = Column(db.DECIMAL_AMOUNT, default=0) 	# 发打赏市值
    receive_tip_count = Column(Integer, default=0)  # 收打赏次数
    receive_tip_user_count = Column(Integer, default=0)  # 收打赏人数（去重）
    receive_tip_amount = Column(db.DECIMAL_AMOUNT, default=0)  # 收打赏总金额（数量）
    receive_tip_usd = Column(db.DECIMAL_AMOUNT, default=0) 	# 收打赏市值


class MonthlyCommentTipReport(ModelBase):
    report_date = db.Column(db.Date, nullable=False, unique=True)
    send_tip_count = Column(Integer, default=0)  # 发打赏次数
    send_tip_user_count = Column(Integer, default=0)  # 收打赏人数（去重）
    send_tip_amount = Column(db.DECIMAL_AMOUNT, default=0)  # 发打赏总金额（数量）
    send_tip_usd = Column(db.DECIMAL_AMOUNT, default=0) 	# 发打赏市值
    receive_tip_count = Column(Integer, default=0)  # 收打赏次数
    receive_tip_user_count = Column(Integer, default=0)  # 收打赏人数（去重）
    receive_tip_amount = Column(db.DECIMAL_AMOUNT, default=0)  # 收打赏总金额（数量）
    receive_tip_usd = Column(db.DECIMAL_AMOUNT, default=0) 	# 收打赏市值
