from dataclasses import dataclass
from enum import Enum
from typing import List, Optional
from xmlrpc.client import DateTime

from sqlalchemy import (
    Column, Integer, String, Float, Text,
    Index, UniqueConstraint, func, desc
)
from sqlalchemy.orm import make_transient, load_only
from sqlalchemy.dialects.postgresql import JSONB

from app.common.constants import Language
from app.utils.iterable import batch_iter
from .base import db, ModelBase, StringEnum
from datetime import datetime
from dateutil import tz
import logging

_logger = logging.getLogger(__name__)


@dataclass
class User:
    """用户结构"""
    id: str
    user_name: str
    account_name: str

    @classmethod
    def get_user_name_by_id(cls, user_id: int) -> str:
        # TODO: 从 backend 获取用户信息，并且缓存到内存
        pass
    
    @classmethod
    def get_user_names_by_ids(cls, user_ids: List[int]) -> Optional[List[str]]:
        # TODO: 从 backend 获取用户信息，并且缓存到内存
        pass
    

class Comment(ModelBase):
    """评论表"""
    class Business(Enum):
        COIN = 'coin'

    class CommentStatus(Enum):
        CREATED = 'created'     # 刚创建，待审核（仅自己可见）
        PUBLISHED = 'published' # 审核通过，可公开展示（两级审核都可以通过）
        DISABLED = 'disabled'   # 审核失败，仅自己可见（两级审核都可以拒绝）
        DELETED = 'deleted'     # 已删除

    class SortType(Enum):
        HOT = 'hot'            # 热度排序  
        NEW = 'new'            # 最新排序
        TOP = 'top'            # 互动排序
    
    # 列定义
    business = Column(db.StringEnum(Business), nullable=False)  # 业务代码，比如 'COIN'
    business_id = Column(String(128), nullable=False)           # 业务id，比如 '1', 'AB126FD'
    business_code = Column(String(128))                         # 业务 id 对应的可读编码，比如：'BTC'
    content = Column(Text, nullable=False)
    content_hash = Column(String(64), nullable=True)  # 新增：内容MD5哈希
    user_id = Column(Integer, nullable=False)
    parent_id = Column(Integer)                             # 父级评论的 id（这个字段有值，说明是回复）
    root_id = Column(Integer)                               # 一级评论的 id（这个字段有值，说明是回复）
    lang = Column(StringEnum(Language), nullable=False)     # 评论所属语区
    detected_lang = Column(StringEnum(Language))            # 根据评论内容的检测出的语言，如果为空，表示识别失败
    is_simple = Column(db.Boolean, default=False)             # 是否简单文本，即只有标点、空白、表情符号之类的，无需翻译的文本
    at_users: list = Column(JSONB)          # 提及的用户列表
    status = Column(db.StringEnum(CommentStatus), default=CommentStatus.CREATED, nullable=False)
    up_count = Column(Integer, default=0)                   # 点赞数
    down_count = Column(Integer, default=0)                 # 点踩数
    report_count = Column(Integer, default=0)               # 举报数
    reply_count = Column(Integer, default=0)                # 回复数
    hot_score = Column(Float, default=0)                    # 热度分数，用于 HOT 排序
    top_score = Column(Float, default=0)                    # 互动分数，用于 TOP 排序
    new_score = Column(Float, default=0)                    # 新旧分数，用于 NEW 排序
    remark = Column(Text, default=None)                   # 管理员备注
    status_updated_at = Column(
        db.POSTGRESQL_TIMESTAMP_6, default=datetime.utcfromtimestamp(0)
    )  # 状态更新时间，用于修正评论/回复数

    # 一些充当 Entity 的临时字段
    is_voted = 0     # 用户是否点赞(1)或踩(-1)了该评论，0表示没有投
    highlighted = False  # 是否高亮显示（从互动消息跳转到列表时，指定的那一条）
    parent_user_id = None  # 父级评论的用户ID

    # 索引
    __table_args__ = (
        Index('ix_comments_business_lang_hot', business, business_id, lang, hot_score, 'created_at'),
        Index('ix_comments_business_lang_interact', business, business_id, lang, top_score, 'created_at'),
        Index('ix_comments_business_lang_created', business, business_id, lang, new_score),
        Index('ix_comments_root_id_created', root_id, 'created_at'),
        Index('ix_comments_user_id_created', user_id, 'created_at'),
        Index('ix_comments_status_updated_parent_id', status_updated_at, parent_id),
    )

    def get_score(self, sort_type: SortType) -> float:
        score = getattr(self, self.score_attr_name(sort_type))
        return 0 if score is None else score

    @classmethod
    def score_attr_name(cls, sort_type: SortType):
        return SCORE_COLS[sort_type].key

    @classmethod
    def get_comments(cls, business: str = None, business_id: str = None, lang: Language = None,
                     sort_type: SortType = SortType.HOT,
                     root_id: int = None,
                     last_score: float = None, last_id: str = None, limit: int = 100,
                     readonly: bool = True,
                     with_entities: list = None) -> List['Comment']:
        """获取一级评论或回复列表"""
        if root_id:
            query = cls.query.filter(
                cls.root_id == root_id,
            )
        else:
            query = cls.query.filter(
                cls.business == business,
                cls.business_id == business_id,
                cls.lang == lang,
                cls.root_id.is_(None),  # 一级评论
            )

        query = query.filter(cls.status == cls.CommentStatus.PUBLISHED)
        if last_score is not None:
            query = query.filter(SCORE_COLS[sort_type] <= last_score)

        # 查询
        if with_entities:
            query = query.options(load_only(*with_entities))

        limit = limit + 3 if last_score is not None and last_id is not None else limit
        # 如果是翻页查询的话，需要多查 3 条，以免漏掉同分评论
        comments = query.order_by(desc(SCORE_COLS[sort_type]), desc(cls.id)).limit(limit).all()
        
        if readonly:
            [make_transient(comment) for comment in comments]

        if last_score is not None and last_id is not None:
            # 在 Python 层面过滤掉不需要的记录
            filtered_comments = []
            for comment in comments:
                if comment.get_score(sort_type) > last_score or comment.id >= last_id:
                    continue
                filtered_comments.append(comment)
            return filtered_comments[:limit]
        return comments


class CommentFullTextSearch(ModelBase):
    """评论全文检索表"""
    comment_id = Column(Integer, nullable=False)
    lang = Column(String(16), nullable=False)
    ts_content = Column(db.FullTextSearch, nullable=False)

    __table_args__ = (
        UniqueConstraint('comment_id', name='uq_comment_id'),
        Index('ix_ts_content', ts_content, postgresql_using='gin'),
    )


SCORE_COLS = {
    Comment.SortType.HOT: Comment.hot_score,
    Comment.SortType.TOP: Comment.top_score,
    Comment.SortType.NEW: Comment.new_score
}


class CommentTranslation(ModelBase):
    """评论翻译表"""
    comment_id = Column(Integer, nullable=False)
    lang = Column(String(16), nullable=False)
    content = Column(Text, nullable=False)
    translator = db.Column(db.String(16), nullable=False, default='ai')
    translated_by = db.Column(Integer)

    __table_args__ = (
        UniqueConstraint('comment_id', 'lang', name='uq_translation_comment_lang'),
        Index('ix_translations_translated_by', translated_by, 'created_at'),
    )

    @classmethod
    def get_comment_translation_map(cls, comment_ids: List[int], lang: Language):
        result = {}

        for ids in batch_iter(comment_ids, 5000):
            tmp = cls.query.filter(
                cls.comment_id.in_(comment_ids),
                cls.lang == lang.name
            ).with_entities(cls.comment_id, cls.content).all()
            result.update(dict(tmp))
        return result


class CommentVote(ModelBase):
    """评论投票表"""

    class VoteType(Enum):
        UP = 1
        DELETED = 0
        DOWN = -1

    user_id = Column(Integer, nullable=False)
    comment_id = Column(Integer, nullable=False)
    comment_user_id = Column(Integer, nullable=False)  # 统计用户被赞数量使用
    vote = Column(Integer, nullable=False)  # 1: up, -1: down

    __table_args__ = (
        UniqueConstraint('user_id', 'comment_id', name='uq_vote_user_comment'),
        Index('ix_comment_vote_all', user_id, comment_id, vote),
        Index('ix_comment_vote_comment_id', comment_id, vote),
        Index('ix_comment_vote_updated_at_comment_id', 'updated_at', comment_id),
        Index('ix_comment_vote_updated_at_comment_user_id', 'updated_at', comment_user_id),
        Index('ix_comment_comment_user_id', comment_user_id),
    )


class CommentTag(ModelBase):
    """评论标签表"""
    name = Column(String(256), nullable=False)
    business = Column(String(128), nullable=False)
    business_id = Column(String(128), nullable=False)
    lang = Column(String, nullable=False)
    comment_ids = Column(JSONB, default=list)  # 使用JSONB存储评论ID列表

    __table_args__ = (
        Index('ix_tags_name', name),
        Index('ix_tags_business_lang_name', business, business_id, lang, name),
        # 为JSONB列创建GIN索引以支持高效查询
        Index('ix_tags_comment_ids_gin', comment_ids, postgresql_using='gin'),
    )

    @classmethod
    def get_or_create_tag(cls, content: str, business: str, business_id: str, lang: str) -> 'CommentTag':
        """获取或创建标签"""
        tag = cls.get_or_create(
            content=content,
            business=business,
            business_id=business_id,
            lang=lang
        )
        return db.session_add_and_flush(tag)

    @classmethod
    def add_comment_to_tag(cls, tag_id: int, comment_id: int):
        """将评论ID添加到标签的comment_ids列表中"""
        tag = cls.query().get(tag_id)
        if tag and comment_id not in tag.comment_ids:
            tag.comment_ids.append(comment_id)
            db.session_add_and_flush(tag)

    @classmethod
    def remove_comment_from_tag(cls, tag_id: int, comment_id: int):
        """从标签的comment_ids列表中移除评论ID"""
        tag = cls.query().get(tag_id)
        if tag and comment_id in tag.comment_ids:
            tag.comment_ids.remove(comment_id)
            db.session_add_and_flush(tag)

    @classmethod
    def get_tags_by_business(cls, business: str, business_id: str, lang: str, limit: int = 20):
        """获取指定业务和语言的标签列表"""
        return cls.query().filter(
            cls.business == business,
            cls.business_id == business_id,
            cls.lang == lang
        ).order_by(
            func.jsonb_array_length(cls.comment_ids).desc()
        ).limit(limit).all()


class CommentWarning(ModelBase):
    """评论警告表"""
    comment_id = Column(Integer, nullable=False)
    title = Column(String(256), nullable=False)
    content = Column(Text, nullable=False)


class CommentWarningTranslation(ModelBase):
    """评论警告翻译表"""
    title = Column(String(256), nullable=False)
    content = Column(Text, nullable=False)
    comment_warning_id = Column(Integer, nullable=False)
    lang = Column(StringEnum(Language), nullable=False)
