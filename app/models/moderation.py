from enum import Enum
from typing import List, Optional, TYPE_CHECKING
from datetime import datetime
from sqlalchemy import (
    Column, Integer, String, Boolean, DateTime, 
    Text, Index, UniqueConstraint
)
from sqlalchemy.orm import relationship
from sqlalchemy import func

from app.utils.date_ import now
from app.utils.iterable import batch_iter
from .base import StringEnum, db, ModelBase
from sqlalchemy.dialects.postgresql import JSONB

if TYPE_CHECKING:
    from .comment import Comment


class CommentReport(ModelBase):
    """评论举报表"""

    class Type(Enum):
        FRAUD = 'fraud'           # 欺诈/诈骗
        MALICIOUS = 'malicious'   # 恶意/消极
        SPAM = 'spam'             # 垃圾
        FAKE = 'fake'             # 虚假互动
        OTHER = 'other'           # 其他

    user_id = Column(Integer, nullable=False)
    comment_id = Column(Integer,  nullable=False)
    comment_user_id = Column(Integer, nullable=False)
    type = Column(db.StringEnum(Type), nullable=False)
    reason = Column(Text, nullable=True)

    __table_args__ = (
        Index('ix_reports_created_at_comment_id', 'created_at', comment_id),
        Index('ix_reports_comment_id', comment_id),
        Index('ix_reports_comment_user_id', comment_user_id),
    )

    @classmethod
    def get_comment_report_count(cls, comment_ids: List[int]):

        result = {}
        for ids in batch_iter(comment_ids, 5000):
            tmp = cls.query.filter(
                cls.comment_id.in_(comment_ids)
            ).with_entities(cls.comment_id, 
                            func.count(cls.id)).group_by(cls.comment_id).all()
            result.update(dict(tmp))
        return result


class CommentReportReview(ModelBase):
    """评论举报审核表"""

    class Status(Enum):
        CREATED = 'created'      # 刚创建，待审核
        RESERVED = 'reserved'    # 已保留，评论可以展示
        DISABLED = 'disabled'    # 已处理，评论被禁用

    comment_id = Column(Integer, nullable=False)
    status = Column(db.StringEnum(Status), default=Status.CREATED)
    operator_id = Column(Integer)    # 管理员 id

    __table_args__ = (
        Index('ix_report_reviews_comment', comment_id),
        Index('ix_report_reviews_created_status', 'created_at', status),
    )


class CommentModeration(ModelBase):
    """评论审核表"""

    class Phase(Enum):
        INITIAL = 'initial'    # 初步筛查
        FULL = 'full'         # 全面筛查
    
    class Status(Enum):
        PROCESSING = 'processing'   # 进行中
        APPROVED = 'approved'       # 通过
        AUTO_APPROVED = 'auto-approved' # 审核处于处理中超过10分钟自动通过
        REJECTED = 'rejected'       # 拒绝
        FAILED = 'failed' # 审核失败
        
    class ModeratorType(Enum):
        KEYWORD = 'keyword'   # 关键词过滤器
        AI = 'ai'            # AI 审核员
        MANUAL = 'manual'    # 人工审核员

    class Trigger(Enum):
        AUTO = 'auto'        # 系统自动触发
        PATROL = 'patrol'    # 人工巡检触发
        REPORT = 'report'    # 举报触发

    class RejectType(Enum):
        # value 为数字，让ai判断违规时输出更少更快
        ABUSE = 1  # 辱骂、攻击性语言、侮辱他人
        ILLEGAL = 2  # 违禁品、非法物品交易
        VIOLENCE = 3  # 宣扬暴力行为
        OTHER = 4  # 引流联系方式
        PORN = 5  # 色情内容
        DISCRIMINATION = 6  # 歧视性言论


    comment_id = Column(Integer, nullable=False)
    phase: Phase = Column(db.StringEnum(Phase), nullable=False)
    moderator_type: ModeratorType = Column(db.StringEnum(ModeratorType), nullable=False)
    trigger: Trigger = Column(db.StringEnum(Trigger), nullable=False)
    status: Status = Column(db.StringEnum(Status), default=Status.PROCESSING)
    rejected_type: RejectType = Column(db.StringEnum(RejectType), nullable=True)
    reason = Column(Text, default=None)# 详细的原因会比较长
    remark = Column(Text, default=None)
    operator_id = Column(Integer)
    moderation_detail = Column(JSONB, nullable=True)  # 存储详细的审核信息

    @property
    def violation_details(self):
        """获取违规详情"""
        if not self.moderation_detail:
            return None
        return self.moderation_detail.get('details')

    @property
    def all_violations(self):
        """获取所有违规类型"""
        if not self.violation_details:
            return []
        return self.violation_details.get('all_violations', [])

    @property
    def violation_text(self):
        """获取违规文本片段"""
        if not self.violation_details:
            return []
        return self.violation_details.get('violation_text', [])

    @property
    def violation_reasons(self):
        """获取具体违规原因"""
        if not self.violation_details:
            return []
        return self.violation_details.get('violation_reasons', [])

    @classmethod
    def create_initial_moderation(cls, comment_id: int):
        """创建初始审核记录
        Args:
            comment_id: 评论ID
        Returns:
            CommentModeration: 审核记录
        """
        moderation = cls(
            comment_id=comment_id,
            phase=cls.Phase.INITIAL,
            moderator_type=cls.ModeratorType.AI,
            trigger=cls.Trigger.AUTO,
            status=cls.Status.PROCESSING,
        )
        db.session_add_and_flush(moderation)
        return moderation


    __table_args__ = (
        Index('ix_moderations_comment_type', comment_id, phase, 'created_at'),
        Index('ix_moderations_operator', operator_id, 'created_at'),
    )


class CommentUserStatus(ModelBase):
    """用户评论状态表"""
    
    class BanDuration(Enum):
        TEN_DAYS = 10
        THIRTY_DAYS = 30
        SIXTY_DAYS = 60
        ONE_EIGHTY_DAYS = 180
        THREE_SIXTY_FIVE_DAYS = 365
        FOREVER = 0
    
    class BanReason(Enum):
        FRAUD = 'fraud'           # 欺诈/诈骗
        MALICIOUS = 'malicious'   # 恶意/消极
        SPAM = 'spam'             # 垃圾消息
        FAKE = 'fake'             # 虚假互动

    user_id = Column(Integer, nullable=False, unique=True)
    banned = Column(Boolean, default=False)
    ban_duration = Column(db.StringEnum(BanDuration), default=None)
    # banned is True and banned_until is None means banned forever
    banned_until = Column(db.POSTGRESQL_TIMESTAMP_6, default=None)
    banned_reason = Column(String(256), nullable=True) # multiple reasons separated by comma
    
    __table_args__ = (
        UniqueConstraint('user_id', name='uq_user_comment_status'),
    )

    def ban(self, duration: BanDuration, reason: List[BanReason], is_commit=True):
        """封禁用户"""
        self.banned = True
        self.ban_duration = duration
        self.banned_reason = ','.join([item.name for item in reason])
        if duration != self.BanDuration.FOREVER:
            self.banned_until = now() + datetime.timedelta(days=duration.value)
        else:
            self.banned_until = None
        if is_commit:
            db.session.commit()
    
    def unban(self, is_commit=True):
        """解封用户"""
        self.banned = False
        self.ban_duration = None
        self.banned_until = None
        self.banned_reason = None
        if is_commit:
            db.session.commit()
    
    def is_banned(self) -> bool:
        """是否被封禁"""
        if self.banned:
            if self.banned_until is None:
                return True
            if self.banned_until < now():
                return False
            return True
        return False
