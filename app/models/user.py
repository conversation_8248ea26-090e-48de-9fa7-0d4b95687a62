from typing import Iterable

from app.utils.iterable import batch_iter
from .base import ModelBase, db
from enum import Enum
from sqlalchemy import (
    Column, Boolean, Integer, String, Float, Text,
    Index, UniqueConstraint, func, desc
)
from app.models.base import StringEnum
from ..utils import now


class UserInfo(ModelBase):
    """用户信息映射表"""
    user_id = Column(Integer, nullable=False)
    name = Column(String(64))
    account_name = Column(String(64), nullable=False)
    avatar = Column(String(128))

    signed_off = Column(Boolean, nullable=False, default=False)

    __table_args__ = (
        UniqueConstraint('user_id', name='uq_user_id'),
        Index('ix_name_user_id', name, user_id),
        Index('ix_account_name_user_id', account_name, user_id),
    )

    @classmethod
    def set(cls, user_id: int, name: str | None, account_name: str, avatar_file_key=None, signed_off=False):
        user = cls.query.filter(cls.user_id == user_id).first()
        if not user:
            user = cls(user_id=user_id, 
                       name=name or "", 
                       account_name=account_name, 
                       avatar=avatar_file_key or "",
                       signed_off=signed_off)
            db.session.add(user)
        else:
            user.name = name or ""
            user.account_name = account_name
            user.avatar = avatar_file_key or ""
            user.sign_off = signed_off
            user.updated_at = now()

        db.session.commit()
    

    @classmethod
    def set_signed_off(cls, user_id: int, signed_off: bool):
        if not (user:= cls.query.filter(cls.user_id == user_id).first()):
            return
        user.account_name = user.account_name or ""
        user.signed_off = signed_off
        db.session.commit()

    @classmethod
    def get_user_info_map(cls, user_ids: Iterable[int], fields: list[str] = None):
        if not fields:
            fields = ['name', 'account_name']
        fields.append('user_id')
        fields = list(set(fields))
        result = {}
        users = []
        for ids in batch_iter(user_ids, 5000):
            tmp = cls.query.filter(cls.user_id.in_(ids)).with_entities(
                *[getattr(cls, field) for field in fields]
            ).all()
            users.extend(tmp)
        result.update({user.user_id: user for user in users})
        return result
