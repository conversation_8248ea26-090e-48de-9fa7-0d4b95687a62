from flask import g, request
from app.utils.date_ import timezone_to_offset
from app.api.common.requests import get_request_language, get_request_user_id, get_request_timezone


url_prefix = '/res'


def before_request():
    from app.business.user import UserManager
    lang = get_request_language().value
    g.user_lang = g.lang = lang   # 接口层面返回用户语言相关的内容时，用g.lang或g.user_lang
    g.user_tz = tz = get_request_timezone()
    g.user_id = get_request_user_id()
    if tz is not None:
        g.user_tz_offset = timezone_to_offset(tz) // 60
