
from decimal import Decimal
from app.api.common import Resource
from app.api.common import Namespace
from app.api.common.decorators import respond_with_code


ns = Namespace('CommentTip', description='评论打赏相关接口')


@ns.route('/config')
@respond_with_code
class CommentTipConfigResource(Resource):
    """打赏相关的配置"""
    
    def get(cls):
        """获取打赏相关的配置"""
        return dict(
            tip_amount_options=(Decimal('5'), Decimal('10'), Decimal('20'))
        )
