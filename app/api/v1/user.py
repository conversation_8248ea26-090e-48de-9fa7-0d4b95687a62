from app.api.common import Namespace, mm_fields
from app.api.common import Resource
from app.api.common.decorators import respond_with_code
from app.cache.user import UserInfoCache
from app.models.user import UserInfo
from logging import getLogger

ns = Namespace('User')

_logger = getLogger(__name__)


@ns.route('/info')
@respond_with_code
class UserInfoResource(Resource):
    """用户信息"""

    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer(required=True),
        is_signed_off=mm_fields.Boolean(missing=False),
    ))
    def post(cls, **kwargs):
        """
        更新用户状态
        """
        UserInfo.set_signed_off(
            user_id=kwargs['user_id'],
            signed_off=kwargs['is_signed_off'],
        )

        UserInfoCache().set_signed_off(
            user_id=kwargs['user_id'],
            signed_off=kwargs['is_signed_off'],
        )