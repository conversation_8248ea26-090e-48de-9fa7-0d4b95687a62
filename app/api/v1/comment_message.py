from flask import g
from datetime import datetime, timedelta

from app.api.common import Resource
from app.api.common import Namespace, mm_fields
from app.api.common.fields import LimitField, EnumField, TimestampField
from app.api.common.requests import get_request_language
from app.api.common.decorators import respond_with_code, login_required, limit_user_frequency
from app.business.comment_manager import CommentManager

from app.business.comment_message import CommentMessageManager
from app.business.user import UserManager
from app.models.comment import Comment
from app.models.event import (
    CommentEvent
)
from app.common.constants import Language
from app.exceptions import InvalidArgument


ns = Namespace('CommentMessage')


@ns.route('/message-list')
@respond_with_code
class CommentMessageResource(Resource):
    """互动消息"""
    
    @classmethod
    @login_required
    @limit_user_frequency(20, 60)
    @ns.use_kwargs(dict(
        read_status=EnumField(CommentEvent.ReadStatus, missing=None),  # 消息状态筛选(已读/未读)
        message_type=EnumField(CommentEvent.EventType, missing=None),  # 消息类型筛选(点赞/踩/评论/回复/提及)
        message_source=EnumField(CommentEvent.EventSource, missing=None),  # 消息来源筛选(自己/他人)
        start_time=TimestampField(missing=datetime.now() - timedelta(days=90)),  # 开始时间,默认近3个月
        end_time=TimestampField(missing=None),  # 结束时间
        limit=LimitField(missing=20),
        last_id=mm_fields.Integer(missing=None)
    ))
    def get(cls, **kwargs):
        """获取互动消息列表
        
        支持:
        - 状态筛选(全部/已读/未读)
        - 类型筛选(全部/回复/赞/踩/提及/我的评论/我的回复/我的点赞/我的踩)
        - 时间范围筛选(默认近3个月)
        """
        result = CommentMessageManager.get_messages(
            user_id=g.user_id,
            read_status=kwargs.get('read_status'),
            types=[kwargs.get('message_type')] if kwargs.get('message_type') else None,
            source=kwargs.get('message_source'),
            start_time=kwargs.get('start_time'),
            end_time=kwargs.get('end_time'),
            limit=kwargs['limit'],
            last_id=kwargs.get('last_id')
        )
        messages = result.items
        has_next = result.has_next
        messages = list(messages)
        comment_ids = set()
        user_ids = set()

        for msg in messages:
            comment_ids.add(msg.comment_id)
            user_ids.add(msg.other_user_id)

        # 获取comment内容
        comments = CommentManager.get_comment_content_by_ids(list(comment_ids))
        parent_comment_ids = set()
        for comment in comments.values():
            if comment.parent_id:
                parent_comment_ids.add(comment.parent_id)
            if comment.root_id:
                parent_comment_ids.add(comment.root_id)

        parent_comments = {}
        if parent_comment_ids:
            parent_comments = CommentManager.get_comment_content_by_ids(list(parent_comment_ids))

        # 获取user_info
        user_infos = UserManager.get_user_info(list(user_ids))
        lang = get_request_language()
        res = []
        for msg in messages:

            comment = comments[msg.comment_id]
            user_info = user_infos.get(msg.other_user_id, {})
            if user_info:
                user_info['user_id'] = msg.other_user_id

            if comment.root_id is not None:
                root_comment = parent_comments.get(comment.root_id)
            else:
                root_comment = comment

            is_author = False
            if root_comment:
                if msg.source == CommentEvent.EventSource.OTHERS:
                    user_id = msg.other_user_id
                else:
                    user_id = msg.user_id
                is_author = root_comment.user_id == user_id

            if comment.parent_id is not None:
                parent_comment = parent_comments.get(comment.parent_id)
                if parent_comment:
                    content = parent_comment.content
                    comment_status = parent_comment.status
                else:
                    content = ""
                    comment_status = Comment.CommentStatus.DELETED  # 缺省值

                reply_content = comment.content
                reply_comment_status = comment.status
            else:
                content = comment.content
                comment_status = comment.status
                reply_content = None
                reply_comment_status = None

            res.append(dict(
                message_id=msg.id,
                created_at=msg.created_at,
                comment_id=msg.comment_id,
                user_info=user_info,
                lang=msg.lang.name if msg.lang else None,
                message_type=msg.type.name if msg.type else None,
                message_source=msg.source.name if msg.source else None,
                read_status=msg.read_status.name if msg.read_status else None,
                content=content,
                reply_content=reply_content,
                comment_status=comment_status.name if comment_status else None,
                reply_comment_status=reply_comment_status.name if reply_comment_status else None,
                root_comment_status= root_comment.status.name if root_comment else None,
                is_author=is_author,
                business=comment.business.name if comment.business else None,
                business_id=comment.business_id,
                business_code=comment.business_code,
                extra=msg.extra,
                msg_title=CommentMessageManager.get_msg_title(msg.type, lang, msg.source)
            ))

        return {
            "messages": res,
            "has_next": has_next,
        }


@ns.route('/mark-read')
@respond_with_code
class CommentMessageMarkReadResource(Resource):
    """标记消息已读"""
    
    @classmethod
    @login_required
    @ns.use_kwargs(dict(
        messages_ids=mm_fields.List(mm_fields.Integer(), required=True)  # 要标记为已读的评论ID列表
    ))
    def post(cls, **kwargs):
        """标记指定消息为已读"""
        messages_ids = kwargs.get('messages_ids', [])
        updated_count = 0
        if messages_ids:
            updated_count = CommentMessageManager.mark_read(
                user_id=g.user_id,
                messages_ids=messages_ids
            )
        return {'updated_count': updated_count}


@ns.route('/mark-all-read')
@respond_with_code
class CommentMessageMarkAllReadResource(Resource):
    """标记全部消息已读"""
    
    @classmethod
    @login_required
    def post(cls):
        """标记所有消息为已读"""
        updated_count = CommentMessageManager.mark_all_read(user_id=g.user_id)
        return {'updated_count': updated_count}


@ns.route('/unread-count')
@respond_with_code
class CommentMessageUnreadCountResource(Resource):
    """未读消息数量"""
    
    @classmethod
    @login_required
    def get(cls):
        """获取未读消息数量"""
        unread_count = CommentMessageManager.get_unread_count(user_id=g.user_id)
        return {'unread_count': unread_count}
