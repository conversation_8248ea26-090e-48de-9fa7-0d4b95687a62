from flask import g
from app.api.common import Namespace, mm_fields
from app.api.common import Resource
from app.api.common.decorators import respond_with_code, login_required, limit_user_multi_frequency, \
    limit_user_frequency
from app.api.common.fields import Limit<PERSON>ield, EnumField
from app.business.comment_manager import CommentManager, CommentTranslateManager, CommentVoteManager
from app.business.comment_message import CommentEventManager
from app.business.comment_tip import CommentTipManager
from app.business.moderation_manager import CommentReportManager
from app.business.user import UserManager
from app.cache.comment_cache import CommentCache
from app.common.constants import Language
from app.exceptions import InvalidArgument, CommentUserBanned, RecordNotFound, CommentReplyUserBanned, \
    CommentUserBannedForever, CommentReplyUserBannedForever, CommentNotFound, CommentDeleted
from app.models import db
from app.models.comment import Comment, CommentVote
from app.models.moderation import CommentReport
from app.models.moderation import CommentUserStatus
from app.models.statistics import CommentUserStatistics
from app.utils import datetime_to_str
from app.utils.text import detect_lang
from logging import getLogger


ns = Namespace('Comment')

_logger = getLogger(__name__)

SortType = Comment.SortType


@ns.route('/comment-list')
@respond_with_code
class CommentListResource(Resource):
    """评论列表"""
    @classmethod
    @ns.use_kwargs(dict(
        business=EnumField(Comment.Business, required=True),  # 业务代码
        business_id=mm_fields.String(required=True),  # 业务ID
        lang=EnumField(Language, required=True),
        sort_type=EnumField(SortType, missing=SortType.HOT, default=SortType.HOT),
        last_score=mm_fields.Float(missing=None, default=None),
        last_id=mm_fields.Integer(missing=None, default=None),
        limit=LimitField(missing=50, default=50),
        highlight_comment=mm_fields.Integer(missing=None, default=None),
    ))
    def get(cls, **kwargs):
        """
        获取评论列表
        支持分页、排序(Hot/New/Top)、多语言、高亮评论
        """
        business = kwargs.get('business')
        business_id = kwargs.get('business_id')
        lang = kwargs.get('lang')
        sort_type = kwargs.get('sort_type')
        last_score = kwargs.get('last_score')
        last_id = kwargs.get('last_id')
        limit = kwargs.get('limit') or 50
        highlight_comment = kwargs.get('highlight_comment')
        user_id = g.user_id

        # 先获取公共评论列表缓存
        # 再获取用户屏蔽的评论ID列表
        # 过滤后返回
        comments = CommentManager.get_root_comments(
            business=business,
            business_id=business_id,
            lang=lang,
            sort_type=sort_type,
            last_score=last_score,
            last_id=last_id,
            limit=limit,
            highlight_comment_id=highlight_comment,
            user_id=user_id
        )
        return CommentManager.to_display(comments, sort_type, user_id=g.user_id)


@ns.route('')
@respond_with_code
class CommentCreateResource(Resource):
    """创建评论"""
    @classmethod
    @login_required
    @limit_user_multi_frequency([
        (5, 60),
        (2000, 60 * 60 * 24),
    ])
    @ns.use_kwargs(dict(
        business=mm_fields.String(required=True),
        business_id=mm_fields.String(required=True),  # 修改为 String 类型
        business_code=mm_fields.String(missing=None),
        lang=EnumField(Language, required=True),
        content=mm_fields.String(required=True),
        at_users=mm_fields.List(mm_fields.Dict, missing=None)
    ))
    def post(cls, **kwargs):
        """发表评论"""
        user_id = g.user_id
        user_status = CommentUserStatus.query.filter_by(user_id=user_id).first()
        _logger.info('---> user_status: %s', user_status)

        if user_status and user_status.is_banned():
            if user_status.banned_until:
                raise CommentUserBanned(
                    banned_date=datetime_to_str(user_status.banned_until, fmt='%Y-%m-%d'))
            else:
                raise CommentUserBannedForever

        business = kwargs.get('business')
        business_id = kwargs.get('business_id')
        business_code = kwargs.get('business_code')
        lang: Language = kwargs.get('lang')
        content = kwargs.get('content')
        at_users: list = kwargs.get('at_users')

        comment = CommentManager.create_comment(
            user_id=user_id,
            content=content,
            lang=lang,
            business=business,
            business_id=business_id,
            business_code=business_code,
            at_users=at_users,
        )
        UserManager.update_user_from_request()
        return CommentManager.to_display(comment)


# 注意：凡是直接针对 comment_id 的操作，就不需要 business 参数了
@ns.route('/<comment_id>/reply-list')
@respond_with_code
class CommentRepliesResource(Resource):
    """评论回复列表"""

    @classmethod
    @ns.use_kwargs(dict(
        last_score=mm_fields.Float(missing=None, default=None),
        last_id=mm_fields.Integer(missing=None, default=None),
        limit=LimitField(missing=50, default=50),
        highlight_comment=mm_fields.Integer(missing=None, default=None),
    ))
    def get(cls, comment_id, **kwargs):
        """获取指定评论的回复列表
        支持分页查询
        """
        # 获取回复列表，传入当前用户ID（未登录则为None）
        user_id = getattr(g, 'user_id', None)
        last_score = kwargs.get('last_score')
        last_id = kwargs.get('last_id')
        limit = kwargs.get('limit') or 50
        highlight_comment = kwargs.get("highlight_comment")

        root_comment = CommentCache(comment_id).read_aside()
        if not root_comment:
            raise CommentNotFound
        else:
            if root_comment['status'] == Comment.CommentStatus.DELETED.name:
                raise CommentDeleted

        root_id = int(comment_id)
        replies = CommentManager.get_comment_replies(
            root_id=root_id,
            user_id=user_id,
            last_score=last_score,
            last_id=last_id,
            limit=limit,
            highlight_comment_id=highlight_comment,
        )

        return {
            "total": int(root_comment['reply_count']),
            "replies": CommentManager.to_display(replies, sort_type=SortType.TOP, user_id=g.user_id)
        }


@ns.route('/<root_id>/reply')
@respond_with_code
class CommentReplyResource(Resource):
    """评论回复"""
    @classmethod
    @login_required
    @limit_user_multi_frequency([
        (5, 60),
        (200, 60 * 60 * 24),
    ])
    @ns.use_kwargs(dict(
        content=mm_fields.String(required=True),
        lang=EnumField(Language, required=True),
        parent_id=mm_fields.Integer(required=True),
        at_users=mm_fields.List(mm_fields.Dict, missing=None)
    ))
    def post(cls, root_id, **kwargs):
        """回复评论"""
        content = kwargs.get('content')
        lang = kwargs.get('lang')
        parent_id = kwargs.get('parent_id')
        user_id = g.user_id
        at_users: list = kwargs.get('at_users')

        user_status = CommentUserStatus.query.filter_by(user_id=user_id).first()
        if user_status and user_status.is_banned():
            if user_status.banned_until:
                raise CommentReplyUserBanned(
                    banned_date=datetime_to_str(user_status.banned_until, fmt='%Y-%m-%d'))
            else:
                raise CommentReplyUserBannedForever

        # 创建回复，业务信息会从父评论继承
        reply = CommentManager.create_comment(
            user_id=user_id,
            content=content,
            lang=lang,
            parent_id=parent_id,
            root_id=root_id,  # 一级评论ID
            at_users=at_users,
        )
        UserManager.update_user_from_request()
        return CommentManager.to_display(reply)


@ns.route('/<comment_id>')
@respond_with_code
class CommentResource(Resource):
    """评论操作"""
            
    
    @classmethod
    @login_required
    def delete(cls, comment_id:str):
        """删除评论"""
        user_id = g.user_id
        CommentManager.delete_comment(int(comment_id), user_id)
        return {}


@ns.route('/root')
@respond_with_code
class CommentRootResource(Resource):
    """一级评论"""
    @classmethod
    @ns.use_kwargs(
        dict(
            comment_id=mm_fields.Integer(required=True),  # 一级评论或者回复的ID
        )
    )
    def get(
        cls, **kwargs
    ):
        """获取一级评论详情
        @param comment_id: 一级评论或者回复的ID
        @return: 一级评论详情。
        如果 comment_id 是一级评论的ID，则返回自己的详情；
        如果 comment_id 是回复的ID，则返回对应的 root comment 详情
        因为返回的不一定是 comment_id 对应的信息，所以放在 url 里不合适，就作为 query 参数了
        """
        comment_id = kwargs.get('comment_id')
        comment = CommentManager.get_valid_comment(comment_id, from_cache=True)
        if comment.parent_id:
            root_comment = CommentManager.get_valid_comment(comment.root_id, from_cache=True)
        else:
            root_comment = comment
        return CommentManager.to_display(root_comment, user_id=g.user_id)


@ns.route('/<comment_id>/translate')
@respond_with_code
class CommentTranslateResource(Resource):
    """翻译评论"""
    @classmethod
    @ns.use_kwargs(dict(
        target=EnumField(Language, required=True),
    ))
    def post(cls, comment_id, **kwargs):
        """翻译评论"""
        current_lang = getattr(g, 'lang', 'en_US')
        target_lang = kwargs['target']
        comment = Comment.query.get(comment_id)
        if not comment:
            raise InvalidArgument()
        target_lang_comment = (CommentTranslateManager.
                               translate_comment(comment_id, target_lang, current_lang=current_lang))
        return {
            "content": target_lang_comment
        }


@ns.route('/<comment_id>/vote')
@respond_with_code
class CommentVoteResource(Resource):
    """评论投票(点赞/踩)"""

    ValidVoteTypeList = [CommentVote.VoteType.UP.value, CommentVote.VoteType.DOWN.value]

    @classmethod
    @login_required
    @limit_user_multi_frequency([
        (20, 60),
        (1000, 60 * 60 * 24),
    ])
    @ns.use_kwargs(dict(
        vote=mm_fields.Integer(required=True, validate=lambda x: x in CommentVoteResource.ValidVoteTypeList)
    ))
    def post(cls, comment_id, **kwargs):
        """点赞/踩"""
        vote = kwargs.get('vote')

        user_id = g.user_id
        CommentVoteManager.add_vote(comment_id, user_id, vote)
        UserManager.update_user_from_request()
        return {}

    @classmethod
    @login_required
    @limit_user_multi_frequency([
        (20, 60),
        (1000, 60 * 60 * 24),
    ])
    @ns.use_kwargs(dict(
        vote=mm_fields.Integer(required=True, validate=lambda x: x in CommentVoteResource.ValidVoteTypeList)
    ))
    def delete(cls, comment_id, **kwargs):
        """取消点赞/踩"""
        vote = kwargs.get('vote')\

        user_id = g.user_id
        CommentVoteManager.remove_vote(comment_id, user_id, vote)
        UserManager.update_user_from_request()
        return {}


@ns.route('/<int:comment_id>/tips')
@respond_with_code
class CommentTipsResource(Resource):
    """评论打赏记录"""

    @classmethod
    @login_required
    @limit_user_multi_frequency([
        (20, 60),
        (1000, 60 * 60 * 24),
    ])
    @ns.use_kwargs(dict(
        last_id=mm_fields.Integer(missing=None, default=None),
        limit=LimitField(missing=50),
    ))
    def get(cls, comment_id, **kwargs):
        """评论打赏记录列表"""
        comment = Comment.query.get(comment_id)
        if not comment:
            raise InvalidArgument
        last_id = kwargs.get('last_id')
        limit = kwargs['limit']
        tips = CommentTipManager.get_tips(comment_id, last_id, limit)
        return tips

@ns.route('/<int:comment_id>/tip-users')
@respond_with_code
class CommentTipUsersResource(Resource):
    """评论打赏用户"""

    @classmethod
    @ns.use_kwargs(dict(
        send_user_id=mm_fields.Integer,
        amount=mm_fields.Decimal,
        asset=mm_fields.String,
        limit=LimitField(missing=50),
    ))
    def get(cls, comment_id, **kwargs):
        """评论打赏用户列表"""
        comment = Comment.query.get(comment_id)
        if not comment:
            raise InvalidArgument
        send_user_id = kwargs.get('send_user_id')
        amount = kwargs.get('amount')
        asset = kwargs.get('asset')
        tip_users = CommentTipManager.get_tip_users(comment_id, kwargs['limit'], send_user_id, amount, asset)
        return tip_users


@ns.route('/user/vote-count')
@respond_with_code
class CommentVoteCountResource(Resource):
    """用户点赞数"""
    @classmethod
    @ns.use_kwargs(dict(
        user_id=mm_fields.Integer(required=True),
    ))
    def get(cls, **kwargs):
        """获取用户总点赞数"""
        user_id = kwargs.get('user_id')
        user_statistics = CommentUserStatistics.query.filter_by(user_id=int(user_id)).first()
        up_count = 0
        if user_statistics:
            up_count = user_statistics.up_count
        return {
            'UP': up_count
        }


@ns.route('/report-types')
@respond_with_code
class CommentReportTypesResource(Resource):
    """获取评论举报类型"""
    @classmethod
    def get(cls, **kwargs):
        """获取评论举报类型"""
        lang = Language(g.lang)
        report_type_map = CommentReportManager.get_report_type(lang)
        return [{
            "code": k.name,
            "name": v
        } for k, v in report_type_map.items()]


@ns.route('/<comment_id>/report')
@respond_with_code
class CommentReportResource(Resource):
    """评论举报"""
    @classmethod
    @login_required
    @limit_user_frequency(10, 60)
    @ns.use_kwargs(dict(
        type=EnumField(CommentReport.Type, required=True),
        reason=mm_fields.String(missing=None, validate=lambda x: 0 <= len(x) <= 500)  # 举报原因
    ))
    def post(cls, comment_id, **kwargs):
        """举报评论"""
        report_type = kwargs.get('type')
        reason = kwargs.get('reason')

        CommentReportManager.new_report(comment_id, g.user_id, report_type, reason)
        UserManager.update_user_from_request()
        return {}


@ns.route('/at-user-list')
@respond_with_code
class AtUserResource(Resource):
    #提及用户
    @classmethod
    @login_required
    def get(cls):
        return CommentEventManager.get_relate_user_infos(g.user_id)


@ns.route('/comment-info')
@respond_with_code
class CommentCountResource(Resource):
    """评论信息"""

    @classmethod
    @ns.use_kwargs(dict(
        business=EnumField(Comment.Business, required=True),  # 业务代码
        business_id=mm_fields.String(required=True),  # 业务ID
        lang=EnumField(Language, required=True),
    ))
    def get(cls, **kwargs):
        """获取评论数"""
        business = kwargs.get('business')
        business_id = kwargs.get('business_id')
        lang = kwargs.get('lang')
        count = CommentManager.get_comment_count(business, business_id, lang)
        last_comment_time = CommentManager.get_last_comment_time(business, business_id, lang)
        return {
            'root_count': count,
            'root_last_comment_time': last_comment_time,
        }
