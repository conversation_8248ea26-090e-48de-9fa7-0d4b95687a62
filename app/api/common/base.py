import json
from traceback import format_exc

from flask import make_response, jsonify, Response, current_app
from flask_restx import (Api as _Api, Resource as _Resource,
                         Namespace as _Namespace)
from flask_api.exceptions import APIException
from marshmallow import fields as mm_fields
from marshmallow.utils import EXCLUDE
from webargs.flaskparser import use_kwargs, parser
from werkzeug.exceptions import HTTPException

from app.api.common.requests import get_request_info
from app.api.common.responses import failure
from app.exceptions import ErrorWithResponseCode, ServiceUnavailable, InvalidArgument
from app.models import db, row_to_dict
from app.utils.parser import J<PERSON><PERSON>nco<PERSON> as _JsonEncoder, dict2schema


@parser.error_handler
def handle_request_parsing_error(err, req, schema,
                                 *, error_status_code, error_headers):
    raise InvalidArgument(err.messages)

class Api(_Api):

    # disable swagger
    def _register_apidoc(self, app):
        pass

    def _register_specs(self, app):
        pass

    def _register_doc(self, app):
        pass

    def handle_error(self, e):
        if isinstance(e, APIException):
            return make_response(jsonify(dict(
                message=e.detail
            )), e.status_code)
        if isinstance(e, ErrorWithResponseCode):
            return Response(json.dumps(
                failure(e.response_code, e.message, e.data),
            ), mimetype='application/json')
        elif isinstance(e, HTTPException):
            return Response(json.dumps(
                failure(e.code, e.description),
            ), status=e.code, mimetype='application/json')
        else:
            req = get_request_info()
            msg = ' '.join([f'{k}: {v}' for k, v in req.items()])
            current_app.logger.error(f"{msg} {format_exc()}", extra={'method': req['method'], 'path': req['path']})
            return Response(json.dumps(
                failure(ServiceUnavailable.response_code,
                        ServiceUnavailable.message_template,
                        {}),
            ), status=500, mimetype='application/json')


Resource = _Resource


class Namespace(_Namespace):

    _METHODS_WITH_BODY = frozenset(['post', 'put', 'patch'])

    def use_kwargs(self, fields):
        def validate_field(_f):
            if isinstance(_f, mm_fields.Field):
                return _f
            if isinstance(_f, type) and issubclass(_f, mm_fields.Field):
                return _f()
            raise TypeError(f'invalid field: {_f}')

        fields = {key: validate_field(field) for key, field in fields.items()}

        def dec(func):
            if func.__name__ in self._METHODS_WITH_BODY:
                location = 'json'
            else:
                location = 'query'

            argmap = dict2schema(fields, schema_class=parser.schema_class)(unknown=EXCLUDE)
            return use_kwargs(argmap, location=location)(func)

        return dec


class ApiJsonEncoder(_JsonEncoder):
    def default(self, obj):
        if isinstance(obj, db.Model):
            return row_to_dict(obj, enum_to_name=True)
        return super().default(obj)
