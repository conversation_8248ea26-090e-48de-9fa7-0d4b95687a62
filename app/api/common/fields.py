# -*- coding: utf-8 -*-
import decimal
import re
from datetime import datetime, date
from decimal import (Decimal, ROUND_HALF_EVEN,
                     InvalidOperation as _InvalidOperation)
from enum import EnumMeta, Enum
from json import loads as json_loads
from typing import Dict, Type, Callable, Iterable, Union, Optional, Any

import typing
from flask_restx.fields import Raw, String
from flask_restx.utils import not_none
from marshmallow import fields as mm_fields

from ... import config
from app.exceptions import InvalidArgument
from ...utils import list_enum_names, list_enum_values, timestamp_to_datetime, str_to_datetime


class DecimalType(String):
    __schema_format__ = "decimal"

    def __init__(self, decimals=8, only_positive: bool = False, **kwargs):
        super(DecimalType, self).__init__(**kwargs)
        self.precision = Decimal("0." + "0" * (decimals - 1) + "1")
        self.only_positive = only_positive

    def format(self, value):
        try:
            d_value = Decimal(value)
        except _InvalidOperation:
            raise InvalidArgument
        if not d_value.is_normal() and d_value != Decimal():
            raise InvalidArgument
        if self.only_positive and d_value < Decimal():
            raise InvalidArgument
        return d_value.quantize(self.precision, rounding=ROUND_HALF_EVEN)


class EnumType(String):
    def __init__(self, enum_class, **kwargs):
        super(EnumType, self).__init__(**kwargs)
        self.enum_class = enum_class

    def format(self, value):
        try:
            return self.enum_class[value.upper()]
        except KeyError:
            raise InvalidArgument


class CustomString(String):
    def __init__(self,
                 convert_func: Callable = lambda x: x,
                 validate_func: Callable = lambda x: True, **kwargs):
        super(CustomString, self).__init__(**kwargs)
        self.convert_func = convert_func
        self.validate_func = validate_func

    def format(self, value):
        new_value = self.convert_func(value)
        if not self.validate_func(new_value):
            raise InvalidArgument
        return new_value


class Object(Raw):
    __schema_type__ = 'object'
    __schema_format__ = 'object'
    __schema_example__ = {}

    def __init__(self,
                 model: Dict[str, Union[Raw,
                 Type[Raw],
                 dict]] = None,
                 *,
                 as_list: bool = False,
                 **kwargs):
        self._model: Dict[str, Raw] = self._normalise_model(model)
        self._as_list = as_list
        super().__init__(**kwargs)

    @classmethod
    def _normalise_model(cls, model):
        if model is None:
            return {}
        normalised = {}
        for key, value in model.items():
            if isinstance(value, dict):
                value = cls(value)
            elif isinstance(value, type):
                value = value()
            elif not isinstance(value, Raw):
                raise TypeError(f'invalid type: {value!r}')
            normalised[key] = value
        return normalised

    def schema(self) -> Dict[str, Dict[str, Any]]:
        schema = super().schema()

        empty = object()

        examples = {}
        properties = {}
        for key, model in self._model.items():
            _schema = model.schema()
            _example = _schema.get('example', empty)
            if _example is empty:
                _example = model.example
            if _example is not None:
                _example = model.format(_example)
            examples[key] = _example
            properties[key] = not_none(_schema)

        new = {'properties': properties}

        if self._as_list:
            schema['type'] = 'array'
            schema['example'] = schema['example'] or [examples]
            schema['items'] = new
        else:
            schema['example'] = schema['example'] or examples
            schema.update(new)

        return schema


class UTCTimeStr(String):

    def format(self, value):
        return datetime.utcfromtimestamp(int(value)).strftime('%Y-%m-%d %H:%M:%S')


class TimestampMarshalField(Raw):

    def format(self, value: Union[datetime, date]):
        if not isinstance(value, datetime):
            value = datetime(value.year, value.month, value.day)
        return int(value.timestamp())


class DateMarshalField(Raw):

    def format(self, value: Union[datetime, date]):
        if isinstance(value, date):
            value = datetime(value.year, value.month, value.day)
        return value.strftime('%Y-%m-%d')


class JsonMarshalField(Raw):

    def format(self, value):
        if not value:
            return self.default
        if isinstance(value, str):
            return json_loads(value)
        return value


class EnumMarshalField(Raw):

    def __init__(self, enum: EnumMeta,
                 output_field_name: bool = True,
                 output_field_lower: bool = True,
                 raise_ex: Optional[Exception] = InvalidArgument,
                 use_translate: bool = False,
                 **kwargs
                 ):
        self._enum_cls = enum
        self._output_field_name = output_field_name
        self._output_field_lower = output_field_lower
        self._raise_ex = raise_ex
        self._use_translate = use_translate
        super().__init__(**kwargs)

    def format(self, value):
        try:
            enum_obj = self._enum_cls(value)
        except KeyError:
            enum_obj = self._enum_cls[value]
        except Exception:
            if self._raise_ex:
                raise InvalidArgument
            raise
        output = enum_obj.name if self._output_field_name else enum_obj.value
        if self._output_field_lower and not output.isdigit():
            output = output.lower()
        return output


class PageField(mm_fields.Integer):
    def __init__(self, missing: int = 1, max_page: Optional[int] = None,
                 unlimited: bool = False,
                 **kwargs):
        validate = (lambda x: (max_page or config['WEB_MAX_PAGE']) >= x >= 1) \
            if not unlimited else (lambda x: True)
        super().__init__(
            missing=missing,
            validate=validate,
            **kwargs
        )


class LimitField(mm_fields.Integer):
    def __init__(self, missing: int = 10, max_limit: Optional[int] = None,
                 **kwargs):
        super().__init__(
            missing=missing,
            validate=lambda x:
            (max_limit or config['WEB_MAX_LIMIT']) >= x >= 1,
            **kwargs
        )


class PositiveDecimalField(mm_fields.Decimal):

    def __init__(self, allow_zero=False, **kwargs):
        self.allow_zero = allow_zero
        super().__init__(**kwargs)

    default_error_messages = {
        "ltzero": "less than zero.",
        "eqzero": "eq zero"
    }

    def _validated(self, value):
        if not value and not self.required:
            return Decimal()
        try:
            num = super()._validated(value)
        except decimal.InvalidOperation as error:
            raise self.make_error("invalid") from error
        if not self.allow_nan and (num.is_nan() or num.is_infinite()):
            raise self.make_error("special")
        if num < Decimal():
            raise self.make_error("ltzero")
        if num == Decimal() and not self.allow_zero:
            raise self.make_error('eqzero')
        return num


class EnumField(mm_fields.String):

    def __init__(self, enum: Union[EnumMeta, Iterable[str]],
                 enum_by_value=False,
                 case_sensitive=True,
                 **kwargs):
        if isinstance(enum, EnumMeta):
            enum_class = enum
            self.enum_by_value = enum_by_value
            if not enum_by_value:
                choices = list_enum_names(enum)
            else:
                choices = list_enum_values(enum)
            choices_set = None
        else:
            enum_class = None
            choices = list(enum)
            choices_set = frozenset(choices)

        self._enum_class = enum_class
        self._choices = choices
        self._choices_set = choices_set
        self._case_sensitive = case_sensitive
        super().__init__(desc=f'choices={choices}', **kwargs)

    @classmethod
    def parse_value_enum(cls,
                         _value: str,
                         _enum_cls: EnumMeta,
                         _enum_by_value: bool,
                         _case_sensitive: bool) -> Type[EnumMeta]:
        if not _case_sensitive:
            parse_str_list = [_value, _value.upper(), _value.lower()]
        else:
            parse_str_list = [_value]
        parse_success = False
        parsed_enum = None
        for parse_str in parse_str_list:
            if _enum_by_value:
                try:
                    parsed_enum = _enum_cls(parse_str)
                    parse_success = True
                    break
                except ValueError:
                    continue
            else:
                if isinstance(
                        (parsed_enum := getattr(_enum_cls, parse_str, None)),
                        _enum_cls):
                    parse_success = True
                    break
        if not parse_success:
            raise InvalidArgument(
                        message=f'{_value!r} is not a valid {_enum_cls} value')
        return parsed_enum

    def _deserialize(self, value, attr, data, **kwargs
                     ) -> Union[Enum, str, None]:
        value = super()._deserialize(value, attr, data, **kwargs)
        if not self.required and not value:
            return None
        if (enum_cls := self._enum_class) is not None:
            value = self.parse_value_enum(
                value, enum_cls, self.enum_by_value, self._case_sensitive)
        elif value not in self._choices_set:
            raise InvalidArgument(message=f'invalid enum: {value!r}')
        return value


class BoolField(mm_fields.Boolean):

    def _deserialize(self, value, attr, data, **kwargs):
        if value in (None, ''):
            return None
        if not self.truthy:
            return bool(value)
        else:
            try:
                if value in self.truthy:
                    return True
                elif value in self.falsy:
                    return False
            except TypeError as error:
                raise self.make_error("invalid", input=value) from error
        raise self.make_error("invalid", input=value)


class IntEnumField(mm_fields.String):

    def __init__(self, enum: Union[EnumMeta, Iterable[str]], enum_by_value=False, **kwargs):
        if isinstance(enum, EnumMeta):
            enum_class = enum
            self.enum_by_value = enum_by_value
            if not enum_by_value:
                choices = list_enum_names(enum)
            else:
                choices = list_enum_values(enum)
            choices_set = None
        else:
            enum_class = None
            choices = list(enum)
            choices_set = frozenset(choices)

        self._enum_class = enum_class
        self._choices = choices
        self._choices_set = choices_set
        super().__init__(desc=f'choices={choices}', **kwargs)

    def _deserialize(self, value, attr, data, **kwargs
                     ) -> Union[Enum, str, None]:
        value = super()._deserialize(value, attr, data, **kwargs)
        if not self.required and not value:
            return None
        if (enum_cls := self._enum_class) is not None:
            if self.enum_by_value:
                try:
                    value = enum_cls(int(value))
                except ValueError:
                    raise InvalidArgument(
                        message=f'{value!r} is not a valid {enum_cls} value')
            else:
                v1 = getattr(enum_cls, value.upper(), None)
                v2 = getattr(enum_cls, value.lower(), None)
                if isinstance(v1, enum_cls):
                    return v1
                if isinstance(v2, enum_cls):
                    return v2
                raise InvalidArgument(
                    message=f'{value!r} is not a valid {enum_cls}')
        elif value not in self._choices_set:
            raise InvalidArgument(message=f'invalid enum: {value!r}')
        return value


class ClientIdField(mm_fields.String):

    def _deserialize(self, value, attr, data, **kwargs) -> typing.Any:
        value = super()._deserialize(value, attr, data, **kwargs)
        if not re.match('^[a-zA-Z0-9_\-]*$', value):
            raise self.make_error('invalid')
        if len(value) > 32:
            raise self.make_error('invalid')
        return value


class TimestampField(mm_fields.Number):

    def __init__(self, *, is_ms: bool = False, to_hour=False,
                 to_date=False, **kwargs):
        self.is_ms = is_ms
        self.to_hour = to_hour
        self.to_date = to_date
        super().__init__(is_ms=is_ms, **kwargs)

    def _deserialize(self, value, attr, data, **kwargs) -> Optional[datetime]:
        value = super()._deserialize(value, attr, data, **kwargs)
        if not self.required and not value:
            return None
        if self.is_ms:
            value = value // 1000
        if self.to_hour:
            value = value // 3600 * 3600
        value_dt = timestamp_to_datetime(value)
        return value_dt.date() if self.to_date else value_dt


class DateField(mm_fields.String):

    def __init__(self, to_date: bool = False, **kwargs):
        self.to_date = to_date
        super().__init__(**kwargs)

    def _deserialize(self, value, attr, data, **kwargs
                     ) -> Union[datetime, date, None]:
        value = super()._deserialize(value, attr, data, **kwargs)
        if not self.required and not value:
            return None
        dt = str_to_datetime(value)
        return dt if not self.to_date else dt.date()


class JSONField(mm_fields.String):

    def _deserialize(self, value, attr, data, **kwargs) -> Any:
        value = super()._deserialize(value, attr, data, **kwargs)
        if not self.required and not value:
            return None
        try:
            return json_loads(value)
        except ValueError:
            raise InvalidArgument(message=f'invalid JSON string: {value!r}')


class NoneMixin(object):
    def _deserialize(self, value, attr, data, **kwargs):
        if value == '':
            return None
        # noinspection PyUnresolvedReferences
        return super(NoneMixin, self)._deserialize(value, attr, data, **kwargs)


class CustomIntegerField(NoneMixin, mm_fields.Integer):
    pass
