from functools import wraps
from inspect import isclass
from typing import Union, Callable

from flask import Response, g, current_app
from flask.views import http_method_funcs as _http_method_funcs
from flask_api.exceptions import AuthenticationFailed

from app.api.common.requests import get_request_user_id
from app.api.common.responses import success
from app.cache.flow_control import FrequencyCache
from app.exceptions import FrequencyExceeded
from app.utils import func_to_str


def _decorate_class(dec, cls):
    cls_vars = vars(cls)
    for name in _http_method_funcs:
        func = cls_vars.get(name)
        if func is None:
            continue
        if isinstance(func, (classmethod, staticmethod)):
            func = type(func)(dec(func.__func__))
        else:
            func = dec(func)
        setattr(cls, name, func)
    return cls


def respond_with_code(func_or_class):
    if isclass(func_or_class):
        return _decorate_class(respond_with_code, func_or_class)

    @wraps(func_or_class)
    def wrapper(*args, **kwargs):
        resp = func_or_class(*args, **kwargs)
        if isinstance(resp, Response):
            return resp
        return success(resp)

    return wrapper


def respond_with_code_new_message(func_or_class):
    if isclass(func_or_class):
        return _decorate_class(respond_with_code_new_message, func_or_class)

    @wraps(func_or_class)
    def wrapper(*args, **kwargs):
        resp = func_or_class(*args, **kwargs)
        if isinstance(resp, Response):
            return resp
        return success(resp, message="OK")

    return wrapper


def limit_frequency(count: int, interval: int,
                    func_name: str = None,
                    arg_key: Union[str, Callable[..., str]] = None):
    def dec(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal func_name, arg_key

            _func_name = func_name or func_to_str(func)
            _arg_key = arg_key(*args) if callable(arg_key) else arg_key
            full_key = f'{_func_name}-{_arg_key}' if _arg_key else func_name

            cache = FrequencyCache(full_key, interval)
            if not cache.add_value_if_fewer_than(count):
                raise FrequencyExceeded
            return func(*args, **kwargs)

        return wrapper

    return dec


def limit_user_frequency(count: int, interval: int):
    return limit_frequency(count, interval, arg_key=lambda *args, **kwargs: str(get_request_user_id(must=True)))


def limit_multi_frequency(limit_rules: [(int, int)], func_name: str = None,
                          arg_key: Union[str, Callable[..., str]] = None):
    def dec(func):

        @wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal func_name, arg_key

            _func_name = func_name or func_to_str(func)
            _arg_key = arg_key(*args) if callable(arg_key) else arg_key

            # 依次应用每个规则
            frequency_exceeded = False
            for index, rule in enumerate(limit_rules):
                count, interval = rule

                func_key = f'{_func_name}-{_arg_key}' if _arg_key else func_name
                full_key = f'{func_key}-{index}'

                cache = FrequencyCache(full_key, interval)
                if not cache.add_value_if_fewer_than(count):  # 一个规则触发限频后仍要对其他规则计数
                    frequency_exceeded = True

            if frequency_exceeded:
                raise FrequencyExceeded
            return func(*args, **kwargs)

        return wrapper

    return dec


def limit_user_multi_frequency(limit_rules: [(int, int)]):
    return limit_multi_frequency(limit_rules, arg_key=lambda *args, **kwargs: str(get_request_user_id(must=True)))


def login_required(func_or_class=None):
    """
    Please put this decorator above any `ns.expect()`.
    """
    def dec(_func):

        @wraps(_func)
        def wrapper(*args, **kwargs):
            if get_request_user_id() is None:
                raise AuthenticationFailed

            return _func(*args, **kwargs)

        return wrapper

    if func_or_class is not None:
        return dec(func_or_class)

    return dec
