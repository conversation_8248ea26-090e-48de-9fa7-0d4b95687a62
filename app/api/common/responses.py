# -*- coding: utf-8 -*-
from typing import Any, Dict
from flask import make_response


def success(data: Any = None, message: str = 'Success'):
    if data is None:
        data = {}
    return dict(
        code=0,
        data=data,
        message=message or "成功"
    )


def failure(code: int, message='Failure', data: Any = None):
    if not code:
        raise ValueError('`code` must be non-zero')
    if data is None:
        data = {}
    return dict(
        code=code,
        data=data,
        message=message or "失败"
    )


def json_string_success(data: str, message: str = 'Success'):
    """json string to flask response"""
    message = message or "成功"
    res = f'{{"code":0,"data":{data},"message":"{message}"}}'
    return make_response(res, {'Content-Type': 'application/json'})


def api_v2_success(data: Dict, message: str = 'OK'):
    if data:
        return dict(
            **data,
            code=0,
            message=message or "成功"
        )
    return dict(data={}, code=0, message=message or "成功")
