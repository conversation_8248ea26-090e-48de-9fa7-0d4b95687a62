from datetime import tzinfo, timezone, timedelta
from decimal import Decimal
from enum import Enum
from typing import Optional
from flask import request
from app.common.constants import Language
from itertools import chain as chain_iter


def get_request_info() -> dict:
    return dict(
        path=request.path,
        method=request.method,
        ip=get_request_ip(),
        language=get_request_language().name,
        args=dict(request.args)
    )


def get_request_ip() -> str:
    headers = request.headers
    if 'Cf-Connecting-Ip' in headers:     # 来自CloudFlare
        return headers['Cf-Connecting-IP']
    if 'X-Real-IP' in headers:            # 来自nginx设置
        return headers['X-Real-IP']
    if 'X-Forwarded-For' in headers:      # 来自api_gateway等设置
        return headers['X-Forwarded-For'].split(',')[0]
    return request.remote_addr


def get_request_language() -> Language:
    for lang in chain_iter(request.accept_languages.values(),
                           [request.cookies.get('lang')]):
        if not lang:
            continue
        try:
            lang = Language(lang)
        except ValueError:
            continue
        break
    else:
        lang = Language.DEFAULT
    return lang


def get_request_user_id(must: bool = False) -> Optional[int]:
    user_id = request.headers.get('User-Id')
    if user_id and user_id.isdigit():
        return int(user_id)
    if must:
        raise ValueError('Missing User-Id')
    return None


def decode_utf8_to_latin(latin_str):
    # 请求头中的参数只能以latin编码传输，对端将utf8的字节流编码成latin编码传输，这里反向操作即可获取到原文
    return latin_str.encode('latin-1').decode()


def get_request_user_name(must: bool = False) -> Optional[str]:
    user_name = request.headers.get('User-Name')
    if user_name:
        return decode_utf8_to_latin(user_name)
    if must:
        raise ValueError('Missing User-Name')
    return None


def get_request_account_name(must: bool = False) -> Optional[str]:
    account_name = request.headers.get('Account-Name')
    if account_name:
        return decode_utf8_to_latin(account_name)
    if must:
        raise ValueError('Missing Account-Name')
    return None


def get_request_avatar(must: bool = False) -> Optional[str]:
    avatar = request.headers.get('Avatar')
    if avatar:
        return avatar
    if must:
        raise ValueError('Missing Avatar')
    return None


def get_request_timezone() -> Optional[tzinfo]:
    if (tz := request.headers.get('timezone')) is None:
        return None
    return timezone(timedelta(minutes=int(Decimal(tz) * 60)))


class RequestPlatform(Enum):

    WEB = 'web'
    IOS = 'iOS'
    IOS_APP_STORE = 'iOS_appstore'
    ANDROID = 'Android'
    ANDROID_GOOGLE_PLAY = 'Android_GooglePlay'
    # 原先的APP也被下架了，重新添加两个字段区分上架
    IOS_LITE = 'iOSLite'
    IOS_LITE_APP_STORE = 'iOSLite_appstore'

    def is_web(self):
        return self is self.WEB

    def is_ios(self):
        return self in [self.IOS, self.IOS_APP_STORE, self.IOS_LITE, self.IOS_LITE_APP_STORE]

    def is_android(self):
        return self in [self.ANDROID, self.ANDROID_GOOGLE_PLAY]

    def is_mobile(self):
        return self.is_ios() or self.is_android()

    @classmethod
    def app_list(cls):
        return [cls.IOS, cls.IOS_APP_STORE, cls.IOS_LITE, cls.IOS_LITE_APP_STORE, cls.ANDROID, cls.ANDROID_GOOGLE_PLAY]

    @classmethod
    def from_request(cls) -> 'RequestPlatform':
        platform = request.headers.get('PLATFORM', '')
        if platform:
            try:
                platform = RequestPlatform(platform)
            except ValueError:
                raise ValueError(f'InvalidPlatform: {platform}')
        else:
            platform = cls.WEB
        return platform


def get_request_platform() -> RequestPlatform:
    return RequestPlatform.from_request()


def get_request_version() -> str:
    return request.headers.get('version', '')


def get_request_build() -> int:
    return int(request.headers.get('BUILD', 0))