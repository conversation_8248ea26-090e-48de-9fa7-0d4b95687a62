from collections import defaultdict
from datetime import timedelta
from enum import Enum
from functools import partial
import re
from typing import List
from webargs import fields as wa_fields
from marshmallow import Schema, fields as mm_fields
from flask import g
from sqlalchemy import and_, desc, func, or_, not_
from sqlalchemy.orm import aliased

from app.api.common import Resource
from app.api.common import Namespace
from app.api.common.fields import EnumField, LimitField, PageField
from app.api.common.decorators import respond_with_code

from app.business.clients.ai_translate import AITranslateBusiness, AITranslateClient, ModelType
from app.business.comment_manager import CommentManager, CommentTranslateManager
from app.business.moderation_manager import CommentReportManager
from app.business.push import send_comment_warning_push, send_user_banned_push
from app.cache.comment_cache import CommentCache
from app.common.constants import ADMIN_EXPORT_LIMIT, Language, language_cn_names
from app.exceptions import InvalidArgument
from app.models.base import db
from app.models.comment import SCORE_COLS, Comment, CommentFullTextSearch, CommentTranslation, CommentWarning, CommentWarningTranslation
from app.models.moderation import CommentModeration, CommentReport, CommentReportReview, CommentUserStatus
from app.models.report import DailyCommentTipReport, MonthlyCommentTipReport
from app.models.statistics import CommentStatistics, CommentUserStatistics
from app.models.tip import CommentTip
from app.models.user import UserInfo
from app.utils.amount import amount_to_str
from app.utils.common import ReportType
from app.utils.date_ import now, datetime_to_utc8_str
from app.utils.export import export_xlsx
from app.utils.tokenization import cut_words


ns = Namespace('')

url_prefix = ""

def _format_content(text):
    # 定义正则表达式模式，匹配任意字符
    pattern = r'\{\{mention:([^}]+)\}\}'

    # 替换函数
    def replace_with_at(match):
        name = match.group(1)  # 获取匹配的用户ID
        return f'@{name}'  # 返回替换后的字符串
    # 使用re.sub进行替换
    result = re.sub(pattern, replace_with_at, text)
    return result

class CommentSchema(Schema):
    id = mm_fields.Int()
    content = mm_fields.Method("format_content")
    status = mm_fields.Enum(Comment.CommentStatus)
    lang = mm_fields.Enum(Language)
    business = mm_fields.Enum(Comment.Business)
    root_id = mm_fields.Int()
    parent_id = mm_fields.Int()
    business_id = mm_fields.Str()
    business_code = mm_fields.Str()
    up_count = mm_fields.Int()
    down_count = mm_fields.Int()
    report_count = mm_fields.Int()
    reply_count = mm_fields.Int()
    created_at = mm_fields.Raw()
    updated_at = mm_fields.Raw()
    remark = mm_fields.Str()

    class Meta:
        # Preserve datetime objects during serialization
        datetime_format = "iso"
        ordered = True

    def format_content(self, obj):
        return _format_content(obj.content)

comment_schema = CommentSchema()

@ns.route('/list')
@respond_with_code
class AdminCommentResource(Resource):
    """评论管理"""

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "business_code", Language.ZH_HANS_CN: "币种"},
        {"field": "lang", Language.ZH_HANS_CN: "语区"},
        {"field": "content", Language.ZH_HANS_CN: "内容"},
        {"field": "translated_content", Language.ZH_HANS_CN: "翻译内容"},
        {"field": "user_name", Language.ZH_HANS_CN: "用户名"},
        {"field": "user_account_name", Language.ZH_HANS_CN: "账户名"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "interaction_count", Language.ZH_HANS_CN: "互动总数"},
        {"field": "reply_count", Language.ZH_HANS_CN: "回复数"},
        {"field": "up_count", Language.ZH_HANS_CN: "赞同数"},
        {"field": "down_count", Language.ZH_HANS_CN: "反对数"},
        {"field": "report_count", Language.ZH_HANS_CN: "举报数"},
        {"field": "created_at", Language.ZH_HANS_CN: "发布时间"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "is_display", Language.ZH_HANS_CN: "是否展示"}
    )


    status_map = \
    {
        Comment.CommentStatus.PUBLISHED.name: "正常",
        Comment.CommentStatus.DISABLED.name: "违规",
        Comment.CommentStatus.DELETED.name: "已删除",
    }

    @classmethod
    def search_comments(cls, lang, text, limit):
        words = cut_words(lang, text)
        # 查询
        items = \
            CommentFullTextSearch.query.filter(CommentFullTextSearch\
                                               .ts_content.match(' '.join(words))).limit(limit).all()
        res = []
        for item in items:
            res.append(dict(
                id=item.comment_id,
                content=item.ts_content,
            ))
        return res

    @classmethod
    def search_users(cls, user_name, limit):
        infos = UserInfo.query.filter(
            UserInfo.name.like(f'%{user_name}%')
        ).with_entities(
            UserInfo.user_id,
            UserInfo.name,
            UserInfo.account_name
        ).limit(limit)
        res = []
        for item in infos:
            res.append(dict(
                user_id=item.user_id,
                user_name=item.name,
                user_account_name='@' + item.account_name
            ))
        return res


    @classmethod
    def _export(cls, comments):
        data = []
        lang_cn_name_map = language_cn_names()
        for item in comments:
            lang = Language[item['lang']]
            item['lang'] = lang_cn_name_map[lang]
            item['interaction_count'] = item['reply_count'] + item['up_count'] + item['down_count']
            item['is_display'] = '是' if item['is_display'] else '否'
            item['status'] = cls.status_map[item['status']]

            # Convert datetime fields to strings
            item['created_at'] = datetime_to_utc8_str(item['created_at'])
            item['updated_at'] = datetime_to_utc8_str(item['updated_at'])
            data.append(item)
        return export_xlsx('评论列表', data, cls.export_headers)

    @classmethod
    @ns.use_kwargs(dict(
        business=wa_fields.String(required=True),
        business_id=wa_fields.String,
        business_code=wa_fields.String,
        comment_id=wa_fields.Integer,
        status=EnumField(Comment.CommentStatus),
        lang=EnumField(Language),
        content=wa_fields.String,
        user_id=wa_fields.Integer,
        start_time=wa_fields.DateTime,
        end_time=wa_fields.DateTime,
        page=PageField,
        limit=LimitField,
        parent_id=wa_fields.Integer,
        root_id=wa_fields.Integer,
        is_reply=wa_fields.Boolean(missing=False),
        order_type=wa_fields.String,
        export=wa_fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """获取评论列表(管理)"""
        limit = kwargs.get('limit')
        lang = kwargs.get('lang')
        business = kwargs['business']
        business_id = kwargs.get('business_id')
        business_code = kwargs.get('business_code')
        status = kwargs.get('status')
        content = kwargs.get('content')
        user_id = kwargs.get('user_id')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        root_id = kwargs.get('root_id')
        parent_id = kwargs.get('parent_id')
        is_reply = kwargs['is_reply']
        query = Comment.query.filter(
            Comment.business == business,
            Comment.status != Comment.CommentStatus.CREATED
        ).outerjoin(
            CommentReportReview,
            and_(
                Comment.id == CommentReportReview.comment_id,
                CommentReportReview.status == CommentReportReview.Status.CREATED
            )
        ).filter(or_(
            Comment.status == Comment.CommentStatus.DELETED,
            CommentReportReview.comment_id.is_(None)))
        if business_id:
            query = query.filter(Comment.business_id == business_id)
        if business_code:
            query = query.filter(Comment.business_code == business_code)
        if status:
            query = query.filter(Comment.status == status)
        if lang:
            query = query.filter(Comment.lang == lang)
        if content and not lang:
            raise InvalidArgument(message='请选择语言后查询')
        if user_id:
            users = UserInfo.query.filter(UserInfo.user_id == user_id).with_entities(
                UserInfo.user_id,
                UserInfo.name,
                UserInfo.account_name
            ).all()
            user_ids = [item.user_id for item in users]
            query = query.filter(Comment.user_id.in_(user_ids))
        if root_id:
            query = query.filter(Comment.root_id == root_id)
        if parent_id:
            query = query.filter(Comment.parent_id == parent_id)
        if start_time:
            query = query.filter(Comment.created_at >= start_time)
        if end_time:
            query = query.filter(Comment.created_at <= end_time)
        if content:
            # 全文检索
            # query = query.join(CommentFullTextSearch, Comment.id == CommentFullTextSearch.comment_id)
            # words = cut_words(lang, content)
            # query = query.filter(CommentFullTextSearch.ts_content.match(' '.join(words)))

            query = query.filter(Comment.content.like(f'%{content}%'))
        if is_reply:
            query = query.filter(Comment.parent_id.isnot(None))
        else:
            query = query.filter(Comment.parent_id.is_(None))

        if order_type:= kwargs.get('order_type'):
            if order_type == 'report_count':
                query = query.order_by(Comment.report_count.desc())
        if id_:= kwargs.get('comment_id'):
            comment = CommentCache(id_).get_comment()
            if comment:
                id_ = comment.id if not comment.root_id else comment.root_id
                query = query.filter(Comment.id == id_)
            else:
                raise InvalidArgument(message=f'评论 {id_} 不存在')
        if not kwargs['export']:
            page, limit = kwargs['page'], kwargs['limit']
            comments = query.order_by(Comment.id.desc()).paginate(page=page, per_page=limit)
        else:
            comments = query.order_by(Comment.id.desc()).paginate(page=1, per_page=ADMIN_EXPORT_LIMIT)
        data = []

        user_ids = list({comment.user_id for comment in comments.items})
        user_info_map = UserInfo.get_user_info_map(user_ids)

        comment_ids = [comment.id for comment in comments.items]
        report_count_map = CommentReport.get_comment_report_count(comment_ids)
        translation_map = CommentTranslation.get_comment_translation_map(comment_ids, Language.ZH_HANS_CN)
        comment_statistics_map = CommentStatistics.get_comment_statistics_map(comment_ids)
        for comment in comments.items:
            comment: Comment
            result: dict = comment_schema.dump(comment)
            result['report_count'] = report_count_map.get(comment.id, 0)
            user_info = user_info_map.get(comment.user_id)
            result.update(
                dict(
                    user_id=comment.user_id,
                    user_name=user_info.name if user_info else '',
                    user_account_name='@' + user_info.account_name if user_info else '',
                )
            )
            is_display = result['status'] not in (Comment.CommentStatus.DISABLED.name,
                                                  Comment.CommentStatus.DELETED.name)
            result['is_display'] = is_display
            result['translated_content'] = translation_map.get(comment.id)
            stats = comment_statistics_map.get(comment.id, {})
            result.update(stats)
            data.append(result)
        if kwargs['export']:
            return cls._export(data)
        langs = language_cn_names()
        return dict(
            items=data,
            langs={k.name: v for k, v in langs.items()},
            page=comments.page,
            total=comments.total,
            status_map=cls.status_map
        )

    @classmethod
    @ns.use_kwargs(dict(
        comment_id=wa_fields.Integer(required=True),
        status=EnumField(Comment.CommentStatus),
        remark=wa_fields.String
    ))
    def put(cls, **kwargs):
        """评论管理操作"""
        comment_id = kwargs['comment_id']
        comment = Comment.query.get(comment_id)
        if not comment:
            raise InvalidArgument(message=f'{comment_id} 评论不存在')
        if remark := kwargs.get('remark'):
            comment.remark = remark
        if status := kwargs.get('status'):
            if status == Comment.CommentStatus.PUBLISHED:
                if comment.status != Comment.CommentStatus.PUBLISHED:
                    CommentManager.publish(comment_id)
            elif status == Comment.CommentStatus.DISABLED:
                if comment.status != Comment.CommentStatus.DISABLED:
                    CommentManager.disable(comment_id)
            elif status == Comment.CommentStatus.DELETED:
                CommentManager.delete_comment(comment_id)

        db.session.commit()


@ns.route('/review')
@respond_with_code
class AdminCommentReviewResource(Resource):
    """评论-待审核内容"""

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "lang", Language.ZH_HANS_CN: "语区"},
        # 这个字段叫“币种”并不合理，如新增其他业务应修改
        {"field": "business_code", Language.ZH_HANS_CN: "币种"}, 
        {"field": "content", Language.ZH_HANS_CN: "内容"},
        {"field": "translated_content", Language.ZH_HANS_CN: "翻译内容"},
        {"field": "user_name", Language.ZH_HANS_CN: "用户名"},
        {"field": "user_account_name", Language.ZH_HANS_CN: "账户名"},
        {"field": "report_count", Language.ZH_HANS_CN: "举报数"},
        {"field": "created_at", Language.ZH_HANS_CN: "发布时间"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "report_review_id", Language.ZH_HANS_CN: "审核ID"}
    )

    @classmethod
    def _export(cls, items):
        data = []
        lang_cn_name_map = language_cn_names()
        for item in items:
            item_dict = dict(
                id=item['id'],
                content=_format_content(item['content']),
                translated_content=item['translated_content'],
                lang=lang_cn_name_map[Language[item['lang']]],
                business_code=item['business_code'],
                user_id=item['user_id'],
                user_name=item['user_name'],
                user_account_name=item['user_account_name'],
                report_count=item['report_count'],
                created_at=datetime_to_utc8_str(item['created_at']) if item['created_at'] else '',
                remark=item['remark'],
                report_review_id=item['report_review_id']
            )
            data.append(item_dict)
        return export_xlsx('待审核评论列表', data, cls.export_headers)

    @classmethod
    @ns.use_kwargs(dict(
        business=wa_fields.String(required=True),
        business_id=wa_fields.String,
        business_code=wa_fields.String,
        comment_id=wa_fields.Integer,
        lang=EnumField(Language),
        content=wa_fields.String,
        user_id=wa_fields.Integer,
        start_time=wa_fields.DateTime,
        end_time=wa_fields.DateTime,
        page=PageField,
        limit=LimitField,
        export=wa_fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """评论-待审核内容"""
        limit = kwargs.get('limit')
        lang = kwargs.get('lang')
        business = kwargs['business']
        business_id = kwargs.get('business_id')
        business_code = kwargs.get('business_code')
        content = kwargs.get('content')
        user_id = kwargs.get('user_id')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        query = CommentReportReview.query.filter(
            CommentReportReview.status == CommentReportReview.Status.CREATED,
            Comment.status.not_in((Comment.CommentStatus.DELETED, Comment.CommentStatus.DISABLED))
        ).join(
            Comment, Comment.id == CommentReportReview.comment_id
        ).order_by(Comment.id.desc())
        if business:
            query = query.filter(Comment.business == business)
        if business_id:
            query = query.filter(Comment.business_id == business_id)
        if business_code:
            query = query.filter(Comment.business_code == business_code)
        if lang:
            query = query.filter(Comment.lang == lang)
        if content and not lang:
            raise InvalidArgument(message='请选择语言后查询')
        if user_id:
            users = UserInfo.query.filter(UserInfo.user_id == user_id).with_entities(
                UserInfo.user_id,
                UserInfo.name,
                UserInfo.account_name
            ).all()
            user_ids = [item.user_id for item in users]
            query = query.filter(Comment.user_id.in_(user_ids))
        if start_time:
            query = query.filter(Comment.created_at >= start_time)
        if end_time:
            query = query.filter(Comment.created_at <= end_time)
        if content:
            # query = query.join(CommentFullTextSearch, Comment.id == CommentFullTextSearch.comment_id)
            # words = cut_words(lang, content)
            # query = query.filter(CommentFullTextSearch.ts_content.match(' '.join(words)))
            query = query.filter(Comment.content.like(f'%{content}%'))
        if id_:= kwargs.get('comment_id'):
            comment = CommentCache(id_).get_comment()
            if comment:
                id_ = comment.id if not comment.root_id else comment.root_id
                query = query.filter(Comment.id == id_)
            else:
                raise InvalidArgument(message=f'评论 {id_} 不存在')
        query = query.with_entities(
            Comment.id,
            CommentReportReview.id.label("report_review_id"),
            Comment.content,
            Comment.business_code,
            Comment.user_id,
            Comment.report_count,
            Comment.created_at,
            Comment.remark,
            Comment.lang,
            Comment.root_id,
            Comment.parent_id
        )
        if not kwargs['export']:
            page, limit = kwargs['page'], kwargs['limit']
            comment_reviews = query.order_by(Comment.id.desc()).paginate(page=page, per_page=limit)
        else:
            comment_reviews = query.order_by(Comment.id.desc()).paginate(page=1, per_page=ADMIN_EXPORT_LIMIT)

        user_ids = list({item.user_id for item in comment_reviews.items})
        user_info_map = UserInfo.get_user_info_map(user_ids)
        res = []
        comment_ids = [item.id for item in comment_reviews.items]
        report_count_map = CommentReport.get_comment_report_count(comment_ids)
        translation_map = CommentTranslation.get_comment_translation_map(comment_ids, Language.ZH_HANS_CN)

        for item in comment_reviews.items:
            item: Comment
            user_info = user_info_map.get(item.user_id)
            data = \
                dict(
                    id=item.id,
                    content=_format_content(item.content),
                    business_code=item.business_code,
                    lang=item.lang.name,
                    user_id=item.user_id,
                    user_name=user_info.name if user_info else '',
                    user_account_name='@' + user_info.account_name if user_info else '',
                    report_count=report_count_map.get(item.id, 0),
                    created_at=item.created_at,
                    remark=item.remark,
                    root_id=item.root_id,
                    parent_id=item.parent_id,
                    report_review_id=item.report_review_id,
                    translated_content=translation_map.get(item.id)
                )
            res.append(data)

        if kwargs['export']:
            return cls._export(res)

        langs = language_cn_names()
        res.sort(key=lambda x: x['report_count'], reverse=True)
        return dict(
            items=res,
            langs={k.name: v for k, v in langs.items()},
            page=comment_reviews.page,
            total=comment_reviews.total,
        )

    @classmethod
    @ns.use_kwargs(dict(
        report_review_id=wa_fields.Integer(required=True),
        status=EnumField(CommentReportReview.Status, required=True),
        remark=wa_fields.String
    ))
    def put(cls, **kwargs):
        """评论-评论审核"""
        report_review_id = kwargs['report_review_id']
        record = CommentReportReview.query.get(report_review_id)
        if not record:
            raise InvalidArgument(message=f'{report_review_id} 记录不存在')
        comment_id = record.comment_id
        comment = Comment.query.get(comment_id)
        if remark := kwargs.get('remark'):
            comment.remark = remark
        status = kwargs['status']
        record.status = status
        if status == CommentReportReview.Status.DISABLED:
            CommentManager.disable(comment_id)
        db.session.commit()


@ns.route('/detail')
@respond_with_code
class AdminCommentDetailResource(Resource):


    @classmethod
    @ns.use_kwargs(dict(
        comment_id=wa_fields.Integer(required=True),
        root_id=wa_fields.Integer,
        parent_id=wa_fields.Integer,
        reply_page=PageField,
        reply_limit=LimitField
    ))
    def get(cls, **kwargs):
        """评论详情"""
        comment_id = kwargs['comment_id']
        comment = Comment.query.get(comment_id)
        if not comment:
            raise InvalidArgument(message=f'{comment_id} 评论不存在')
        comment: Comment
        user_info = UserInfo.query.filter(UserInfo.user_id == comment.user_id).with_entities(
            UserInfo.name,
            UserInfo.account_name
        ).first()


        detail: dict = comment_schema.dump(comment)
        warnings = CommentWarning.query.filter(
            CommentWarning.comment_id == comment_id
        ).with_entities(
            CommentWarning.created_at,
            CommentWarning.title,
            CommentWarning.content
        ).all()
        warnings = [dict(
            created_at=warning.created_at,
            title=warning.title,
            content=warning.content
        ) for warning in warnings]

        page, limit = kwargs['reply_page'], kwargs['reply_limit']

        id_ = comment.root_id or comment.id
        root_comment = Comment.query.get(id_)
        root_data = comment_schema.dump(root_comment)

        replies = Comment.query.filter(Comment.root_id == id_).order_by(desc(SCORE_COLS[Comment.SortType.TOP]),
                                                                        desc(Comment.id)).paginate(page=page, per_page=limit)
        comment_parent_map = {reply.id: reply.parent_id for reply in replies.items if reply.parent_id}
        parent_users = Comment.query.filter(Comment.id.in_(list(comment_parent_map.values()))).with_entities(
            Comment.id,
            Comment.user_id,
        ).all()
        parent_user_map = dict(parent_users)

        user_ids = list({reply.user_id for reply in replies.items})
        user_ids.extend({root_comment.user_id, comment.user_id})
        user_ids.extend(list(parent_user_map.values()))
        user_info_map = UserInfo.get_user_info_map(user_ids)

        info = user_info_map.get(comment.user_id)
        detail.update(
            dict(
                user_id=comment.user_id,
                user_name=info.name if info else '',
                user_account_name='@' + info.account_name if info else '',
            )
        )
        info = user_info_map.get(root_comment.user_id)
        root_data.update(
            dict(
                user_id=comment.user_id,
                user_name=info.name if info else '',
                user_account_name='@' + info.account_name if info else '',
            )
        )
        reply_comment_ids = [reply.id for reply in replies.items]
        report_count_map = CommentReport.get_comment_report_count([comment_id, root_comment.id] + reply_comment_ids)

        detail['report_count'] = report_count_map.get(comment_id, 0)
        root_data['report_count'] = report_count_map.get(root_comment.id, 0)

        stats_map = CommentStatistics.get_comment_statistics_map([comment_id, root_comment.id] + reply_comment_ids)
        detail.update(stats_map.get(comment_id, {}))
        root_data.update(stats_map.get(root_comment.id, {}))
        reply_comments = []
        for item in replies.items:
            item: Comment
            user_info = user_info_map.get(item.user_id)
            user_id = item.user_id
            user_name = user_info.name if user_info else ''
            user_account_name = user_info.account_name if user_info else ''

            comment_user_id = parent_user_map.get(item.parent_id, 0)
            comment_user_info = user_info_map.get(comment_user_id)
            comment_user_name = comment_user_info.name if comment_user_info else ''
            comment_user_account_name = comment_user_info.account_name if comment_user_info else ''

            d: dict = comment_schema.dump(item)
            d.update(
                dict(
                    user_id=user_id,
                    user_name=user_name,
                    user_account_name='@' + user_account_name,
                    report_count=report_count_map.get(item.id, 0),
                    comment_user_id=comment_user_id,
                    comment_user_name=comment_user_name,
                    comment_user_account_name='@' + comment_user_account_name
                )
            )
            d.update(stats_map.get(item.id, {}))
            reply_comments.append(d)
        langs = language_cn_names()
        return dict(
            data=detail,
            root_data=root_data,
            langs={k.name: v for k, v in langs.items()},
            warnings=warnings,
            replies=reply_comments,
            reply_total=replies.total
        )



@ns.route('/reports')
@respond_with_code
class AdminCommentReportsResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        comment_id=wa_fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        """评论举报列表"""
        comment_id = kwargs['comment_id']
        reports = CommentReport.query.filter(
            CommentReport.comment_id == comment_id
        ).with_entities(
            CommentReport.created_at,
            CommentReport.user_id,
            CommentReport.comment_user_id,
            CommentReport.type,
            CommentReport.reason
        ).order_by(CommentReport.id.desc()).paginate(page=1, per_page=500) # cannot be more than this right?

        user_ids = list({report.user_id for report in reports.items})
        user_ids.extend({report.comment_user_id for report in reports.items})
        user_info_map = UserInfo.get_user_info_map(user_ids)
        res = []
        stats_map = defaultdict(int)

        report_type_map = CommentReportManager.get_report_type(Language.ZH_HANS_CN)
        for v in report_type_map.values():
            stats_map[v] = 0
        for report in reports.items:
            user_info = user_info_map.get(report.user_id)
            type_ = report_type_map.get(report.type, report.type.name)
            res.append(dict(
                created_at=report.created_at,
                user_id=report.user_id,
                user_name=user_info.name if user_info else '',
                user_account_name='@' + user_info.account_name if user_info else '',
                type=type_,
                reason=report.reason if report.reason else ''
            ))
            stats_map[type_] += 1
        return dict(
            items=res,
            total=reports.total,
            statistics=stats_map,
            report_types={k.name: v for k, v in report_type_map.items()}
        )

@ns.route('/warning')
@respond_with_code
class AdminCommentWarningResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        comment_id=wa_fields.Integer(required=True),
        title=wa_fields.String(required=True),
        content=wa_fields.String(required=True),
        translated_title=wa_fields.String,
        translated_content=wa_fields.String,
        translated_lang=EnumField(Language)
    ))
    def post(cls, **kwargs):
        """评论-添加警告"""
        comment = Comment.query.filter(Comment.id == kwargs['comment_id']).with_entities(
            Comment.id,
            Comment.user_id,
        ).first()
        if not comment:
            raise InvalidArgument(message='评论不存在')
        record = CommentWarning(
            comment_id=comment.id,
            title=kwargs['title'],
            content=kwargs['content']
        )
        db.session.add(record)
        db.session.flush()
        if (translated_title:= kwargs.get('translated_title')) \
            and (translated_content:= kwargs.get('translated_content')):

            if not (lang:= kwargs.get('translated_lang')):
                raise InvalidArgument
            trans_record = CommentWarningTranslation(
                comment_warning_id=record.id,
                title=translated_title,
                content=translated_content,
                lang=lang
            )
            db.session.add(trans_record)
        db.session.commit()
        title = kwargs.get('translated_title') or kwargs['title']
        content = kwargs.get('translated_content') or kwargs['content']
        if lang:= kwargs.get('translated_lang'):
            warning_lang = lang.name
        else:
            warning_lang = None
        send_comment_warning_push.delay(comment.user_id, title, content, warning_lang)
        return dict(
            id=record.id,
            title=record.title,
            content=record.content,
            created_at=record.created_at
        )

@ns.route('/warning/translation')
@respond_with_code
class AdminCommentWarningTranslationResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        title=wa_fields.String(required=True),
        content=wa_fields.String(required=True),
        lang=EnumField(Language, required=True)
    ))
    def get(cls, **kwargs):
        """
        评论-警告翻译
        """
        lang = kwargs['lang']
        title = kwargs['title']
        content = kwargs['content']

        ai_translator = AITranslateClient()
        translate = partial(ai_translator.translate,
                            source=Language.ZH_HANS_CN,
                            target=lang,
                            model_type=ModelType.BALANCED,
                            business=AITranslateBusiness.COIN_COMMENT)
        try:
            title_resp = translate(content=title)
            content_resp = translate(content=content)
        except:
            raise InvalidArgument(message='翻译失败')
        return dict(
            title=title_resp.content,
            content=content_resp.content
        )

@ns.route('/moderation')
@respond_with_code
class AdminCommentModerationResource(Resource):
    """违规内容"""

    class Status(Enum):
        NORMAL = '正常'
        AI_REJECTED = '违规(AI)'
        MANUAL_REJECTED = '违规'

    REJECT_TYPE_MAP = {
        CommentModeration.RejectType.ABUSE: '辱骂',
        CommentModeration.RejectType.ILLEGAL: '违禁',
        CommentModeration.RejectType.OTHER: '其它(违规联系信息或者关键词)',
        CommentModeration.RejectType.VIOLENCE: '暴力',
        CommentModeration.RejectType.PORN: '色情',
        CommentModeration.RejectType.DISCRIMINATION: '种族歧视',

    }

    MODERATOR_TYPE_MAP = {
        CommentModeration.ModeratorType.KEYWORD: '关键词过滤器',
        CommentModeration.ModeratorType.AI: 'AI',
        CommentModeration.ModeratorType.MANUAL: '人工审核员',
    }

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "comment_id", Language.ZH_HANS_CN: "评论ID"},
        {"field": "lang", Language.ZH_HANS_CN: "语区"},
        {"field": "business_code", Language.ZH_HANS_CN: "币种"},
        {"field": "content", Language.ZH_HANS_CN: "内容"},
        {"field": "translated_content", Language.ZH_HANS_CN: "翻译内容"},
        {"field": "user_name", Language.ZH_HANS_CN: "用户名"},
        {"field": "user_account_name", Language.ZH_HANS_CN: "账户名"},
        {"field": "created_at", Language.ZH_HANS_CN: "发布时间"},
        {"field": "reject_type", Language.ZH_HANS_CN: "类型"},
        {"field": "reason", Language.ZH_HANS_CN: "原因"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        
    )

    @classmethod
    def _export(cls, items):
        data = []
        lang_cn_name_map = language_cn_names()
        for item in items:
            item_dict = dict(
                id=item['id'],
                comment_id=item['comment_id'],
                lang=lang_cn_name_map[Language[item['lang']]],
                business_code=item['business_code'],
                content=item['content'],
                translated_content=item['translated_content'],
                user_name=item['user_name'],
                user_account_name=item['user_account_name'],
                status=cls.Status[item['status']].value if item['status'] else '',
                moderator_type=cls.MODERATOR_TYPE_MAP.get(CommentModeration.ModeratorType[item['moderator_type']], '') if item['moderator_type'] else '',
                reject_type=cls.REJECT_TYPE_MAP.get(CommentModeration.RejectType[item['reject_type']], '') if item['reject_type'] else '',
                reason=item['reason'] or '',
                remark=item['remark'] or '',
                created_at=datetime_to_utc8_str(item['created_at']) if item['created_at'] else ''
            )
            data.append(item_dict)
        return export_xlsx('违规内容列表', data, cls.export_headers)

    @classmethod
    @ns.use_kwargs(dict(
        business=wa_fields.String,
        business_id=wa_fields.String,
        business_code=wa_fields.String,
        comment_id=wa_fields.Integer,
        status=EnumField(Status),
        lang=EnumField(Language),
        user_id=wa_fields.Integer,
        content=wa_fields.String,
        start_time=wa_fields.DateTime,
        end_time=wa_fields.DateTime,
        reject_type=EnumField(CommentModeration.RejectType),
        moderator_type=EnumField(CommentModeration.ModeratorType),
        page=PageField,
        limit=LimitField,
        is_reply=wa_fields.Boolean(missing=False),
        export=wa_fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """违规内容"""
        lang = kwargs.get('lang')
        business_id = kwargs.get('business_id')
        business_code = kwargs.get('business_code')
        status = kwargs.get('status')
        user_id = kwargs.get('user_id')
        start_time = kwargs.get('start_time')
        end_time = kwargs.get('end_time')
        moderator_type = kwargs.get('moderator_type')
        reject_type = kwargs.get('reject_type')

        content = kwargs.get('content')

        query = CommentModeration.query.filter(
            CommentModeration.status != CommentModeration.Status.PROCESSING,
            Comment.status != Comment.CommentStatus.DELETED,
            or_(
                CommentModeration.moderator_type == CommentModeration.ModeratorType.MANUAL,
                and_(
                    CommentModeration.moderator_type == CommentModeration.ModeratorType.AI,
                    CommentModeration.status == CommentModeration.Status.REJECTED
                )
            )
        ).join(
            Comment, Comment.id == CommentModeration.comment_id
        )
        if business_id:
            query = query.filter(Comment.business_id == business_id)
        if business_code:
            query = query.filter(Comment.business_code == business_code)
        if status:
            if status == cls.Status.NORMAL:
                query = query.filter(CommentModeration.status == CommentModeration.Status.APPROVED)
            elif status == cls.Status.AI_REJECTED:
                query = query.filter(CommentModeration.status == CommentModeration.Status.REJECTED,
                                     CommentModeration.moderator_type == CommentModeration.ModeratorType.AI)
            elif status == cls.Status.MANUAL_REJECTED:
                query = query.filter(CommentModeration.status == CommentModeration.Status.REJECTED,
                                     CommentModeration.moderator_type == CommentModeration.ModeratorType.MANUAL)
        if lang:
            query = query.filter(Comment.lang == lang)
        if moderator_type:
            query = query.filter(CommentModeration.moderator_type == moderator_type)
        if reject_type:
            query = query.filter(CommentModeration.rejected_type == reject_type)
        if user_id:
            users = UserInfo.query.filter(UserInfo.user_id == user_id).with_entities(
                UserInfo.user_id,
                UserInfo.name,
                UserInfo.account_name
            ).all()
            user_ids = [item.user_id for item in users]
            query = query.filter(Comment.user_id.in_(user_ids))
        if start_time:
            query = query.filter(Comment.created_at >= start_time)
        if end_time:
            query = query.filter(Comment.created_at <= end_time)
        if content:
            query = query.filter(Comment.content.like(f'%{content}%'))
        if id_:= kwargs.get('comment_id'):
            comment = CommentCache(id_).get_comment()
            if comment:
                id_ = comment.id if not comment.root_id else comment.root_id
                query = query.filter(Comment.id == id_)
            else:
                raise InvalidArgument(message=f'评论 {id_} 不存在')
        query = query.order_by(CommentModeration.id.desc())
        query = query.with_entities(
            CommentModeration.id,
            CommentModeration.reason,
            CommentModeration.status,
            CommentModeration.created_at,
            CommentModeration.moderator_type,
            CommentModeration.rejected_type,
            CommentModeration.remark,
            Comment.content,
            Comment.business_id,
            Comment.business_code,
            Comment.content,
            Comment.user_id,
            Comment.created_at,
            Comment.lang,
            Comment.id.label('comment_id'),
            Comment.root_id,
            Comment.parent_id
        )

        if not kwargs['export']:
            records = query.paginate(page=kwargs['page'], per_page=kwargs['limit'])
        else:
            records = query.paginate(page=1, per_page=ADMIN_EXPORT_LIMIT)

        res = []
        user_ids = list({record.user_id for record in records.items})
        user_info_map = UserInfo.get_user_info_map(user_ids)

        translation_map = CommentTranslation.get_comment_translation_map([item.comment_id for item in records.items], Language.ZH_HANS_CN)
        for item in records.items:
            status_str = ''
            if item.status == CommentModeration.Status.APPROVED:
                status_str = cls.Status.NORMAL.name
            elif item.status == CommentModeration.Status.REJECTED:
                if item.moderator_type == CommentModeration.ModeratorType.AI:
                    status_str = cls.Status.AI_REJECTED.name
                elif item.moderator_type == CommentModeration.ModeratorType.MANUAL:
                    status_str = cls.Status.MANUAL_REJECTED.name
            user_info = user_info_map.get(item.user_id)
            res.append(dict(
                id=item.id,
                comment_id=item.comment_id,
                user_id=item.user_id,
                user_name=user_info.name if user_info else '',
                user_account_name='@' + user_info.account_name if user_info else '',
                reason=item.reason,
                remark=item.remark,
                moderator_type=item.moderator_type.name,
                reject_type=item.rejected_type.name if item.rejected_type else '',
                status=status_str,
                created_at=item.created_at,
                business_id=item.business_id,
                business_code=item.business_code,
                content=_format_content(item.content),
                lang=item.lang.name,
                root_id=item.root_id,
                parent_id=item.parent_id,
                translated_content=translation_map.get(item.comment_id)
            ))

        if kwargs['export']:
            return cls._export(res)

        langs = language_cn_names()
        return dict(
            items=res,
            langs={k.name: v for k, v in langs.items()},
            status_map={item.name: item.value for item in cls.Status},
            moderator_type_map={k.name: v for k, v in cls.MODERATOR_TYPE_MAP.items()},
            reject_type_map={k.name: v for k, v in cls.REJECT_TYPE_MAP.items()},
            page=records.page,
            total=records.total
        )

    @classmethod
    @ns.use_kwargs(dict(
        comment_moderation_ids=wa_fields.List(wa_fields.Integer, required=True),
        status=EnumField(CommentModeration.Status, required=True),
        remark=wa_fields.String
    ))
    def put(cls, **kwargs):
        """
        违规内容审核/批量审核
        """

        ids = kwargs['comment_moderation_ids']
        records = CommentModeration.query.filter(CommentModeration.id.in_(ids)).all()
        comment_ids = [record.comment_id for record in records]
        comments = Comment.query.filter(Comment.id.in_(comment_ids)).all()
        comment_map = {comment.id: comment for comment in comments}

        for record in records:
            record: CommentModeration
            record.status = kwargs['status']
            record.moderator_type = CommentModeration.ModeratorType.MANUAL
            record.operator_id = g.user_id
            comment = comment_map[record.comment_id]
            if kwargs['status'] == CommentModeration.Status.APPROVED:
                if comment.status != Comment.CommentStatus.PUBLISHED:
                    CommentManager.publish(comment)
            elif kwargs['status'] == CommentModeration.Status.REJECTED:
                if comment.status != Comment.CommentStatus.DISABLED:
                    CommentManager.disable(comment)
            else:
                pass
            if remark := kwargs.get('remark'):
                record.remark = remark
        db.session.commit()


@ns.route('/user')
@respond_with_code
class AdminUserResource(Resource):

    Duration = CommentUserStatus.BanDuration
    Reason = CommentUserStatus.BanReason

    DURATION_MAP = {
        Duration.TEN_DAYS: '10天',
        Duration.THIRTY_DAYS: '30天',
        Duration.SIXTY_DAYS: '60天',
        Duration.ONE_EIGHTY_DAYS: '180天',
        Duration.THREE_SIXTY_FIVE_DAYS: '365天',
        Duration.FOREVER: '永久',
    }

    REASON_MAP = {
        Reason.FRAUD: '欺诈/诈骗',
        Reason.MALICIOUS: '恶意/消极',
        Reason.SPAM: '垃圾消息',
        Reason.FAKE: '虚假互动',
    }

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True),
    ))
    def get(cls, **kwargs):
        """用户信息"""
        record = CommentUserStatus.query.filter(CommentUserStatus.user_id == kwargs['user_id']).first()

        name = account_name = ''
        info = UserInfo.query.filter(UserInfo.user_id == kwargs['user_id']).first()
        if info:
            name = info.name
            account_name = info.account_name

        reason_str = ''
        if record and record.banned_reason:
            reasons = record.banned_reason.split(',')
            reason_str = ', '.join([cls.REASON_MAP[CommentUserStatus.BanReason[item]] for item in reasons])
        return dict(
            user_id=kwargs['user_id'],
            banned=record.banned if record else False,
            banned_until=record.banned_until if record else None,
            banned_reason=reason_str,
            ban_duration=record.ban_duration if record else None,
            duration_map={k.name: v for k, v in cls.DURATION_MAP.items()},
            reason_map={k.name: v for k, v in cls.REASON_MAP.items()},
            name=name,
            account_name=account_name
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True),
        status=wa_fields.Boolean,
        banned_duration=EnumField(CommentUserStatus.BanDuration),
        banned_reason=wa_fields.List(EnumField(CommentUserStatus.BanReason)),
    ))
    def put(cls, **kwargs):
        """用户禁言"""
        user_record: CommentUserStatus = CommentUserStatus.get_or_create(user_id=kwargs['user_id'])
        is_banned = kwargs.get('status')
        duration = kwargs.get('banned_duration')
        reason = kwargs.get('banned_reason')

        if is_banned is not None:
            if is_banned and (not duration or not reason):
                raise InvalidArgument(message='禁言需设置原因及时长')
            user_record.banned = is_banned

            if is_banned:
                user_record.ban_duration = duration
                user_record.banned_reason = ','.join([item.name for item in reason])
                if duration == CommentUserStatus.BanDuration.FOREVER:
                    user_record.banned_until = None
                else:
                    user_record.banned_until = now() + timedelta(days=duration.value)
            else:
                user_record.banned_until = None
                user_record.ban_duration = None
                user_record.banned_reason = None
        reason_str = ''
        if user_record.banned_reason:
            reasons = user_record.banned_reason.split(',')
            reason_str = ', '.join([cls.REASON_MAP[CommentUserStatus.BanReason[item]] for item in reasons])

        db.session_add_and_commit(user_record)
        if is_banned:
            send_user_banned_push.delay(kwargs['user_id'], duration.name, user_record.banned_reason)
        return dict(
            user_id=kwargs['user_id'],
            banned_until=user_record.banned_until,
            is_banned=is_banned,
            banned_reason=reason_str,
        )



@ns.route('/stats')
@respond_with_code
class AdminStatsResource(Resource):
    """评论统计"""

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "ID"},
        {"field": "user_name", Language.ZH_HANS_CN: "用户名"},
        {"field": "user_account_name", Language.ZH_HANS_CN: "账户名"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "comment_count", Language.ZH_HANS_CN: "评论数"},
        {"field": "reply_count", Language.ZH_HANS_CN: "收到回复"},
        {"field": "up_count", Language.ZH_HANS_CN: "赞同数"},
        {"field": "down_count", Language.ZH_HANS_CN: "反对数"},
        {"field": "send_tip_count", Language.ZH_HANS_CN: "发打赏次数"},
        {"field": "send_tip_user_count", Language.ZH_HANS_CN: "发打赏人数"},
        {"field": "send_tip_amount", Language.ZH_HANS_CN: "发打赏总金额(CET)"},
        {"field": "receive_tip_count", Language.ZH_HANS_CN: "收打赏次数"},
        {"field": "receive_tip_user_count", Language.ZH_HANS_CN: "收打赏人数"},
        {"field": "receive_tip_amount", Language.ZH_HANS_CN: "收打赏总金额(CET)"},
        {"field": "report_count", Language.ZH_HANS_CN: "被举报数"},
        {"field": "warning_count", Language.ZH_HANS_CN: "警告次数"},
        {"field": "block_count", Language.ZH_HANS_CN: "违规次数"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间"},
    )

    @classmethod
    def _export(cls, items):
        data = []
        for item in items:
            ban_duration_str = ''
            if item['status'] and item['ban_duration']:
                duration = CommentUserStatus.BanDuration[item['ban_duration']]
                if duration == CommentUserStatus.BanDuration.FOREVER:
                    ban_duration_str = '(永久)'
                else:
                    ban_duration_str = f'({duration.value}天)'

            item_dict = dict(
                id=item['id'],
                user_id=item['user_id'],
                user_name=item['user_name'],
                user_account_name=item['user_account_name'],
                status='正常' if not item['status'] else '禁言' + ban_duration_str,
                comment_count=item['comment_count'],
                up_count=item['up_count'],
                down_count=item['down_count'],
                report_count=item['report_count'],
                reply_count=item['reply_count'],
                warning_count=item['warning_count'],
                block_count=item['block_count'],
                created_at=datetime_to_utc8_str(item['created_at']) if item['created_at'] else '',
                updated_at=datetime_to_utc8_str(item['updated_at']) if item['updated_at'] else '',
                ban_duration=item['ban_duration'] or '',
                send_tip_count=item['send_tip_count'],
                send_tip_user_count=item['send_tip_user_count'],
                send_tip_amount=amount_to_str(item['send_tip_amount']),
                receive_tip_count=item['receive_tip_count'],
                receive_tip_user_count=item['receive_tip_user_count'],
                receive_tip_amount=amount_to_str(item['receive_tip_amount']),
            )
            data.append(item_dict)
        return export_xlsx('用户评论统计', data, cls.export_headers)

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer,
        status=wa_fields.Boolean,
        page=PageField,
        limit=LimitField,
        order=wa_fields.String(missing='comment_count'),
        descending=wa_fields.Boolean(missing=True),
        export=wa_fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """获取评论统计数据"""
        query = CommentUserStatistics.query
        if user_id:= kwargs.get('user_id'):
            query = query.join(UserInfo,
                               CommentUserStatistics.user_id == UserInfo.user_id).filter(
                                   UserInfo.user_id == user_id)
        if (status := kwargs.get('status')) is not None:
            if status is False:  # not banned
                query = query.outerjoin(
                    CommentUserStatus,
                    CommentUserStatistics.user_id == CommentUserStatus.user_id
                ).filter(
                    or_(
                        CommentUserStatus.banned.is_(False),  # 只选择未被禁言的用户
                        CommentUserStatus.user_id.is_(None)   # 选择不存在的用户
                    )
                )
            else:
                query = query.join(
                    CommentUserStatus,
                    CommentUserStatistics.user_id == CommentUserStatus.user_id
                ).filter(
                    CommentUserStatus.banned.is_(True)  # 只选择被禁言的用户
                )
        order = kwargs['order']
        if kwargs['descending']:
            query = query.order_by(getattr(CommentUserStatistics, order).desc())
        else:
            query = query.order_by(getattr(CommentUserStatistics, order).asc())
        query = query.with_entities(
            CommentUserStatistics.id,
            CommentUserStatistics.user_id,
            CommentUserStatistics.comment_count,
            CommentUserStatistics.up_count,
            CommentUserStatistics.down_count,
            CommentUserStatistics.report_count,
            CommentUserStatistics.reply_count,
            CommentUserStatistics.warning_count,
            CommentUserStatistics.block_count,
            CommentUserStatistics.send_tip_count,
            CommentUserStatistics.send_tip_user_count,
            CommentUserStatistics.send_tip_amount,
            CommentUserStatistics.receive_tip_count,
            CommentUserStatistics.receive_tip_user_count,
            CommentUserStatistics.receive_tip_amount,
            CommentUserStatistics.created_at,
            CommentUserStatistics.updated_at,
        )

        if not kwargs['export']:
            records = query.paginate(page=kwargs['page'], per_page=kwargs['limit'])
        else:
            records = query.paginate(page=1, per_page=ADMIN_EXPORT_LIMIT)

        user_ids = [comment.user_id for comment in records.items]
        user_info_map = UserInfo.get_user_info_map(user_ids)
        user_statuses = CommentUserStatus.query.filter(CommentUserStatus.user_id.in_(user_ids)).with_entities(
            CommentUserStatus.user_id,
            CommentUserStatus.banned,
            CommentUserStatus.ban_duration
        ).all()
        user_status_map = {user.user_id: user.banned for user in user_statuses}
        ban_duration_map = {user.user_id: user.ban_duration for user in user_statuses}
        res = []
        for item in records.items:
            status = user_status_map.get(item.user_id, False)
            user_info = user_info_map.get(item.user_id)
            ban_duration = ban_duration_map.get(item.user_id)
            res.append(dict(
                id=item.id,
                user_id=item.user_id,
                user_name=user_info.name if user_info else '',
                user_account_name='@' + user_info.account_name if user_info else '',
                status=status,
                comment_count=item.comment_count,
                up_count=item.up_count,
                down_count=item.down_count,
                report_count=item.report_count,
                reply_count=item.reply_count,
                warning_count=item.warning_count,
                block_count=item.block_count,
                send_tip_count=item.send_tip_count or 0,
                send_tip_user_count=item.send_tip_user_count or 0,
                send_tip_amount=item.send_tip_amount or 0,
                receive_tip_count=item.receive_tip_count or 0,
                receive_tip_user_count=item.receive_tip_user_count or 0,
                receive_tip_amount=item.receive_tip_amount or 0,
                created_at=item.created_at,
                updated_at=item.updated_at,
                ban_duration=ban_duration.name if ban_duration else None
            ))

        if kwargs['export']:
            return cls._export(res)

        return dict(
            items=res,
            page=records.page,
            total=records.total
        )


@ns.route('/user/search')
@respond_with_code
class UserSearchResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_name=wa_fields.String(required=True),
        limit=wa_fields.Integer(missing=10)
    ))
    def get(cls, **kwargs):
        """用户模糊搜索"""
        user_name = kwargs['user_name']
        user_name = user_name.strip()
        user_name = user_name.lstrip('@')
        limit = kwargs['limit']
        infos = UserInfo.query.filter(
            UserInfo.account_name.like(f'%{user_name}%')
        ).with_entities(
            UserInfo.user_id,
            UserInfo.name,
            UserInfo.account_name
        ).limit(limit)
        res = []
        for item in infos:
            res.append(dict(
                user_id=item.user_id,
                user_name=item.name,
                user_account_name='@' + item.account_name
            ))
        return dict(
            items=res
        )



@ns.route('/moderation/detail')
@respond_with_code
class AdminCommentModerationDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        comment_id=wa_fields.Integer(required=True)
    ))
    def post(cls, **kwargs):
        """获取评论的详细AI审核结果"""
        from app.business.moderation_manager import ModerationManager
        comment = Comment.query.get(kwargs['comment_id'])
        if not comment:
            raise InvalidArgument()


        result = ModerationManager.get_moderation_detail(comment)

        reject_type = result.get('reject_type')
        if isinstance(reject_type, CommentModeration.RejectType):
            reject_type = reject_type.name

        return dict(
            passed=result['passed'],
            reason=result.get('reason'),
            reject_type=reject_type,
        )


@ns.route('/translate')
@respond_with_code
class CommentTranslateResource(Resource):
    """翻译评论"""
    @classmethod
    @ns.use_kwargs(dict(
        comment_id=wa_fields.Integer(required=True),
        target=EnumField(Language, required=True),
    ))
    def post(cls, **kwargs):
        """翻译评论"""

        current_lang = getattr(g, 'lang', 'en_US')
        comment_id = kwargs['comment_id']
        target_lang = kwargs['target']

        CommentTranslation.query.filter(
            CommentTranslation.comment_id == comment_id,
            CommentTranslation.lang == target_lang.name
        ).delete()
        db.session.commit()
        target_lang_comment = (CommentTranslateManager.translate_comment(comment_id,
                                                                         target_lang,
                                                                         current_lang=current_lang))
        return {
            "content": target_lang_comment
        }


@ns.route('/tips')
@respond_with_code
class CommentTipsResource(Resource):
    """打赏记录"""

    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "打赏ID"},
        {"field": "comment_id", Language.ZH_HANS_CN: "内容ID"},
        {"field": "type", Language.ZH_HANS_CN: "内容类型"},
        {"field": "business_code", Language.ZH_HANS_CN: "币种"},
        {"field": "lang", Language.ZH_HANS_CN: "语区"},
        {"field": "content", Language.ZH_HANS_CN: "内容"},
        {"field": "send_user", Language.ZH_HANS_CN: "发打赏用户"},
        {"field": "send_user_id", Language.ZH_HANS_CN: "发打赏用户ID"},
        {"field": "receive_user", Language.ZH_HANS_CN: "收打赏用户"},
        {"field": "receive_user_id", Language.ZH_HANS_CN: "收打赏用户ID"},
        {"field": "tip_amount", Language.ZH_HANS_CN: "收打赏数量"},
        {"field": "created_at", Language.ZH_HANS_CN: "打赏时间"}
    )

    @classmethod
    def _export(cls, items):
        data = []
        lang_cn_name_map = language_cn_names()
        for item in items:
            lang = Language[item['lang']]
            item['lang'] = lang_cn_name_map[lang]
            item['created_at'] = datetime_to_utc8_str(item['created_at'])
            item['send_user'] = f"{item['send_user_info']['user_name']}{item['send_user_info']['user_account_name']}"
            item['send_user_id'] = item['send_user_info']['user_id']
            item['receive_user'] = f"{item['receive_user_info']['user_name']}{item['receive_user_info']['user_account_name']}"
            item['receive_user_id'] = item['receive_user_info']['user_id']
            item['tip_amount'] = f"{amount_to_str(item['amount'])} {item['asset']}"
            item['type'] = '回复' if item['parent_id'] else '评论'
            data.append(item)
        return export_xlsx('评论打赏记录', data, cls.export_headers)

    @classmethod
    @ns.use_kwargs(dict(
        business=wa_fields.String(required=True),
        business_code=wa_fields.String,
        lang=EnumField(Language),
        user_id=wa_fields.Integer,
        comment_id=wa_fields.Integer,
        start_time=wa_fields.DateTime,
        end_time=wa_fields.DateTime,
        page=PageField,
        limit=LimitField,
        is_reply=wa_fields.Boolean,
        export=wa_fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """打赏记录列表"""
        query = CommentTip.query.join(
            Comment, Comment.id == CommentTip.comment_id
        ).order_by(CommentTip.id.desc())
        query = query.filter(Comment.business == kwargs['business'])
        if business_code := kwargs.get('business_code'):
            query = query.filter(Comment.business_code == business_code)
        if lang := kwargs.get('lang'):
            query = query.filter(Comment.lang == lang.name)
        if user_id := kwargs.get('user_id'):
            query = query.filter(Comment.user_id == user_id)
        if start_time := kwargs.get('start_time'):
            query = query.filter(CommentTip.created_at >= start_time)
        if end_time := kwargs.get('end_time'):
            query = query.filter(CommentTip.created_at <= end_time)
        if (is_reply := kwargs.get('is_reply')) is not None:
            if is_reply:
                query = query.filter(Comment.parent_id.isnot(None))
            else:
                query = query.filter(Comment.parent_id.is_(None))
        if comment_id := kwargs.get('comment_id'):
            query = query.filter(Comment.id == comment_id)
        query = query.with_entities(
            Comment.id.label('comment_id'),
            Comment.parent_id,
            Comment.business_code,
            Comment.content,
            Comment.lang,
            CommentTip.id,
            CommentTip.created_at,
            CommentTip.amount,
            CommentTip.asset,
            CommentTip.send_user_id,
            CommentTip.receive_user_id,
            CommentTip.amount,
            CommentTip.asset,
            CommentTip.comment_id,
        )
        if not kwargs['export']:
            page, limit = kwargs['page'], kwargs['limit']
            records = query.paginate(page=page, per_page=limit)
        else:
            records = query.paginate(page=1, per_page=ADMIN_EXPORT_LIMIT)
        
        res = []
        user_ids = [item.send_user_id for item in records.items] + [item.receive_user_id for item in records.items]
        user_info_map = UserInfo.get_user_info_map(user_ids)
        for item in records.items:
            send_user_info = user_info_map.get(item.send_user_id)
            receive_user_info = user_info_map.get(item.receive_user_id)
            send_user_info = \
            dict(user_id=item.send_user_id,
                 user_name=send_user_info.name if send_user_info else '',
                 user_account_name='@' + send_user_info.account_name if send_user_info else '')
            receive_user_info = \
            dict(user_id=item.receive_user_id,
                 user_name=receive_user_info.name if receive_user_info else '',
                 user_account_name='@' + receive_user_info.account_name if receive_user_info else '')
            res.append(dict(
                id=item.id,
                created_at=item.created_at,
                amount=item.amount,
                asset=item.asset,
                send_user_id=item.send_user_id,
                receive_user_id=item.receive_user_id,
                send_user_info=send_user_info,
                receive_user_info=receive_user_info,
                comment_id=item.comment_id,
                parent_id=item.parent_id,
                business_code=item.business_code,
                content=_format_content(item.content),
                lang=item.lang.name
            ))
        
        if kwargs['export']:
            return cls._export(res)
        langs = language_cn_names()
        return dict(
            items=res,
            langs={k.name: v for k, v in langs.items()},
            page=records.page,
            total=records.total
        )

@ns.route('/tip-report')
@respond_with_code
class CommentTipsReportResource(Resource):
    """打赏报表"""

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=wa_fields.Date,
        end_date=wa_fields.Date,
        page=PageField,
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """打赏报表"""
        report_type = kwargs['report_type']
        page, limit = kwargs['page'], kwargs['limit']
        if report_type == ReportType.DAILY:
            model = DailyCommentTipReport
        else:
            model = MonthlyCommentTipReport
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        query = model.query
        if start_date:
            query = query.filter(model.report_date >= start_date)
        if end_date:
            query = query.filter(model.report_date <= end_date)
        records = query.order_by(model.report_date.desc()).paginate(page=page, per_page=limit)
        return dict(
            items=records.items,
            page=records.page,
            total=records.total
        )