# -*- coding: utf-8 -*-
from flask import g

from app.business.celery import (get_all_celery_queues,
                         get_celery_queue, delete_celery_queue)
from ..common import (Resource, Namespace, respond_with_code)
from ..common.fields import LimitField


ns = Namespace('Celery')


@ns.route('/redis-queues')
@respond_with_code
class CeleryQueuesResource(Resource):

    @classmethod
    def get(cls):
        """评论系统-Redis队列"""
        return [dict(
            name=name,
            length=length
        ) for name, length in sorted(get_all_celery_queues().items(),
                                     key=lambda x: -x[1])]


# noinspection PyUnresolvedReferences
@ns.route('/redis-queues/<queue>')
@respond_with_code
class CeleryQueueResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        limit=LimitField(missing=100)
    ))
    def get(cls, queue, **kwargs):
        """评论系统-Redis队列详情"""
        tasks, tasks_with_params = get_celery_queue(queue)
        total_tasks = len(tasks)
        total_tasks_with_params = len(tasks_with_params)

        tasks = [dict(
            task=task,
            count=count
        ) for task, count in sorted(tasks.items(), key=lambda x: -x[1])]
        tasks_with_params = [dict(
            task=task,
            count=count
        ) for task, count in sorted(tasks_with_params.items(),
                                    key=lambda x: -x[1])]

        limit = kwargs['limit']
        del tasks[limit:]
        del tasks_with_params[limit:]

        return dict(
            tasks=tasks,
            tasks_with_params=tasks_with_params,
            total_tasks=total_tasks,
            total_tasks_with_params=total_tasks_with_params
        )

    @classmethod
    def delete(cls, queue):
        """评论系统-删除Redis队列"""
        result = delete_celery_queue(queue)
        return result
