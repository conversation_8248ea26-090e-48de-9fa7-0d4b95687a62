from flask import g

from app.api.common.requests import get_request_user_id
from app.api.common.requests import get_request_language

url_prefix = '/admin'

admin_white_resource_list = [
    ('/admin/information/push/<information_id>', ['GET']),
]

admin_white_resource_set = {
    f'{method.upper()}:{rule}'
    for rule, methods in admin_white_resource_list for method in methods
}


def is_admin_white_resource(rule: str, method: str) -> bool:
    return f'{method.upper()}:{rule}' in admin_white_resource_set


def before_request():
    g.lang = get_request_language().value
    g.user_id = get_request_user_id()
