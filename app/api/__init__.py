# -*- coding: utf-8 -*-

from importlib import import_module
from pkgutil import walk_packages

from flask import Flask, Blueprint, make_response, request
from flask_restx import Namespace
from pygtrie import StringTrie
from .common import Api
from . import internal
from . import v1
from .import admin

import json

from .common.base import ApiJsonEncoder

_before_request = StringTrie(separator='/')


def init_app(app: Flask):

    def output_json(data, code, headers=None):
        _resp = make_response(json.dumps(data, cls=ApiJsonEncoder), code)
        _resp.headers.extend(headers or {})
        return _resp

    def _get_url_prefix(mod, mod_name):
        prefix = getattr(mod, 'url_prefix', None)
        if prefix is None:
            return f'/{mod_name.replace("_", "-")}'
        return prefix

    def import_sub_mods(_mod, _url_prefix, _api):
        for _, _mod_name, _is_pkg in walk_packages(_mod.__path__):
            _sub_mod = import_module(f'{_mod.__name__}.{_mod_name}')
            _sub_url_prefix = _get_url_prefix(_sub_mod, _mod_name)
            _sub_url_prefix_full = f'{_url_prefix}{_sub_url_prefix}'
            _ns = getattr(_sub_mod, 'ns', None)
            if _ns is not None:
                _api.add_namespace(_ns, path=_sub_url_prefix_full)

            if _is_pkg:
                # noinspection PyTypeChecker
                import_sub_mods(_sub_mod, _sub_url_prefix_full, _api)

    for mod in (v1, admin, internal):
        mod_name = mod.__name__.split('.')[-1]
        url_prefix = _get_url_prefix(mod, mod_name)

        bp = Blueprint(mod_name, __name__, url_prefix=url_prefix)
        api = Api(bp)

        ns = Namespace(mod_name)

        api.add_namespace(ns)
        app.register_blueprint(bp)

        before_request = getattr(mod, 'before_request', None)
        if callable(before_request):
            _before_request[url_prefix] = before_request

        mod.api = api
        mod.ns = ns

        api.representation('application/json')(output_json)
        # noinspection PyTypeChecker
        import_sub_mods(mod, '', api)

    @app.before_request
    def app_before_request():
        _handler = _before_request.longest_prefix(request.full_path).value
        if not _handler:
            return
        _handler()
