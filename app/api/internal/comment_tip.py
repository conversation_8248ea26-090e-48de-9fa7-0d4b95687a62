
from flask import current_app, g
from app.api.common import Resource
from app.api.common import Namespace, mm_fields
from app.api.common.decorators import respond_with_code

from app.business.comment_manager import CommentManager
from app.business.comment_tip import CommentTipManager
from app.business.user import UserManager
from app.exceptions.basic import InvalidArgument
from app.models.comment import Comment

ns = Namespace('CommentTip', description='评论打赏相关内部接口')

@ns.route('/tip')
@respond_with_code
class CommentTipResource(Resource):
    """打赏记录"""
    
    @classmethod
    @ns.use_kwargs(dict(
        comment_id=mm_fields.Integer(required=True),
        send_user_id=mm_fields.Integer(required=True),
        send_user_name=mm_fields.String,
        send_user_account_name=mm_fields.String,
        send_user_avatar=mm_fields.String,
        receive_user_id=mm_fields.Integer(required=True),
        amount=mm_fields.Decimal(required=True),
        price=mm_fields.Decimal(required=True),
        asset=mm_fields.String(required=True),
        asset_id=mm_fields.Integer(required=True),
        balance_transfer_id=mm_fields.Integer(required=True),
    ))
    def post(cls, **kwargs):
        """新增打赏记录
            backend 端成功完成打赏资产变更的，才会调用这个接口
        """
        comment_id = kwargs['comment_id']
        send_user_id = kwargs['send_user_id']
        receive_user_id = kwargs['receive_user_id']
        price = kwargs['price']
        asset = kwargs['asset']
        asset_id = kwargs['asset_id']
        amount = kwargs['amount']
        balance_transfer_id = kwargs['balance_transfer_id']

        comment = CommentManager.get_valid_comment(comment_id, from_cache=True, raise_err=False)
        if not comment:
            current_app.logger.error(f'comment not found {comment_id}')
            raise InvalidArgument(message=f'comment not found {comment_id}')

        comment_tip = CommentTipManager.add_tip(
            comment_id, send_user_id, 
            receive_user_id, 
            amount, price, asset, asset_id, balance_transfer_id, comment.lang)
        UserManager.update_user(send_user_id, 
                                kwargs.get('send_user_name'), 
                                kwargs.get('send_user_account_name'), 
                                kwargs.get('send_user_avatar'))
        return dict(
            id=comment_tip.id
        )