from app.api.common import Namespace
from app.api.common import Resource
from app.api.common.decorators import respond_with_code
from app.business.comment_manager import CommentManager
from app.cache.comment_cache import CommentCache
from app.models.comment import Comment


ns = Namespace('Comment')

@ns.route('/<int:comment_id>')
@respond_with_code
class CommentResource(Resource):
    """评论操作"""
    
    @classmethod
    def get(cls, comment_id):
        """获取评论详情"""
        comment = CommentCache(comment_id).get_comment()
        return dict(
            comment_id=comment.id if comment else None,
            user_id=comment.user_id if comment else None,
            status=comment.status.name if comment else None
        )