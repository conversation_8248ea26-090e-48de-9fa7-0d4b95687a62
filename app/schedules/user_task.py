import logging
from celery.schedules import crontab
from app.common.constants import CeleryQueues
from app.business.lock import lock_call
from app.business.user import UserManager
from app.models.base import db
from app.models.moderation import CommentUserStatus
from app.utils import celery_task
from app.utils.celery_ import route_module_to_celery_queue, scheduled
from app.utils.date_ import now


route_module_to_celery_queue(__name__, CeleryQueues.USER_INFO)

@celery_task
@lock_call(with_args=True)
def update_user_info(user_id, user_info: dict):
    """更新用户信息"""
    UserManager.update_user(user_id, **user_info)


@scheduled(crontab(minute='*/30'))
@lock_call()
def proccess_user_unban():
    """处理用户解禁"""
    records = CommentUserStatus.query.filter(
        CommentUserStatus.banned.is_(True),
        CommentUserStatus.banned_until < now(),
    ).limit(2000).all()

    is_commit = False
    for record in records:
        if not record.is_banned():
            is_commit = True
            record.unban(is_commit=False)
    if is_commit:
        db.session.commit()