from sqlalchemy import func
from app.common.constants import Language
from app.common.constants import CeleryQueues
from app.models import db
from app.utils import celery_task
from app.models.comment import Comment, CommentFullTextSearch
from app.business.lock import lock_call

import logging

from app.utils.tokenization import cut_words

_logger = logging.getLogger(__name__)


@celery_task(queue=CeleryQueues.COMMENT_MODERATION)
@lock_call(with_args=True)
def initial_moderate(moderation_id: int,if_detail:bool=False):
    """初步审核"""
    from app.business.comment_manager import CommentManager
    from app.business.moderation_manager import ModerationManager
    from app.models.moderation import CommentModeration

    try:
        moderation = ModerationManager.initial_moderate(moderation_id,if_detail=if_detail)
        comment = Comment.query.get(moderation.comment_id)
        if moderation.status == CommentModeration.Status.APPROVED:
            CommentManager.publish(comment)
            _logger.warning(f"The comment has passed initial moderation. Comment ID: {comment.id}")
        else:
            CommentManager.disable(comment)
            _logger.warning(f"The comment was found to violate the rules during initial moderation. Comment ID:{comment.id} by '{moderation.rejected_type}'" +
                          f" because of '{moderation.reason}'")
        return moderation
    except Exception as e:
        _logger.exception(f"initial moderate error: {e}")
        return None


@celery_task(queue=CeleryQueues.SEARCH_INDEX)
@lock_call(with_args=['comment_id'])
def create_comment_fulltext_search(comment_id: int, lang: str, text: str):
    """创建评论全文检索"""
    lang = Language[lang]
    contents = cut_words(lang, text)
    record = CommentFullTextSearch(
        lang=lang.name,
        comment_id=comment_id,
        ts_content=func.to_tsvector(" ".join(contents)), 
    )
    db.session_add_and_commit(record)
