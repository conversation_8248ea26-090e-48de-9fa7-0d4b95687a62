from datetime import date, timedelta
import logging

from app.business.lock import lock_call
from app.common.constants import CeleryQueues
from celery.schedules import crontab
from app.models.base import db
from app.models.report import DailyCommentTipReport, MonthlyCommentTipReport
from app.models.tip import CommentTip
from app.utils import scheduled,route_module_to_celery_queue
from app.utils.amount import quantize_amount
from app.utils.common import ReportType
from app.utils.date_ import date_to_datetime, last_month, next_month, today

_logger = logging.getLogger(__name__)


route_module_to_celery_queue(__name__, CeleryQueues.STATISTICS)


def _update_report(report_date: date, report_type: ReportType):
    if report_type == ReportType.DAILY:
        model = DailyCommentTipReport
        start_dt, end_dt = date_to_datetime(report_date), date_to_datetime(report_date + timedelta(days=1))
    else:
        model = MonthlyCommentTipReport

        start_dt = date_to_datetime(report_date)
        end_dt = next_month(start_dt.year, start_dt.month, 1)
    
    model: DailyCommentTipReport | MonthlyCommentTipReport
    comment_tips = CommentTip.query.filter(
        CommentTip.created_at >= start_dt,
        CommentTip.created_at < end_dt,
    ).with_entities(
        CommentTip.send_user_id,
        CommentTip.receive_user_id,
        CommentTip.amount,
        CommentTip.asset,
        CommentTip.price,
    ).all()

    send_user_ids, receive_user_ids = set(), set()

    tip_amount = tip_usd = 0
    for item in comment_tips:
        send_user_ids.add(item.send_user_id)
        receive_user_ids.add(item.receive_user_id)
        tip_amount += item.amount
        tip_usd += item.amount * item.price
    tip_usd = quantize_amount(tip_usd, 2)

    r = model(
        report_date=report_date,
        send_tip_count=len(comment_tips),
        send_tip_user_count=len(send_user_ids),
        send_tip_amount=tip_amount,
        send_tip_usd=tip_usd,
        receive_tip_count=len(comment_tips),
        receive_tip_user_count=len(receive_user_ids),
        receive_tip_amount=tip_amount,
        receive_tip_usd=tip_usd,
    )
    db.session_add_and_commit(r)


@scheduled(crontab(minute='10,30,50', hour='0,1'))
@lock_call()
def update_daily_comment_tip_report_schedule():
    last_report = DailyCommentTipReport.query.order_by(DailyCommentTipReport.report_date.desc()).first()

    today_ = today()
    if last_report:
        start_date = last_report.report_date + timedelta(days=1)
    else:
        start_date = today_ - timedelta(days=7)

    while start_date < today_:
        _update_report(start_date, ReportType.DAILY)
        start_date += timedelta(days=1)


@scheduled(crontab(day_of_month=1, hour='0,1', minute='10,30,50'))
@lock_call()
def update_monthly_comment_tip_report_schedule():

    today_ = date_to_datetime(today())
    report_date = last_month(today_.year, today_.month, 1)
    _update_report(report_date, ReportType.MONTHLY)
