from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
import json
import logging

from sqlalchemy import case, func

from app.business.lock import lock_call
from app.cache.comment_cache import CommentCache
from app.common.constants import CeleryQueues
from celery.schedules import crontab
from app.cache.statistics import CommentUserDisableCountCache, CommentUserStatsUpdateInfoCache
from app.models.base import db
from app.models.comment import Comment, CommentVote, CommentWarning
from app.models.event import CommentEvent
from app.models.moderation import CommentReport
from app.models.statistics import CommentUserStatistics, CommentStatistics
from app.models.tip import CommentTip
from app.models.user import UserInfo
from app.utils import scheduled,route_module_to_celery_queue
from app.utils.celery_ import celery_task
from app.utils.date_ import current_timestamp, now, timestamp_to_datetime
from app.utils.iterable import batch_iter

_logger = logging.getLogger(__name__)


route_module_to_celery_queue(__name__, CeleryQueues.STATISTICS)

@celery_task
@lock_call(with_args=True, wait=True)
def update_tip_by_comment_task(comment_id: int):
    """更新评论打赏数据"""
    comment_statistics = CommentStatistics.update_tip_by_comment(comment_id)

    CommentCache(comment_id=comment_id).update_tip_data(comment_statistics.tip_count,
                                                        comment_statistics.tip_user_count,
                                                        comment_statistics.tip_amount)


@scheduled(crontab(minute='*/30'))
@lock_call()
def update_comment_user_statistics_schedule():
    """更新评论用户统计"""

    def _get_latest_records(model, last_id: int | None, start_time: datetime):
        """
        获取最新记录：
        - 如果 last_id 有值，则获取大于 last_id 的记录
        - 如果 last_id 为 None, 此时可能是缓存丢失, 取 start_time 之后的记录
        - 如果 last_id 为 0, 代表上次执行无相应记录, 此时查全表
        """
        query = model.query
        if last_id:
            query = query.filter(model.id > last_id)
        elif last_id is None:
            query = query.filter(model.created_at >= start_time)
        return query.order_by(model.id.desc()).all()

    def _get_vote_map(last_updated_at: datetime | None):
        if not last_updated_at:
            last_updated_at = now() - timedelta(days=1)
        update_user_ids = CommentVote.query.filter(
            CommentVote.updated_at >= last_updated_at
        ).with_entities(CommentVote.comment_user_id.distinct()).all()
        update_user_ids = [item[0] for item in update_user_ids]

        res = {}
        for ids in batch_iter(update_user_ids, 5000):
            votes = (CommentVote.query
                .filter(CommentVote.comment_user_id.in_(ids)).group_by(CommentVote.comment_user_id)
                .with_entities(
                    CommentVote.comment_user_id,
                    func.sum(case((CommentVote.vote == CommentVote.VoteType.UP.value, 1), else_=0)
                    ).label('up_count'),
                    func.sum(case((CommentVote.vote == CommentVote.VoteType.DOWN.value, 1),else_=0)
                    ).label('down_count'))
                .all())
            res.update({vote.comment_user_id: (vote.up_count, vote.down_count) for vote in votes})
        return res

    def _get_disable_map(current_ts: int):
        Cache = CommentUserDisableCountCache
        interval = Cache.INTERVAL

        last_ts = current_ts - current_ts % interval

        all_user_ids = set()
        
        for _ in range(3):
            cache = Cache(last_ts)
            data = cache.hgetall()
            user_ids = list(data)
            user_ids = [int(user_id) for user_id in user_ids]
            all_user_ids.update(user_ids)
            cache.delete()
            last_ts -= interval
        res = dict()
        for ids in batch_iter(all_user_ids, 5000):
            tmp = Comment.query.filter(
                Comment.user_id.in_(ids),
                Comment.status == Comment.CommentStatus.DISABLED
            ).group_by(Comment.user_id).with_entities(Comment.user_id, 
                                                      func.count(Comment.id)).all()
            res.update(dict(tmp))
        return res

    # data: {update_ts: 123, event_id: 123, report_id: 123, warning_id: 123}
    now_ts = current_timestamp(to_int=True)
    data = CommentUserStatsUpdateInfoCache().read()
    if data:
        data = json.loads(data)
    last_updated_at = last_event_id = last_report_id = last_warning_id = None
    if data:
        last_event_id = data.get('event_id')
        last_report_id = data.get('report_id')
        last_warning_id = data.get('warning_id')
        last_update_ts = data['update_ts']
        last_updated_at = timestamp_to_datetime(int(last_update_ts))
    
    start_time = now() - timedelta(minutes=30) 
    
    events = _get_latest_records(CommentEvent, last_event_id, start_time)
    last_event_id = events[0].id if events else last_event_id
    
    reports = _get_latest_records(CommentReport, last_report_id, start_time)
    last_report_id = reports[0].id if reports else last_report_id

    warnings = _get_latest_records(CommentWarning, last_warning_id, start_time)
    last_warning_id = warnings[0].id if warnings else last_warning_id

    count_map = defaultdict(lambda: defaultdict(int))
    for event in events:
        event: CommentEvent

        # 赞踩不从此表统计
        if event.type in (CommentEvent.EventType.UP,
                          CommentEvent.EventType.DOWN):
            continue

        user_id = event.user_id
        type_ = event.type.name.lower()
        if event.source == CommentEvent.EventSource.SELF:
            count_map[user_id][f'self_{type_}_count'] += 1
        
        if event.source == CommentEvent.EventSource.OTHERS:
            count_map[user_id][f'{type_}_count'] += 1
    
    comment_user_id_map = {}
    for batch_reports in batch_iter(reports, 2000):
        comment_ids = [report.comment_id for report in batch_reports]
        comment_ids = list(set(comment_ids))
        tmp = Comment.query.filter(
            Comment.id.in_(comment_ids)
        ).with_entities(Comment.id, Comment.user_id).all()
        comment_user_id_map.update(dict(tmp))
    
    for report in reports:
        report: CommentReport
        user_id = comment_user_id_map.get(report.comment_id)
        if not user_id:
            continue
        count_map[user_id]['report_count'] += 1
    
    for batch_warnings in batch_iter(warnings, 2000):
        comment_ids = [report.comment_id for report in batch_warnings]
        comment_ids = list(set(comment_ids))
        tmp = Comment.query.filter(
            Comment.id.in_(comment_ids)
        ).with_entities(Comment.id, Comment.user_id).all()
        comment_user_id_map.update(dict(tmp))
    for warning in warnings:
        warning: CommentWarning
        user_id = comment_user_id_map.get(warning.comment_id)
        if not user_id:
            continue
        count_map[user_id]['warning_count'] += 1

    # tip人数要去重，这里直接全量更新
    send_tip_records = CommentTip.query.group_by(
        CommentTip.send_user_id
    ).with_entities(CommentTip.send_user_id, 
                    func.count(CommentTip.receive_user_id.distinct()).label('user_count'),
                    func.count(CommentTip.id).label('count'),
                    func.sum(CommentTip.amount).label('amount')).all()
    send_tip_sum_record_map = {item.send_user_id: item for item in send_tip_records}

    receive_tip_records = CommentTip.query.group_by(
        CommentTip.receive_user_id
    ).with_entities(CommentTip.receive_user_id, 
                    func.count(CommentTip.send_user_id.distinct()).label('user_count'),
                    func.count(CommentTip.id).label('count'),
                    func.sum(CommentTip.amount).label('amount')).all()
    receive_tip_sum_record_map = {item.receive_user_id: item for item in receive_tip_records}

    vote_map = _get_vote_map(last_updated_at)
    for user_id, (up_count, down_count) in vote_map.items():
        count_map[user_id]['up_count'] = up_count
        count_map[user_id]['down_count'] = down_count
    
    disable_map = _get_disable_map(now_ts)

    for user_id, count in disable_map.items():
        count_map[user_id]['block_count'] = count
    
    # user_ids 即要更新的用户列表, 排除已注销的用户
    user_ids = list(count_map)

    excluded_user_ids = set()
    for ids in batch_iter(user_ids, 5000):
        infos = UserInfo.query.filter(
            UserInfo.user_id.in_(ids),
            UserInfo.signed_off.is_(True)
        ).with_entities(UserInfo.user_id).all()
        tmp = {info.user_id for info in infos}
        excluded_user_ids.update(tmp)

    user_ids = list(set(user_ids) - excluded_user_ids)
    updated_user_ids = []
    for batch_user_ids in batch_iter(user_ids, 5000):
        update_records = CommentUserStatistics.query.filter(
            CommentUserStatistics.user_id.in_(batch_user_ids)
        ).all()
        for item in update_records:
            item: CommentUserStatistics
            user_id = item.user_id
            map_ = count_map[user_id]
            item.comment_count += map_['self_comment_count'] + map_['self_reply_count']
            item.reply_count += map_['reply_count']
            
            # up 和 down两个字段全量更新
            if user_id in vote_map:
                item.up_count = map_['up_count']
                item.down_count = map_['down_count']
            item.report_count += map_['report_count']
            item.warning_count += map_['warning_count']

            # 违规数字段全量更新
            if user_id in disable_map:
                item.block_count = map_['block_count']
            if item.send_tip_count is None:
                item.send_tip_count = 0
            if item.send_tip_user_count is None:
                item.send_tip_user_count = 0
            if item.receive_tip_count is None:
                item.receive_tip_count = 0
            if item.receive_tip_user_count is None:
                item.receive_tip_user_count = 0
            if item.send_tip_amount is None:
                item.send_tip_amount = 0
            if item.receive_tip_amount is None:
                item.receive_tip_amount = 0
            send_tip_sum_record = send_tip_sum_record_map.get(user_id)
            receive_tip_sum_record = receive_tip_sum_record_map.get(user_id)
            item.send_tip_count = send_tip_sum_record.count if send_tip_sum_record else 0
            item.send_tip_user_count = send_tip_sum_record.user_count if send_tip_sum_record else 0
            item.receive_tip_count = receive_tip_sum_record.count if receive_tip_sum_record else 0
            item.receive_tip_user_count = receive_tip_sum_record.user_count if receive_tip_sum_record else 0
            item.send_tip_amount = send_tip_sum_record.amount if send_tip_sum_record else Decimal()
            item.receive_tip_amount = receive_tip_sum_record.amount if receive_tip_sum_record else Decimal()

            updated_user_ids.append(user_id)
        db.session.commit()
    new_user_ids = set(user_ids) - set(updated_user_ids)
    objects = []
    for user_id in new_user_ids:
        map_ = count_map[user_id]
        send_tip_sum_record = send_tip_sum_record_map.get(user_id)
        receive_tip_sum_record = receive_tip_sum_record_map.get(user_id)
        item = CommentUserStatistics(
            user_id=user_id,
            comment_count=map_['self_comment_count'] + map_['self_reply_count'],
            reply_count=map_['reply_count'],
            up_count=map_['up_count'],
            down_count=map_['down_count'],
            report_count=map_['report_count'],
            warning_count=map_['warning_count'],
            send_tip_count=send_tip_sum_record.count if send_tip_sum_record else 0,
            send_tip_user_count=send_tip_sum_record.user_count if send_tip_sum_record else 0,
            receive_tip_count=receive_tip_sum_record.count if receive_tip_sum_record else 0,
            receive_tip_user_count=receive_tip_sum_record.user_count if receive_tip_sum_record else 0,
            send_tip_amount=send_tip_sum_record.amount if send_tip_sum_record else Decimal(),
            receive_tip_amount=receive_tip_sum_record.amount if receive_tip_sum_record else Decimal(),
        )
        objects.append(item)
    db.session.bulk_save_objects(objects)
    db.session.commit()
    data = dict(
        update_ts=now_ts,
        event_id=last_event_id or 0,
        report_id=last_report_id or 0,
        warning_id=last_warning_id or 0,
    )
    CommentUserStatsUpdateInfoCache().save(json.dumps(data))
