from collections import defaultdict
from datetime import timedelta
from decimal import Decimal
from logging import getLogger
from app import config
from app.business.alert import send_alert_notice
from app.business.clients.push import PushClient
from app.business.comment_score import CommentScoreCalculator
from app.cache.comment_cache import UserPending<PERSON>omments<PERSON>ache, CommentCache, CommentListCache, ProcessCommentVoteCache, \
    ProcessCommentReportCache, ProcessCommentCountCache, CommentCountCache
from app.models.event import CommentEvent
from app.models.moderation import CommentModeration
from app.utils.amount import amount_to_str
from app.utils.date_ import now, timestamp_to_datetime, today
from celery.schedules import crontab
from app.business.lock import lock_call
from app.models import db
from app.models.comment import Comment
from app.utils import scheduled, route_module_to_celery_queue, batch_iter
from app.common.constants import CeleryQueues

_logger = getLogger(__name__)

route_module_to_celery_queue(__name__, CeleryQueues.COMMENT_SCHEDULE)


@scheduled(crontab(minute='*/5'))
@lock_call()
def process_comment_info():
    from app.business.comment_manager import CommentVoteManager, CommentManager
    # 获取所有待处理的赞踩记录
    _now = now()
    last_time = ProcessCommentVoteCache().get_last_evaluation()
    if not last_time:
        last_time = _now - timedelta(days=5)
    else:
        last_time = timestamp_to_datetime(last_time)

    votes = CommentVoteManager.get_new_vote_count(last_time)
    reply_counts = CommentManager.get_new_reply_count(last_time)
    if reply_counts:
        _logger.warning(f'准备修正以下评论的回复数：{reply_counts}')

    comment_ids = set(list(votes.keys()) + list(reply_counts.keys()))

    comments = CommentManager.get_valid_comments(comment_ids)
    for comment_id, (ups, downs) in votes.items():
        comment = comments.get(comment_id)
        if comment is None:
            continue

        # 不处理刚发布/违规的评论
        if comment.status != Comment.CommentStatus.PUBLISHED:
            continue

        # 计算得分
        comment.up_count = ups
        comment.down_count = downs
        CommentScoreCalculator.update_comment_score(comment)

    # 顺路更新回复数到pg
    for comment_id, reply_count in reply_counts.items():
        comment = comments.get(comment_id)
        if comment is None:
            continue
        comment.reply_count = reply_count

    # 保证先写db
    db.session.commit()

    # 更新缓存
    for comment_id in comment_ids:
        comment = comments.get(comment_id)
        if comment is None:
            continue

        # 不处理刚发布/违规的评论
        if comment.status != Comment.CommentStatus.PUBLISHED:
            continue

        CommentCache(comment_id).save_comment_with_keys(
            comment, ["up_count", "down_count", "vote_count", "reply_count", "hot_score", "top_score"]
        )

        # 更新一级列表或者回复的 top 得分
        top_clc = CommentListCache(
            comment.business, comment.business_id, comment.lang, Comment.SortType.TOP, comment.root_id
        )
        top_clc.change_vote(comment_id, comment.top_score)

        if not comment.parent_id:
            # 只更新一级列表的评论 hot 得分
            hot_clc = CommentListCache(
                comment.business, comment.business_id, comment.lang, Comment.SortType.HOT
            )
            hot_clc.change_vote(comment_id, comment.hot_score)

    ProcessCommentVoteCache().save_evaluation_time(int(_now.timestamp()))


@scheduled(crontab(hour='*/1', minute="0"))
@lock_call()
def process_comment_report():
    from app.business.moderation_manager import CommentReportManager, CommentManager

    _now = now()
    last_time = ProcessCommentReportCache().get_last_evaluation()
    if not last_time:
        last_time = _now - timedelta(days=5)
    else:
        last_time = timestamp_to_datetime(last_time)

    new_report_comment_ids = CommentReportManager.get_new_report_comment_ids(last_time)

    for _new_report_comment_ids in batch_iter(new_report_comment_ids, 100):
        new_comment_report_count = CommentReportManager.get_new_comment_report_count(_new_report_comment_ids)
        comments = CommentManager.get_valid_comments(_new_report_comment_ids)
        for comment_id, count in new_comment_report_count.items():
            comment = comments.get(comment_id)
            if comment is None:
                continue

            comment.report_count = count

            if count > 10:
                CommentReportManager.new_comment_report_review(comment_id)

        db.session.commit()  # new_comment_report_review 里有一次commit，这里用来保证最后的report_count正确提交

    ProcessCommentReportCache().save_evaluation_time(int(_now.timestamp()))


@scheduled(crontab(minute="*/15"))
@lock_call()
def process_comment_count():
    from app.business.moderation_manager import CommentManager

    _now = now()
    last_time = ProcessCommentCountCache().get_last_evaluation()
    if not last_time:
        last_time = _now - timedelta(days=5)
    else:
        last_time = timestamp_to_datetime(last_time)

    new_root_comment_business_infos = CommentManager.get_new_root_comment_business_infos(last_time)
    print(new_root_comment_business_infos, )

    for _new_root_comment_business_infos in batch_iter(new_root_comment_business_infos, 100):
        for business_info in _new_root_comment_business_infos:
            business, business_id, lang = business_info
            CommentCountCache(business, business_id, lang).reload()

    ProcessCommentCountCache().save_evaluation_time(int(_now.timestamp()))


@scheduled(crontab(minute='*/1'))
@lock_call()
def check_pending_moderations():
    """检查处理中的审核记录，处理ai初审超时情况"""
    from app.business.comment_manager import CommentManager

    def send_moderation_alert(msg: str):
        try:
            send_alert_notice(
                msg,
                config['ADMIN_CONTACTS'].get('moderation_timeout_notice')
            )
        except Exception as e:
            _logger.error(f"Failed to send cost alert: {e}")

    timeout = timedelta(minutes=10)
    _now = now()

    # 查找所有处于处理中且超过10分钟的记录
    pending_moderations = CommentModeration.query.filter(
        CommentModeration.status == CommentModeration.Status.PROCESSING,
        CommentModeration.updated_at + timeout <= _now
    ).all()

    for moderation in pending_moderations:
        try:
            # 获取评论并检查状态
            comment = Comment.query.get(moderation.comment_id)
            if not comment or comment.status not in [Comment.CommentStatus.CREATED, Comment.CommentStatus.DISABLED]:
                # 如果评论不存在或状态不是待审核/禁用，更新为审核失败
                msg = f"Moderation {moderation.id} for comment {moderation.comment_id} failed: " \
                      f"comment not found or invalid status"
                _logger.warning(msg)
                send_moderation_alert(msg)
                moderation.status = CommentModeration.Status.FAILED
                moderation.reason = "Comment not found or invalid status"
                db.session.add(moderation)
                db.session.commit()
                continue

            msg = f"Moderation {moderation.id} for comment {moderation.comment_id} " \
                  f"timeout after {timeout}, auto approving"

            _logger.warning(msg)
            send_moderation_alert(msg)

            # 更新审核记录
            moderation.status = CommentModeration.Status.AUTO_APPROVED
            moderation.reason = "Auto approved due to timeout"
            db.session.add(moderation)

            # 发布评论
            CommentManager.publish(comment)

            db.session.commit()

        except Exception as e:
            _logger.error(f"Failed to process timeout moderation {moderation.id}: {str(e)}",
                          exc_info=True)
            db.session.rollback()


@scheduled(crontab(hour='*/3', minute="0"))
@lock_call()
def push_comment_interaction_info():
    """推送用户近三小时互动通知"""
    _now = now()
    start_time = _now - timedelta(hours=3)

    query = CommentEvent.query.filter(
        CommentEvent.created_at >= start_time,
        CommentEvent.created_at <= _now,
        CommentEvent.read_status == CommentEvent.ReadStatus.UNREAD,
        CommentEvent.source == CommentEvent.EventSource.OTHERS,
        CommentEvent.self_interact == False,
    ).with_entities(
        CommentEvent.user_id,
        CommentEvent.type,
        CommentEvent.extra,
    )

    offset = 0
    limit = 1000
    user_interaction_info = defaultdict(lambda: {
        "up_count": 0,
        "reply_count": 0,
        "at_count": 0,
        "tip_count": 0,
        "tip_amount": defaultdict(lambda: Decimal("0"))
    })
    while events := query.offset(offset * limit).limit(limit).all():
        for event in events:
            user_id = event.user_id
            match event.type:
                case CommentEvent.EventType.UP:
                    user_interaction_info[user_id]['up_count'] += 1
                case CommentEvent.EventType.REPLY:
                    user_interaction_info[user_id]['reply_count'] += 1
                case CommentEvent.EventType.AT:
                    user_interaction_info[user_id]['at_count'] += 1
                case CommentEvent.EventType.TIP:
                    user_interaction_info[user_id]['tip_count'] += 1
                    if extra := event.extra:
                        if 'asset' in extra:
                            user_interaction_info[
                                user_id
                            ]['tip_amount'][extra['asset']] += Decimal(extra.get('amount', "0"))
                case _:
                    pass
        offset += 1

    for user_id, info_map in user_interaction_info.items():
        if any(info_map.values()):
            PushClient().send(
                user_id=user_id,
                push_type=PushClient.PushType.Interaction,
                web_push_params={
                    "up_count": info_map['up_count'],
                    "reply_count": info_map['reply_count'],
                    "at_count": info_map['at_count'],
                    "tip_count": info_map['tip_count'],
                    "tip_amount": {k: amount_to_str(v) for k, v in info_map['tip_amount'].items()},
                    "total_count": info_map['up_count'] + info_map['reply_count'] + info_map['at_count'] + info_map[
                        'tip_count'],
                }
            )


@scheduled(crontab(hour='10', minute="0"))
@lock_call()
def push_comment_tip_email():
    """推送用户打赏互动邮件"""
    _today = today()
    start_time = _today - timedelta(days=1)

    query = CommentEvent.query.filter(
        CommentEvent.created_at >= start_time,
        CommentEvent.created_at <= _today,
        CommentEvent.type == CommentEvent.EventType.TIP,
        CommentEvent.read_status == CommentEvent.ReadStatus.UNREAD,
        CommentEvent.source == CommentEvent.EventSource.OTHERS,
        CommentEvent.self_interact == False,
    ).with_entities(
        CommentEvent.user_id,
        CommentEvent.extra,
    )

    offset = 0
    limit = 1000
    user_tip_info = defaultdict(lambda: {
        "tip_count": 0,
        "tip_amount": defaultdict(lambda: Decimal("0"))
    })
    while events := query.offset(offset * limit).limit(limit).all():
        for event in events:
            user_id = event.user_id
            user_tip_info[user_id]['tip_count'] += 1
            if extra := event.extra:
                if 'asset' in extra:
                    user_tip_info[user_id]['tip_amount'][extra['asset']] += Decimal(extra.get('amount', "0"))
        offset += 1

    for user_id, info_map in user_tip_info.items():
        if any(info_map.values()):
            PushClient().send(
                user_id=user_id,
                push_type=PushClient.PushType.Interaction,
                email_params={
                    "tip_count": info_map['tip_count'],
                    "tip_amount": {k: amount_to_str(v) for k, v in info_map['tip_amount'].items()},
                }
            )
