# -*- coding: utf-8 -*-
import os

from logging import get<PERSON><PERSON><PERSON>, Lo<PERSON>, getLogRecordFactory, setLogRecordFactory
from logging.config import dictConfig
from traceback import format_exc
from typing import Optional
from flask import Flask
from flask.ctx import has_app_context
from flask_migrate import Migrate
from celery import Celery
from redis import Redis, RedisCluster

from .config import config
from .utils import auto_close_db_session

app: Optional[Flask] = None
celery: Celery = Celery(
    __name__,
    broker=config['CELERY_BROKER_URL']
)
migrate: Optional[Migrate] = None
redis: Optional[Redis] = None
_logger: Optional[Logger] = None


DEFAULT_LABELS = {
    "project": "Coinex",
    "service": "CoinexCom",
    "module": "CoinexComComment",
}


def create_app():
    global app
    app = Flask(__name__, static_folder=None)

    _init_logging()
    _init_config(app)
    _init_db(app)
    _init_celery(app)
    _init_business(app)
    _init_apis(app)
    _init_third_party(app)
    _logger.info('server started successfully')

    return app


def _init_logging():
    # noinspection SpellCheckingInspection
    dictConfig({
        'version': 1,
        'formatters': {
            'simple': {
                'format': '[%(asctime)s] %(levelname)s %(name)s: %(message)s',
            },
            'colored': {
                '()': 'colorlog.ColoredFormatter',
                'format': '[%(asctime)s] '
                          '%(log_color)s%(levelname)s%(reset)s '
                          '%(name)s: %(message)s'
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'formatter': 'colored',
                'level': 'DEBUG',
                'stream': 'ext://sys.stdout'
            },
            'graylog': {
                'class': 'app.utils.GELFUDPHandler',
                'host': config['GRAYLOG_LOGGER_HOST'],
                'port': config['GRAYLOG_LOGGER_PORT'],
                'formatter': 'simple',
                'level': 'WARNING'
            }
        },
        'loggers': {
            'app': {
                'level': 'INFO',
                'handlers': ['console', 'graylog'],
                'propagate': False
            }
        },
        'disable_existing_loggers': False
    })
    # make sure flask `app.name == 'app'`
    # so we can use `current_app.logger` to get `_logger`
    global _logger
    _logger = getLogger('app')

    old_factory = getLogRecordFactory()

    def record_factory(*args, **kwargs):
        record = old_factory(*args, **kwargs)
        for k, v in DEFAULT_LABELS.items():
            setattr(record, k, v)
        return record

    setLogRecordFactory(record_factory)


def _init_config(flask_app: Flask):
    _logger.info('initiating configurations...')

    flask_app.config.from_mapping(config)


def _init_business(flask_app: Flask):
    _logger.info('initiating business layer...')

    from .business import init_app
    init_app(flask_app)


def _init_apis(flask_app: Flask):
    _logger.info('initiating APIs...')

    from app.api import init_app
    init_app(flask_app)


def _init_third_party(flask_app: Flask):
    _logger.info('initiating APIs...')

    from app.third_party import init_app
    init_app(flask_app)


def _init_celery(flask_app: Flask):
    _logger.info('initiating celery...')

    global celery
    celery.config_from_object('app.config.celery')

    class ContextTask(celery.Task):
        abstract = True

        def on_failure(self, exc, task_id, args, kwargs, e_info):
            _ = task_id, e_info
            task_info = f'task_name => {self.name}, ' \
                        f'args => {args}, ' \
                        f'kwargs => {kwargs}'

            with flask_app.app_context():
                from .business.lock import Locked
                logger = flask_app.logger
                if isinstance(exc, Locked):
                    logger.warning(f'{task_info} | Locked: {exc.data}', extra={'task': self.name})
                else:
                    logger.error('\n'.join([task_info, format_exc()]), extra={'task': self.name})

        def __call__(self, *args, **kwargs):
            if has_app_context():
                return super().__call__(*args, **kwargs)
            with flask_app.app_context():  # this means that the task is called asynchronously, not directly.
                _logger.info("[Process %s] Task %s started", os.getpid(), self.name)
                return auto_close_db_session(super().__call__)(*args, **kwargs)

    # noinspection PyPropertyAccess
    celery.Task = ContextTask

    from . import schedules


def _init_db(flask_app: Flask):
    _logger.info('initiating db...')

    from .models import db
    db.init_app(flask_app)

    from alembic.runtime.migration import MigrationContext
    from sqlalchemy import Column
    from sqlalchemy.sql.sqltypes import SchemaType, Enum as SQLEnum

    def type_comparer(context: MigrationContext,
                      inspected_column: Column,
                      metadata_column: Column,
                      inspected_type: SchemaType,
                      metadata_type: SchemaType) -> Optional[bool]:
        """
        For perhaps preposterous reasons Alembic has not supported type
        comparison between `Enum`s therefore we have to do it on our own.
        :return: bool: whether not the same; None: use default implementation
        """
        _ = context, inspected_column, metadata_column
        if not (isinstance(inspected_type, SQLEnum)
                and isinstance(metadata_type, SQLEnum)):
            return None
        return set(inspected_type.enums) != set(metadata_type.enums)

    global migrate
    migrate = Migrate(flask_app, db, compare_type=type_comparer)

    global redis
    redis = RedisCluster(**config['REDIS']) if config['REDIS'].pop('cluster', None) else Redis(**config['REDIS'])
