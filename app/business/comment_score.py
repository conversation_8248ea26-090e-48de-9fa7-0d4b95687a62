from datetime import datetime, timedelta
import math
from app.models.comment import Comment
from app.models.statistics import CommentStatistics


# 设置 HOT 排序基准时间为 1970-01-01 00:00:00 UTC
# BASE_TIME = datetime(1970, 1, 1)
BASE_TIME = datetime(2024, 12, 1, tzinfo=None).timestamp()
# 设置 HOT 排序时间衰减参数
HOT_TIME_FACTOR = 45000  # 时间因子系数
# 设置 TOP 排序时间衰减参数
TOP_ID_FACTOR = 2000000000 # 20 亿，接近 Postgres Integer 类型最大值，保证时间因子总是小数


class CommentScoreCalculator:
    """评论评分计算器"""
    @classmethod
    def update_comment_score(cls, comment: Comment):
        comment.hot_score = cls.cal(Comment.SortType.HOT, comment)
        comment.top_score = cls.cal(Comment.SortType.TOP, comment)

    @classmethod
    def cal(cls, sort_type: Comment.SortType, comment) -> float:
        tip_user_count = CommentStatistics.query.filter(CommentStatistics.comment_id == comment.id)\
            .with_entities(CommentStatistics.tip_user_count).scalar() or 0
        
        if sort_type == Comment.SortType.HOT:
            """Reddit 故事排序算法
            """
            # 计算投票分数
            x = tip_user_count + comment.up_count - comment.down_count
            vote_score = math.log10(max(1, abs(x)))

            # 获取时间因子
            created_timestamp = comment.created_at.replace(tzinfo=None).timestamp() \
                if isinstance(comment.created_at, datetime) \
                else comment.created_at if isinstance(comment.created_at, int) else None
            if created_timestamp is None:
                raise ValueError("时间无效")
            seconds = created_timestamp - BASE_TIME
            # 根据x的符号决定时间因子的方向
            sign = 1 if x >= 0 else -1
            # 计算时间分数
            time_score = seconds * sign / HOT_TIME_FACTOR

            return vote_score + time_score
        elif sort_type == Comment.SortType.NEW:
            """最新排序算法
            """
            # 直接使用 id 作为排序因子
            return float(comment.id)
        elif sort_type == Comment.SortType.TOP:
            """最高评分排序算法
            """
            # 计算投票分数，并且叠加上时间因子（用 id 代替），保证在评分相同时，越早的评论排在前面
            # 为了保证时间因子总是排序的次要因素，不会超过整数范围，将 id 除以一个大数，使得 id 因子总是小数
            return (tip_user_count + comment.up_count - comment.down_count) + comment.id / TOP_ID_FACTOR

    @staticmethod 
    def quantize_score(raw_score: float) -> int:
        """将浮点分数量化为32位整数
        （暂时用不上了，热度直接采用原始分数排序）
        算法说明:
        1. 将分数映射到[-1, 1]区间:
           - 对于正数,使用 2/(1+e^(-x/5)) - 1 进行sigmoid映射
           - 对于负数,使用相反数的映射的相反数保持单调性
        2. 将[-1, 1]区间映射到[-2^30, 2^30]区间
           - 预留一个bit作为符号位
           - 预留一个bit作为安全边界
           
        特点:
        1. 保证了严格的顺序性
        2. 通过sigmoid函数提供了良好的区分度
        3. 结果范围被限制在32位整数范围内
        4. 在常见分数区间([-10,10])提供较高的区分度
        """
        
        # Sigmoid映射到[-1,1]区间
        def sigmoid_map(x):
            return 2 / (1 + math.exp(-x/5)) - 1
            
        if raw_score >= 0:
            normalized = sigmoid_map(raw_score)
        else:
            normalized = -sigmoid_map(-raw_score)
            
        # 映射到32位整数范围
        # 使用2^30作为最大值,预留两个bit
        MAX_INT30 = 2 ** 30

        return int(normalized * MAX_INT30)


if __name__ == '__main__':
    # # 1. 基本功能测试
    # print("=== 基本功能测试 ===")
    # test_scores = [0, 1, -1, 5, -5, 10, -10]
    # print("基本数值测试:")
    # for score in test_scores:
    #     quantized = CommentScoreCalculator.quantize_score(score)
    #     print(f"原始分数: {score:>6}, 量化结果: {quantized:>12}")
    
    # print("\n相近数值区分测试:")
    # close_scores = [1.000, 1.001, 1.010, 1.100]
    # for score in close_scores:
    #     quantized = CommentScoreCalculator.quantize_score(score)
    #     print(f"原始分数: {score:>6.3f}, 量化结果: {quantized:>12}")
    
    # # 2. 顺序性测试
    # print("\n=== 顺序性测试 ===")
    # def test_order(scores):
    #     quantized_scores = [CommentScoreCalculator.quantize_score(s) for s in scores]
    #     is_ordered = all(q1 < q2 for q1, q2 
    #                     in zip(quantized_scores[:-1], quantized_scores[1:]))
    #     print(f"输入序列: {scores}")
    #     print(f"量化结果: {quantized_scores}")
    #     print(f"顺序性保持: {'是' if is_ordered else '否'}")
        
    # test_order([-5, -2, -1, 0, 1, 2, 5])
    # test_order([-100, -10, -1, -0.1, -0.01])
    # test_order([0.01, 0.1, 1, 10, 100])
    
    # # 3. 边界测试
    # print("\n=== 边界测试 ===")
    # boundary_scores = [
    #     -1e6, -1e3, -1e2,  # 大的负数
    #     1e2, 1e3, 1e6      # 大的正数
    # ]
    # for score in boundary_scores:
    #     quantized = CommentScoreCalculator.quantize_score(score)
    #     print(f"原始分数: {score:>10.2e}, 量化结果: {quantized:>12}")
    #     # 验证是否在32位整数范围内
    #     assert -2**31 <= quantized <= 2**31-1, f"量化结果{quantized}超出32位整数范围"
    
    # # 4. 实际场景测试
    # print("\n=== 实际场景测试 ===")
    # # 测试不同时间点的相同评论
    # base_time = datetime(2025, 1, 1)
    # time_points = [
    #     base_time,
    #     base_time.replace(month=6),
    #     base_time.replace(year=2026),
    #     base_time.replace(year=2027),
    #     base_time.replace(year=2028),
    #     base_time.replace(year=2029),
    # ]
    
    # print("\n随时间推移的评论分数变化(相同的赞踩比例):")
    # for t in time_points:
    #     score = CommentScoreCalculator.calculate_hot_score(100, 50, t)
    #     quantized = CommentScoreCalculator.quantize_score(score)
    #     print(f"时间: {t}, 原始分数: {score:>8.3f}, 量化结果: {quantized:>12}")
    
    # print("\n不同赞踩比例的评论(相同时间点):")
    # vote_pairs = [
    #     (100, 0),   # 全是赞
    #     (80, 20),   # 好评为主
    #     (55, 45),   # 略微好评
    #     (50, 50),   # 中性
    #     (45, 55),   # 略微差评
    #     (20, 80),   # 差评为主
    #     (0, 100),   # 全是踩
    # ]
    
    # 新增时间因子权重测试
    # print("\n=== 时间因子权重测试 ===")
    
    # def test_time_weight(start_time):
    #     # 固定投票数
    #     ups = 0
    #     downs = 0
        
    #     # 测试不同时间点
    #     print(f"\n起始时间: {start_time}")
    #     time_points = [
    #         (start_time, "当前时间"),
    #         (start_time + timedelta(seconds=1), "1秒钟后"),
    #         (start_time + timedelta(seconds=5), "5秒钟后"),
    #         (start_time + timedelta(seconds=10), "10秒钟后"),
    #         (start_time + timedelta(minutes=1), "1分钟后"),
    #         (start_time + timedelta(minutes=5), "5分钟后"),
    #         (start_time + timedelta(minutes=10), "10分钟后"),
    #         (start_time + timedelta(hours=1), "1小时后"),
    #         (start_time + timedelta(hours=10), "10小时后"),
    #         (start_time + timedelta(days=1), "1天后"),
    #         (start_time + timedelta(days=10), "10天后"),
    #         (start_time + timedelta(days=30), "1个月后"),
    #         (start_time + timedelta(days=90), "3个月后"),
    #         (start_time + timedelta(days=180), "6个月后"),
    #         (start_time + timedelta(days=365), "1年后"),
    #         (start_time + timedelta(days=730), "2年后"),
    #     ]
        
    #     print(f"固定投票数: {ups}赞/{downs}踩")
    #     for time, desc in time_points:
    #         score = CommentScoreCalculator.calculate_hot_score(ups, downs, time)
    #         print(f"时间: {desc:<6}, hot分数: {score:>8.3f}")

    # # 新增投票因子权重测试
    # def test_vote_weight(start_time):
    #     # 固定时间
    #     # 测试不同投票比例
    #     vote_pairs = [
    #         (1, 0, "全赞1"),
    #         (10, 0, "全赞10"),
    #         (100, 0, "全赞100"),
    #         (80, 20, "好评为主"),
    #         (55, 45, "略微好评"),
    #         (50, 50, "中性"),
    #         (45, 55, "略微差评"),
    #         (20, 80, "差评为主"),
    #         (0, 100, "全踩"),
    #     ]
        
    #     print(f"\n起始时间: {start_time}")
    #     for ups, downs, desc in vote_pairs:
    #         score = CommentScoreCalculator.calculate_hot_score(ups, downs, start_time)
    #         print(f"投票: {desc:<6} ({ups}赞/{downs}踩), hot分数: {score:>8.3f}")

    # # 运行测试
    # # 起始时间
    # test_time = datetime(2025, 1, 1)
    # test_time_weight(test_time)
    # test_vote_weight(test_time)

    # 生成时间-投票交叉表格
    def generate_score_table(start_time):
        # 定义时间点
        time_points = [
            (start_time, "当前时间"),
            (start_time + timedelta(seconds=1), "1秒钟后"),
            (start_time + timedelta(seconds=5), "5秒钟后"),
            (start_time + timedelta(seconds=10), "10秒钟后"),
            (start_time + timedelta(minutes=1), "1分钟后"),
            (start_time + timedelta(minutes=10), "10分钟后"),
            (start_time + timedelta(hours=1), "1小时后"),
            (start_time + timedelta(days=1), "1天后"),
            (start_time + timedelta(days=7), "1周后"),
            (start_time + timedelta(days=30), "1个月后"),
            (start_time + timedelta(days=365), "1年后"),
        ]
        
        # 定义投票组合
        vote_pairs = [
            (1, 0, "全赞1"),
            (2, 0, "全赞2"),
            (10, 0, "全赞10"),
            (50, 0, "全赞50"),
            (100, 0, "全赞100"),
            (80, 20, "好评为主"),
            (50, 50, "中性"),
            (20, 80, "差评为主"),
        ]
        
        # 打印表头
        print("\n=== 时间-投票交叉评分表 ===")
        print(f"起始时间: {start_time}")
        print("\n时间\\投票", end="")
        for _, _, desc in vote_pairs:
            print(f" | {desc:<8}", end="")
        print()
        
        # 打印表格内容
        for time, time_desc in time_points:
            print(f"{time_desc:<8}", end="")
            for ups, downs, _ in vote_pairs:
                score = CommentScoreCalculator.cal(Comment.SortType.HOT, ups, downs, time)
                print(f" | {score:>8.3f}", end="")
            print()

    # 运行测试
    test_time = datetime(2025, 1, 1)
    generate_score_table(test_time)
