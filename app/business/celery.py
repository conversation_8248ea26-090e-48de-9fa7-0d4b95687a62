# -*- coding: utf-8 -*-

from collections import defaultdict
from json import loads as json_loads
from base64 import b64decode
from typing import Dict
from flask import Flask
from flask_redis import FlaskRedis
from ..utils import compact_json_dumps



celery_redis = FlaskRedis(config_prefix='CELERY_BROKER')


def init_celery_redis(app: Flask):
    celery_redis.init_app(app)


def get_all_celery_queues() -> Dict[str, int]:
    return {queue.decode('utf-8'): celery_redis.llen(queue)
            for queue in celery_redis.keys(b'*')
            if celery_redis.type(queue) == b'list'
            and b'reply.celery.pidbox' not in queue}


def get_celery_queue(queue: str):
    tasks = defaultdict(int)
    tasks_with_params = defaultdict(int)
    for data in map(json_loads, celery_redis.lrange(queue, 0, -1)):
        task = data['headers']['task']
        params = json_loads(b64decode(data['body']))[0]
        tasks[task] += 1
        tasks_with_params[f'{task}({compact_json_dumps(params)})'] += 1

    return tasks, tasks_with_params


def delete_celery_queue(queue: str) -> int:
    return celery_redis.delete(queue)


def trim_celery_queue(queue: str, keep: int):
    celery_redis.ltrim(queue, 0, keep)
