from packaging.version import Version

from app.api.common.requests import get_request_platform, get_request_version, get_request_build
from app.common.constants import Platform


class CompatibleRule:

    platform_match_func_map = {
        Platform.WEB: "is_web",
        Platform.IOS: "is_ios",
        Platform.ANDROID: "is_android",
    }

    def __init__(self, platform: Platform, min_version=None, max_version=None):
        if not any([min_version, max_version]):
            raise

        self.platform = platform
        self.min_version = None
        self.max_version = None

        if min_version:
            self.min_version = Version(min_version)
        if max_version:
            self.max_version = Version(max_version)

    def match(self, platform=None, version=None) -> bool:
        platform = platform or get_request_platform()
        version = version or get_request_version()

        match_func_name = self.platform_match_func_map.get(self.platform)
        if not match_func_name:
            return False

        if (not hasattr(platform, match_func_name)) or (not getattr(platform, match_func_name)()):
            return False

        if not version:
            return False

        try:
            v = Version(version)
        except Exception:
            return False

        if self.min_version and v <= self.min_version:
            return False
        if self.max_version and v >= self.max_version:
            return False

        return True


class CompatibleRuleGroup:

    def __init__(self, rules: [CompatibleRule], desc=""):
        self.rules = rules
        self.desc = desc

    def match(self, platform=None, version=None) -> [CompatibleRule]:
        platform = platform or get_request_platform()
        version = version or get_request_version()

        match_rule = []
        for rule in self.rules:
             if rule.match(platform, version):
                 match_rule.append(rule)

        return match_rule


COMMENT_TIP_MESSAGE_COMPATIBLE_RULE = CompatibleRuleGroup([
    CompatibleRule(Platform.IOS, max_version='3.18.0'),  # < build 91
    CompatibleRule(Platform.ANDROID, max_version='3.48.0'),  # < build 3480
], desc="低版本app对评论打赏功能消息列表相关的兼容规则")


def is_old_app_request(android_build: int, ios_build: int) -> bool:
    platform = get_request_platform()
    build = get_request_build()
    # 处理ios两个app的兼容，临时解决办法。后面CoinEx Pro build号增长超过500时注意跟进处理。
    # CoinEx App: 500 < build < 600
    # CoinEx Pro App: 0 < build < 500
    if ios_build < 500:
        ios_build += 600
    if platform.is_ios() and build < 500:
        build += 600
    if (platform.is_android() and build < android_build) or \
            (platform.is_ios() and build < ios_build):
        return True
    return False
