import hashlib

import requests
from flask import current_app

from app import config
from app.cache import Alert<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def get_msg_handle_func(url):
    return send_slack_message


def send_alert_notice(content: str, url: str, expired_seconds: int = 0, msg_id: str = None, at: str = None, ) -> bool:
    """ 发送系统(告警)消息 """
    if not content or not url:
        return False  # for testing env
    # 如果有多个人，用逗号分隔","
    if at:
        at_users = at.split(",")
        at_str = " ".join([f"<@{_s}>" for _s in at_users])
        content = f"{content}\n{at_str}"

    send_func = get_msg_handle_func(url)
    if expired_seconds <= 0:
        send_func(content, url)
        return True

    if not msg_id:
        msg_id = hashlib.md5(content.encode()).hexdigest()
    cache = AlertUserTimeCache(msg_id)
    if cache.exists():
        return False
    send_func(content, url)
    cache.set_cache(expired_seconds)
    return True


def send_slack_message(content: str, channel_id: str) -> None:
    """ 发送slack消息 """
    build_text_json = {
        "text": content
    }
    _do_slack_request(channel_id=channel_id, json=build_text_json)


def _do_slack_request(channel_id: str, json: dict) -> None:
    resp = requests.post(
        config["SLACK_MSG_URL"],
        headers={'Authorization': f'Bearer {config["SLACK_TOKEN"]}'},
        json={**json, 'channel': channel_id},
        timeout=15
    )

    # slack 直接使用 http code 来反馈错误，非200就是各种异常。
    if resp.status_code != 200:
        error_msg = f"send channel id: {channel_id} msg request error: status code: {resp.status_code}, text: {resp.text}"
        current_app.logger.error(error_msg)


if __name__ == '__main__':
    bool_ = send_alert_notice('test', config['ADMIN_CONTACTS'].get('llm_cost_notice'))
    print(bool_)
