import logging
from datetime import datetime
import emoji
from sqlalchemy import func
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from app.business.comment_manager import CommentManager
from app.common.constants import (
    COMPETITOR_DOMAIN,
    COMPETITOR_KEY_WORDS,
    TRANSLATION_REPORT_TYPE_FAKE,
    TRANSLATION_REPORT_TYPE_FRAUD,
    TRANSLATION_REPORT_TYPE_MALICIOUS,
    TRANSLATION_REPORT_TYPE_OTHER,
    TRANSLATION_REPORT_TYPE_SPAM,
    Language,TARGET_DIGITS_EMOJIS
)
from app.config import config as app_config
from app.exceptions import (
    InvalidArgument,
    RecordNotFound,
)
from app.models import db
from app.models.comment import Comment
from app.models.moderation import (
    CommentModeration,
    CommentReport,
    CommentReportReview,
)
from app.utils import BaseHTTP<PERSON>lient, RESTClient

_logger = logging.getLogger(__name__)

Phase = CommentModeration.Phase
ModeratorType = CommentModeration.ModeratorType
Trigger = CommentModeration.Trigger
Status = CommentModeration.Status
RejectType = CommentModeration.RejectType


class ModerationError(Exception):
    pass


class ModerationManager:
    conf = app_config['AI_MODERATION_CONFIG']
    AI_CLIENT: RESTClient = RESTClient(conf['url'])

    @classmethod
    def initial_moderate(cls, moderation_id: int, if_detail: bool = False) -> CommentModeration:
        """处理初步审核
        Args:
            moderation_id: 审核记录id
            if_detail:是否获取详细审核信息
        Returns:
            moderation: 审核记录
        """
        moderation = CommentModeration.query.get(moderation_id)
        if not moderation:
            _logger.error(f"Moderation record {moderation_id} not found")
            raise InvalidArgument("Moderation record not found")

        comment = Comment.query.get(moderation.comment_id)
        if not comment:
            moderation.status = Status.FAILED
            moderation.reason = "Comment not found"
            db.session.commit()
            _logger.error(f"Initial moderate comment {moderation.comment_id} not found for moderation {moderation_id}")
            raise InvalidArgument(f"Comment {moderation.comment_id} not found")

        # 先进行关键词检查
        keyword_result = cls._check_keywords(comment.content,phase=Phase.INITIAL)
        if not keyword_result['passed']:
            moderation.status = Status.REJECTED
            moderation.rejected_type = RejectType.OTHER
            moderation.moderator_type = ModeratorType.KEYWORD
            moderation.reason = f"Contains competitor words or number emoji: {', '.join(keyword_result['matched_words'])}"
            moderation.moderation_detail = {'matched_words': keyword_result['matched_words']}
            db.session.add(moderation)
            db.session.commit()
            return moderation

        if comment.is_simple:
          moderation_result = {
                'passed': True,
                'reject_type': None,
                'reason': None
            }
        else:
            # 如果关键词检查通过，继续 AI 审核
            moderation_result = cls._check_by_ai(comment.content, phase=Phase.INITIAL, if_detail=if_detail)

        passed = moderation_result['passed']

        # 更新审核记录
        if passed:
            moderation.status = Status.APPROVED
        else:
            moderation.status = Status.REJECTED
            moderation.rejected_type = moderation_result.get('reject_type')
            moderation.reason = moderation_result.get('reason')
            moderation.moderation_detail = moderation_result.get('details')

        db.session.add(moderation)
        db.session.commit()

        return moderation

    @classmethod
    def _check_keywords(cls, content: str,phase: Phase) -> dict:
        """关键词过滤检查
        Args:
            content: 评论内容
            phase: 审核阶段(initial/full)
        Returns:
            dict: {'passed': bool, 'matched_words': list}
        """

        matched_key_words = []
        content_lower = content.lower()
        filter_key_words = COMPETITOR_DOMAIN+COMPETITOR_KEY_WORDS
        for key_word in filter_key_words:
            if key_word.lower() in content_lower:
                matched_key_words.append(key_word)
        # 检测是否包含多个emoji数字
        digit_emoji_count = 0
        matched_digit_emojis = []
        for emoji_data in emoji.emoji_list(content):
            if emoji_data['emoji'] in TARGET_DIGITS_EMOJIS:
                digit_emoji_count += 1
                matched_digit_emojis.append(emoji_data['emoji'])
        if digit_emoji_count >=5:
            matched_key_words.extend(matched_digit_emojis)

        return {
            'passed': len(matched_key_words) == 0,
            'matched_words': matched_key_words
        }

    @classmethod
    @retry(
        stop=stop_after_attempt(2),
        wait=wait_exponential(multiplier=1, min=2, max=4),
        retry=retry_if_exception_type((BaseHTTPClient.BadResponse, ModerationError)),
        before_sleep=lambda retry_state: _logger.warning(
            f"AI moderation attempt {retry_state.attempt_number} failed, retrying..."
        )
    )
    def _check_by_ai(cls, content: str, phase: Phase, if_detail: bool = False) -> dict:
        """AI 审核检查
        Args:
            content: 评论内容
            phase: 审核阶段(initial/full)
        Returns:
            dict: {'passed': bool, 'reason': str,'reject_type': RejectType }
        """
        try:
            response = cls.AI_CLIENT.post('/', {
                'content': content,
                'phase': phase.name,
                'if_detail': if_detail,
            })

            if response['code'] != 0:
                _logger.error(f'check comment {content} failed:{response}')
                raise ModerationError(f'check comment {content} failed:{response}')

            passed = response['data']['passed']
            reason = response['data'].get('reason')
            reject_type = response['data'].get('reject_type')
            details = response['data'].get('details')

            if reject_type is not None:
                try:
                    # 将数字映射到对应的枚举
                    reject_type = RejectType(int(reject_type))
                except (ValueError, TypeError) as e:
                    _logger.error(f"Invalid reject_type from AI service: {reject_type},error:{e}")
                    raise ModerationError(f"Invalid reject_type from AI service: {reject_type},error:{e}")

            return {'passed': passed, 'reason': reason, 'reject_type': reject_type, 'details': details}

        except BaseHTTPClient.BadResponse as e:
            _logger.error(f"Ai moderation failed: {e}")
            raise ModerationError(f"Ai moderation failed: {e}")

    @classmethod
    def get_moderation_detail(cls, comment: Comment) -> dict:
        """获取评论的详细AI审核结果
        Args:
            comment: 评论对象
        Returns:
            moderation: 审核记录
        """
        existing_moderation = CommentModeration.query.filter(
            CommentModeration.comment_id == comment.id,
            CommentModeration.phase == Phase.INITIAL
        ).first()

        if not existing_moderation:
            _logger.error(f"No moderation record found for comment {comment.id}")
            raise InvalidArgument()

        # 如果已经有详细信息，直接返回
        if existing_moderation.moderation_detail:
            return {
                'passed': existing_moderation.status == Status.APPROVED,
                'reason': existing_moderation.reason,
                'reject_type': existing_moderation.rejected_type,
            }
        try:
            ai_result = cls._check_by_ai(comment.content, phase=Phase.INITIAL, if_detail=True)
            # 更新现有审核记录的详细信息
            passed = ai_result['passed']
            existing_moderation.moderation_detail = ai_result.get('details')
            existing_moderation.reason = ai_result.get('reason')
            if passed:
                existing_moderation.reason = "AI重新过滤后判定不违规，请人工确认"
            db.session.commit()

            return {'passed':passed, 'reason': existing_moderation.reason, 'reject_type': ai_result.get('reject_type')}

        except Exception as e:
            _logger.error(f"Failed to get moderation detail for comment {comment.id}: {str(e)}")
            db.session.rollback()
            raise InvalidArgument()

    @classmethod
    def manual_moderate(cls, comment_id: int, operator_id: int,
                        result: CommentModeration.Status, reason: str = None,
                        rejected_type: CommentModeration.RejectType = None) -> CommentModeration:
        """人工审核
        Args:
            comment_id: 评论ID
            operator_id: 操作人ID
            result: 审核结果(approved/rejected)
            reason: 审核原因(拒绝时必填)
            rejected_type: 拒绝类型(可选)
        Returns:
            CommentModerate: 审核记录
        Raises:
            InvalidArgument: 参数无效
            RecordNotFound: 评论不存在
        """
        try:
            if result not in [Status.APPROVED, Status.REJECTED]:
                raise InvalidArgument("Invalid moderation result")

            # 拒绝时必须提供原因
            if result == Status.REJECTED and not reason:
                raise InvalidArgument("Reason is required when rejecting comment")

            comment = Comment.query.get(comment_id)
            if not comment:
                raise RecordNotFound("Comment not found")

            if comment.status == Comment.CommentStatus.DELETED:
                raise RecordNotFound("Comment has been deleted")

            if comment.status not in [Comment.CommentStatus.CREATED, Comment.CommentStatus.PUBLISHED, Comment.CommentStatus.DISABLED]:
                raise InvalidArgument("Comment status does not allow moderation")

            # 创建人工审核记录
            moderation = CommentModeration(
                comment_id=comment_id,
                phase=CommentModeration.Phase.FULL,
                moderator_type=CommentModeration.ModeratorType.MANUAL,
                trigger=CommentModeration.Trigger.PATROL,
                status=result,
                reason=reason,
                rejected_type=rejected_type if result == Status.REJECTED else None,
                operator_id=operator_id,
                remark=None,
                moderation_detail=None
            )
            db.session.add(moderation)

            # 更新评论状态
            if result == Status.APPROVED:
                if comment.status == Comment.CommentStatus.DISABLED:
                    CommentManager.publish(comment)
            elif result == Status.REJECTED:
                if comment.status in [Comment.CommentStatus.CREATED, Comment.CommentStatus.PUBLISHED]:
                    CommentManager.disable(comment)

            db.session.commit()

            _logger.info(
                f"Manual moderation completed - comment_id: {comment_id}, "
                f"result: {result.name}, operator_id: {operator_id}"
            )

            return moderation

        except Exception as e:
            db.session.rollback()
            _logger.error(
                f"Manual moderation failed - comment_id: {comment_id}, "
                f"error: {str(e)}"
            )
            raise


class CommentReportManager:
    """评论举报管理类"""

    report_type_dict = {item.name: item.value for item in CommentReport.Type}

    @classmethod
    def get_report_type(cls, lang: Language = None) -> dict:
        """获取举报类型"""
        _report_type_translation_map = {
            CommentReport.Type.FRAUD: TRANSLATION_REPORT_TYPE_FRAUD,
            CommentReport.Type.MALICIOUS: TRANSLATION_REPORT_TYPE_MALICIOUS,
            CommentReport.Type.SPAM: TRANSLATION_REPORT_TYPE_SPAM,
            CommentReport.Type.FAKE: TRANSLATION_REPORT_TYPE_FAKE,
            CommentReport.Type.OTHER: TRANSLATION_REPORT_TYPE_OTHER,
        }
        lang = lang or Language.ZH_HANS_CN
        return {k: v.get(lang) for k, v in _report_type_translation_map.items()}

    @staticmethod
    def new_report(comment_id, user_id, report_type, reason):
        if reason and len(reason) > 500:
            raise InvalidArgument("Content is too long")

        comment = CommentManager.get_valid_comment(comment_id)

        db.session.add(CommentReport(
            comment_id=comment_id,
            comment_user_id=comment.user_id,
            user_id=user_id,
            type=report_type,
            reason=reason,
        ))
        db.session.commit()

    @staticmethod
    def get_new_report_comment_ids(after: datetime):
        return [
            cr.comment_id for cr in
            db.session.query(CommentReport.comment_id).filter(CommentReport.created_at >= after).distinct().all()
        ]

    @staticmethod
    def get_new_comment_report_count(comment_ids):
        query = db.session.query(
            CommentReport.comment_id,
            func.count(func.distinct(CommentReport.user_id)).label('count'),
        ).filter(CommentReport.comment_id.in_(comment_ids)).group_by(CommentReport.comment_id)

        return {res.comment_id: res.count for res in query.all()}

    @staticmethod
    def new_comment_report_review(comment_id):
        # 如果已经存在审核条目，不再新增
        crr = CommentReportReview.query.filter(
            CommentReportReview.comment_id == comment_id
        ).first()

        if crr is None:
            db.session.add(CommentReportReview(
                comment_id=comment_id,
                status=CommentReportReview.Status.CREATED,
            ))

            db.session.commit()
