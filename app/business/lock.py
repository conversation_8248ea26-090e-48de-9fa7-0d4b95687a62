# -*- coding: utf-8 -*-
import time
from base64 import b64encode
from hashlib import sha512
from types import FunctionType
from typing import List, Union, Optional

from decorator import decorate

from app.cache import LockCache
from app.cache.base import _BaseCache
from app.exceptions import FrequencyExceeded
from app.utils import func_args_to_str, func_to_str
import logging
_logger = logging.getLogger(__name__)


class LockKeys:

    @classmethod
    def func_lock(cls, key: str):
        return f'func_lock:{key}'


class Locked(FrequencyExceeded):

    def __init__(self, key: str):
        super().__init__(data=key)


class CacheLock:

    def __init__(self, key: str, *,
                 ttl: int = None,
                 wait: Union[bool, float] = False):
        self._key = key
        self._ttl = ttl or 3600
        self._wait = wait
        self._cache = LockCache(key)

    def __enter__(self):
        self.acquire()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()

    def test(self) -> bool:
        return self._cache.exists()

    def acquire(self, *, wait: Union[bool, float] = None):
        if wait is None:
            wait = self._wait
        max_wait = 60 if isinstance(wait, bool) else wait

        t0 = time.time()
        while not self._cache.set('1', ex=self._ttl, nx=True):
            if not wait or time.time() - t0 >= max_wait:
                raise TimeoutError(self._key)
            time.sleep(0.2)

    def release(self):
        self._cache.delete()


def load_if_not_exists(cache_instance: _BaseCache) -> bool:
    if cache_instance.exists():
        return False

    lock_key = f"cache_lock:{cache_instance.key}:reload"
    with CacheLock(lock_key, wait=1):
        # 双重检查
        if cache_instance.exists():
            _logger.warning(f'get cache after lock: {cache_instance.key}')
            return False
            
        try:
            _logger.warning(f'reload cache: {cache_instance.key}')
            cache_instance.reload()
        except Exception as e:
            _logger.error(f'failed to reload cache {cache_instance.key}: {e}')
            return False
        return True


def _gen_args_key(func: FunctionType, args: tuple, kwargs: dict,
                  only: Optional[list] = None) -> str:
    key = func_args_to_str(func, args, kwargs, only=only)
    if len(key) > 88:  # ceil(512 / 8 * log(256, 64)) == 86
        key = b64encode(sha512(key.encode()).digest()).decode()
    return key


def lock_call(key: str = None, *,
              with_args: Union[bool, int, str, List[Union[int, str]]] = False,
              wait: Union[bool, float] = False,
              ttl: int = None):
    """
    Locks a function call until it's finished.
    :param key: unique key for the lock (defaults to '{module}.{__qualname__}')
    :param wait: whether to wait if already locked (float for max waiting time)
    :param with_args: whether to include arguments in the key
    :param lock_type: cache / db
    :param ttl: TTL of lock

    Examples:
        @lock_call('abc')
        @lock_call(wait=True)
        @lock_call(wait=60)
        @lock_call(with_args=True)
        @lock_call(with_args=[0, 'x'])
    """
    if key is not None and not isinstance(key, str):
        raise TypeError(
            f'invalid key: {key!r}'
            f' (Did you forget to append parentheses to the decorator?)')

    if isinstance(with_args, str) \
            or (isinstance(with_args, int) and not isinstance(with_args, bool)):  # bool is int too
        with_args = [with_args]

    def dec(func):
        nonlocal key
        key = key or LockKeys.func_lock(func_to_str(func))

        def wrapper(_func, *args, **kwargs):
            if with_args is False:
                _key = key
            else:
                _args_key = _gen_args_key(
                    _func, args, kwargs,
                    None if with_args is True else with_args)
                _key = f'{key}:{_args_key}'
            _lock_args = dict(wait=wait)
            if ttl is not None:
                _lock_args['ttl'] = ttl
            _lock = CacheLock(_key, **_lock_args)
            with _lock:
                return _func(*args, **kwargs)

        return decorate(func, wrapper)

    return dec
