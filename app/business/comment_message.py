from datetime import datetime
from sqlalchemy import or_, and_

from app.business.compat_rules import COMMENT_TIP_MESSAGE_COMPATIBLE_RULE, is_old_app_request
from app.business.user import UserManager
from app.cache.user import UserInfoCache
from app.common.constants import Language, MSG_TITLE_SELF_UP_TITLE, MSG_TITLE_OTHER_UP_TITLE, MSG_TITLE_SELF_DOWN_TITLE, \
    MSG_TITLE_OTHER_DOWN_TITLE, MSG_TITLE_SELF_COMMENT_TITLE, MSG_TITLE_SELF_REPLY_TITLE, MSG_TITLE_OTHER_REPLY_TITLE, \
    MSG_TITLE_OTHER_AT_TITLE, MSG_TITLE_SELF_TIP_TITLE, MSG_TITLE_OTHER_TIP_TITLE
from app.models import db
from app.models.event import CommentEvent
from app.utils.iterable import Pagination, pagination


class CommentEventManager:
    """评论事件管理类，用于event的管理"""

    @classmethod
    def new_comment_event(
        cls, comment_id: int, from_user_id: int, to_user_id: int,
        lang: Language, event_type: CommentEvent.EventType, need_commit=True, extra=None,
    ):
        self_interact = from_user_id == to_user_id
        # self
        db.session.add(CommentEvent(
            comment_id=comment_id,
            user_id=from_user_id,
            other_user_id=to_user_id,
            lang=lang,
            type=event_type,
            source=CommentEvent.EventSource.SELF,
            read_status=CommentEvent.ReadStatus.READ,  # 自己操作的都是已读
            self_interact=self_interact,
            extra=extra or {},
        ))

        # other
        if event_type != CommentEvent.EventType.COMMENT:
            db.session.add(CommentEvent(
                comment_id=comment_id,
                user_id=to_user_id,
                other_user_id=from_user_id,
                lang=lang,
                type=event_type,
                source=CommentEvent.EventSource.OTHERS,
                read_status=CommentEvent.ReadStatus.READ if self_interact else CommentEvent.ReadStatus.UNREAD,
                self_interact=self_interact,
                extra=extra or {},
            ))

        if need_commit:
            db.session.commit()

    @staticmethod
    def get_events(user_id: int,
                   types: [CommentEvent.EventType] = None,
                   source: CommentEvent.EventSource | None = None,
                   read_status: CommentEvent.ReadStatus | None = None,
                   start_time: datetime | None = None,
                   end_time: datetime | None = None,
                   limit: int = 20, last_id: int = 0) -> [CommentEvent]:
        """获取互动消息列表"""
        query = CommentEvent.query.filter(CommentEvent.user_id == user_id)

        if read_status:
            query = query.filter(CommentEvent.read_status == read_status)

        if types:
            query = query.filter(CommentEvent.type.in_(types))

        if source:
            query = query.filter(CommentEvent.source == source)

        if start_time:
            query = query.filter(CommentEvent.created_at >= start_time)

        if end_time:
            query = query.filter(CommentEvent.created_at <= end_time)

        if last_id:
            query = query.filter(CommentEvent.id < last_id)

        return query.order_by(CommentEvent.created_at.desc()).limit(limit).all()

    @staticmethod
    def get_relate_user_infos(user_id, count=10):
        limit = min(100, count * 5)

        query_completed = False
        res = []
        relate_user_id_set = set()
        for offset in range(3):
            comment_events = CommentEvent.query.with_entities(CommentEvent.other_user_id).filter(
                CommentEvent.user_id == user_id,
                CommentEvent.other_user_id != user_id,
                CommentEvent.type.in_([CommentEvent.EventType.REPLY, CommentEvent.EventType.AT]),
            ).order_by(CommentEvent.created_at.desc()).offset(offset*limit).limit(limit).all()

            if not comment_events:
                break

            relate_user_ids = []
            comment_event_count = 0
            for comment_event in comment_events:
                comment_event_count += 1
                if comment_event.other_user_id in relate_user_id_set:
                    continue

                relate_user_id_set.add(comment_event.other_user_id)
                relate_user_ids.append(comment_event.other_user_id)

            if comment_event_count < limit:
                query_completed = True

            user_infos = UserManager.get_user_info(relate_user_id_set)
            for relate_user_id in relate_user_ids:
                user_info = user_infos.get(relate_user_id)
                if not user_info:
                    continue

                if user_info.get(UserInfoCache.SignedOffBizName):
                    continue

                user_info['user_id'] = relate_user_id
                res.append(user_info)

                if len(res) >= count:
                    query_completed = True
                    break

            if query_completed:
                break

        return res


class CommentMessageManager:
    """评论互动消息管理类"""

    MSG_TITLE_MAP = {
        CommentEvent.EventType.UP: {
            CommentEvent.EventSource.SELF: MSG_TITLE_SELF_UP_TITLE,
            CommentEvent.EventSource.OTHERS: MSG_TITLE_OTHER_UP_TITLE,
        },
        CommentEvent.EventType.DOWN: {
            CommentEvent.EventSource.SELF: MSG_TITLE_SELF_DOWN_TITLE,
            CommentEvent.EventSource.OTHERS: MSG_TITLE_OTHER_DOWN_TITLE,
        },
        CommentEvent.EventType.COMMENT: {
            CommentEvent.EventSource.SELF: MSG_TITLE_SELF_COMMENT_TITLE,
        },
        CommentEvent.EventType.REPLY: {
            CommentEvent.EventSource.SELF: MSG_TITLE_SELF_REPLY_TITLE,
            CommentEvent.EventSource.OTHERS: MSG_TITLE_OTHER_REPLY_TITLE,
        },
        CommentEvent.EventType.AT: {
            CommentEvent.EventSource.OTHERS: MSG_TITLE_OTHER_AT_TITLE,
        },
        CommentEvent.EventType.TIP: {
            CommentEvent.EventSource.SELF: MSG_TITLE_SELF_TIP_TITLE,
            CommentEvent.EventSource.OTHERS: MSG_TITLE_OTHER_TIP_TITLE,
        },
    }

    @staticmethod
    def default_message_filter(query):
        query = query.filter(or_(
            and_(
                CommentEvent.source == CommentEvent.EventSource.OTHERS,
                CommentEvent.self_interact == False,
            ),
            and_(
                CommentEvent.source == CommentEvent.EventSource.SELF,
                CommentEvent.type != CommentEvent.EventType.AT,
            )
        ))

        if is_old_app_request(3480, 91):
            query = query.filter(CommentEvent.type != CommentEvent.EventType.TIP)

        return query

    @staticmethod
    def get_messages(
        user_id: int,
        types: [CommentEvent.EventType] = None,
        source: CommentEvent.EventSource | None = None,
        read_status: CommentEvent.ReadStatus | None = None,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        limit: int = 20, 
        last_id: int = 0
    ) -> Pagination:
        """获取互动消息列表"""
        query = CommentEvent.query.filter(CommentEvent.user_id == user_id)

        if read_status:
            query = query.filter(CommentEvent.read_status == read_status)

        if types:
            query = query.filter(CommentEvent.type.in_(types))

        if source:
            query = query.filter(CommentEvent.source == source)
            if source == CommentEvent.EventSource.OTHERS:
                query = query.filter(CommentEvent.self_interact == False)
            else:
                query = query.filter(CommentEvent.type != CommentEvent.EventType.AT)
        else:
            query = CommentMessageManager.default_message_filter(query)

        if start_time:
            query = query.filter(CommentEvent.created_at >= start_time)

        if end_time:
            query = query.filter(CommentEvent.created_at <= end_time)

        if last_id:
            query = query.filter(CommentEvent.id < last_id)
        query = query.order_by(CommentEvent.created_at.desc())

        return pagination(query, limit)

    @staticmethod
    def mark_read(user_id: int, messages_ids: [int]) -> int:
        """标记互动消息为已读"""
        result = CommentEvent.query.filter(
            CommentEvent.user_id == user_id,
            CommentEvent.id.in_(messages_ids),
            CommentEvent.read_status == CommentEvent.ReadStatus.UNREAD
        ).update({CommentEvent.read_status: CommentEvent.ReadStatus.READ}, synchronize_session=False)

        db.session.commit()
        return result

    @staticmethod
    def mark_all_read(user_id: int) -> int:
        """标记用户所有互动消息为已读"""
        result = CommentMessageManager.default_message_filter(CommentEvent.query).filter(
            CommentEvent.user_id == user_id,
            CommentEvent.read_status == CommentEvent.ReadStatus.UNREAD
        ).update({CommentEvent.read_status: CommentEvent.ReadStatus.READ}, synchronize_session=False)

        db.session.commit()
        return result

    @staticmethod
    def get_unread_count(user_id: int) -> int:
        """获取用户未读消息数量"""
        return CommentMessageManager.default_message_filter(CommentEvent.query).filter(
            CommentEvent.user_id == user_id,
            CommentEvent.read_status == CommentEvent.ReadStatus.UNREAD
        ).count()

    @classmethod
    def get_msg_title(cls, msg_type: CommentEvent.EventType, lang: Language, source: CommentEvent.EventSource):
        if msg_source_title_map := cls.MSG_TITLE_MAP.get(msg_type):
            if msg_lang_title_map := msg_source_title_map.get(source):
                return msg_lang_title_map.get(lang)
            return ""
        return ""
