from flask import request

from app.api.common.requests import get_request_user_id, get_request_user_name, get_request_account_name, \
    get_request_avatar
from app.cache.user import UserInfoCache
from app.exceptions import InvalidArgument
from app.models import UserInfo


class UserManager:

    @staticmethod
    def update_user_from_request():
        if not request:
            raise InvalidArgument('must use in flask resource')

        user_id = get_request_user_id(True)

        UserManager.update_user(user_id,
                                get_request_user_name(),
                                get_request_account_name(),
                                get_request_avatar())

    @staticmethod
    def update_user(user_id, user_name, account_name, avatar):
        UserInfo.set(user_id, user_name, account_name, avatar)
        UserInfoCache().set_user_info(user_id, user_name, account_name, avatar)

    @staticmethod
    def get_user_info(user_ids):
        return UserInfoCache().get_user_info(user_ids)
