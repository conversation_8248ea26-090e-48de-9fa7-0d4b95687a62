from decimal import Decimal

from flask import g
from app.business.comment_manager import CommentManager
from app.business.comment_message import CommentEventManager
from sqlalchemy import func
from app.cache.comment_cache import CommentCache
from app.cache.user import UserInfoCache
from app.models import CommentEvent
from app.models.base import db
from app.models.statistics import CommentStatistics
from app.models.tip import CommentTip
from app.schedules.statistics import update_tip_by_comment_task
from app.utils.amount import amount_to_str
from app.utils.iterable import pagination


class CommentTipManager:

    @staticmethod
    def add_tip(comment_id: int, user_id: int, receive_user_id:int,
                amount: Decimal, price: Decimal, 
                asset: str, asset_id: int, balance_transfer_id: int, lang) -> CommentTip:
        comment_tip = CommentTip.query.filter(CommentTip.balance_transfer_id == balance_transfer_id).first()
        if comment_tip:
            return comment_tip
        comment_tip = CommentTip(
            send_user_id=user_id,
            receive_user_id=receive_user_id,
            comment_id=comment_id,
            amount=amount,
            price=price,
            asset=asset,
            asset_id=asset_id,
            balance_transfer_id=balance_transfer_id,
        )
        db.session.add(comment_tip)
        db.session.flush()

        # 记录event
        CommentEventManager.new_comment_event(
            comment_id, user_id, receive_user_id, lang, CommentEvent.EventType.TIP, need_commit=False,
            extra={
                "asset_id": asset_id,
                "asset": asset,
                "amount": amount_to_str(amount),
                "price": amount_to_str(price),
                "balance_transfer_id": balance_transfer_id,
                "comment_tip_id": comment_tip.id,
            }
        )

        db.session.flush()        
        db.session.commit()

        update_tip_by_comment_task.delay(comment_id)
        return comment_tip

    @staticmethod
    def get_tips(comment_id: int, last_id: int | None, limit: int):
        tip_query = CommentTip.query.filter(CommentTip.comment_id == comment_id).order_by(CommentTip.id.desc())
        if last_id:
            tip_query = tip_query.filter(CommentTip.id < last_id)
        tip_query = tip_query.with_entities(
            CommentTip.id,
            CommentTip.created_at,
            CommentTip.amount,
            CommentTip.asset,
            CommentTip.send_user_id,
            CommentTip.receive_user_id,
        )
        r = pagination(tip_query, limit)
        tips = r.items
        records = []
        for item in tips:
            item: CommentTip
            data = dict(
                id=item.id,
                created_at=item.created_at,
                amount=item.amount,
                asset=item.asset,
                send_user_id=item.send_user_id,
                receive_user_id=item.receive_user_id,
            )
            records.append(data)
        send_user_id_records = CommentTip.query.filter(CommentTip.comment_id == comment_id).with_entities(
            CommentTip.send_user_id.distinct().label("user_id")
        ).all()
        sum_record = CommentTip.query.filter(CommentTip.comment_id == comment_id).with_entities(
            func.sum(CommentTip.amount),
            func.count(CommentTip.id),
        ).first()
        amount, count = sum_record

        send_user_ids = {item.user_id for item in send_user_id_records}
        user_info_map = UserInfoCache().get_user_info(send_user_ids)
        for item in records:
            info_ = user_info_map.get(item['send_user_id'], {})
            info_['user_id'] = item['send_user_id']
            item['send_user_info'] = info_
        
        
        return dict(
            items=records,
            count=count,
            amount=amount,
            user_count=len(send_user_ids),
            has_next=r.has_next
        )
    
    @staticmethod
    def get_tip_users(comment_id: int, limit: int, 
                      send_user_id: int = None, amount: Decimal = None, asset: str = None):
        query = CommentTip.query
        if send_user_id:
            query = query.filter(CommentTip.send_user_id == send_user_id)
        if amount:
            query = query.filter(CommentTip.amount == amount)
        if asset:
            query = query.filter(CommentTip.asset == asset)

        send_user_id_records = query.filter(CommentTip.comment_id == comment_id).with_entities(
            CommentTip.send_user_id
        ).order_by(CommentTip.id.desc()).all()
        send_user_ids = []
        added = set()
        for item in send_user_id_records:
            if item.send_user_id not in added:
                send_user_ids.append(item.send_user_id)
                added.add(item.send_user_id)
        
        user_count = len(send_user_ids)
        send_user_ids = send_user_ids[:limit]
        user_info_map = UserInfoCache().get_user_info(send_user_ids)
        if g.user_id and g.user_id in send_user_ids:
            send_user_ids.remove(g.user_id)
            send_user_ids.insert(0, g.user_id)
        result = []
        for user_id in send_user_ids:
            info_ = user_info_map.get(user_id, {})
            info_['user_id'] = user_id
            result.append(info_)
        return dict(
            items=result,
            user_count=user_count
        )
        

