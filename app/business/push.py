

from app.business.clients.push import PushClient
from app.business.lock import lock_call
from app.common.constants import CeleryQueues
from app.models.comment import Comment
from app.models.moderation import CommentUserStatus
from app.utils.celery_ import celery_task, route_module_to_celery_queue

route_module_to_celery_queue(__name__, CeleryQueues.COMMENT_SCHEDULE)

@celery_task
@lock_call(with_args=['user_id', ])
def send_user_banned_push(user_id: int, duration: str, reason: str):
    duration = CommentUserStatus.BanDuration[duration].value
    push_client = PushClient()
    push_client.send(user_id=user_id,
                     push_type=PushClient.PushType.Ban,
                     web_push_params=dict(duration=duration, reason=reason, ban_days=duration),
                     message_params=dict(duration=duration, reason=reason, ban_days=duration)
                     )


@celery_task
@lock_call(with_args=['user_id', ])
def send_comment_warning_push(user_id: int, title: str, content: str, lang: str = None):
    push_client = PushClient()
    push_client.send(user_id=user_id,
                     push_type=PushClient.PushType.Warning,
                     web_push_params=dict(title=title, content=content),
                     message_params=dict(title=title, content=content)
                     )
