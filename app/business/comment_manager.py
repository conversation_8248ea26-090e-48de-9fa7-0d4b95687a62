import logging
import hashlib
from datetime import datetime, timedelta

from flask import g
from sqlalchemy import func, case
from sqlalchemy import select

from app import config
from app.exceptions.basic import CommentLangMismatch, CommentDuplicate
from dateutil.tz import UTC
from app.models.statistics import CommentStatistics
from app.models.tip import CommentTip
from app.utils import batch_iter, now, GoogleTranslate
from app.utils.text import clean_whitespace, sanitize, detect_lang, check_simple_text
from app.common.constants import Language
from app.exceptions import (
    InvalidArgument,
    InvalidAccount,
    CommentDisabled,
    TranslationError,
    CommentNotFound,
    CommentDeleted,
)
from app.models import db
from app.models.comment import Comment, CommentTranslation, CommentVote
from app.models.moderation import CommentModeration
from app.models.event import CommentEvent
from app.business.clients.ai_translate import AITranslateClient, ModelType, AITranslateBusiness
from app.cache.comment_cache import UserPendingCommentsCache, CommentCache, Comment<PERSON><PERSON><PERSON><PERSON>, CommentCountCache
from app.cache.statistics import CommentUserDisableCountCache
from app.cache.user import UserInfoCache
from app.schedules.comment_task import create_comment_fulltext_search, initial_moderate
from .comment_message import CommentEventManager
from .comment_score import CommentScoreCalculator
from .user import UserManager

_logger = logging.getLogger(__name__)


class CommentManager:
    """评论管理类
    处理评论相关的业务逻辑
    """
    @classmethod
    def to_display(cls, comments: list[Comment] | Comment, sort_type: Comment.SortType = Comment.SortType.TOP, user_id: int = None) -> list | dict:
        if isinstance(comments, list):
            comment_list = [cls._comment_to_display(comment, sort_type) for comment in comments]
            cls._fill_user_info(comment_list)
            cls._fill_is_voted(comment_list, user_id)
            cls._fill_tips_info(comment_list, user_id)
            # 如果前两条是 highlighted 评论（从互动消息跳转到列表时，指定的那一条），则把它们折叠起来
            if len(comment_list) >= 2 and comment_list[0].get('highlighted') and comment_list[1].get('highlighted'):
                # 把第二条评论放到第一条评论的 children 属性里
                comment_list[0]['children'] = [comment_list[1]]
                # 删除第二条评论
                comment_list.pop(1)
            return comment_list
        else:
            comment_dict = cls._comment_to_display(comments, sort_type)
            cls._fill_user_info([comment_dict])
            cls._fill_is_voted([comment_dict], user_id)
            cls._fill_tips_info([comment_dict], user_id)
            return comment_dict

    @classmethod
    def _comment_to_display(cls, comment: Comment, sort_type: Comment.SortType = None) -> dict:
        comment_dict = {
            'id': comment.id,
            'content': comment.content,
            'at_users': comment.at_users,
            'up_count': comment.up_count,
            'down_count': comment.down_count,
            'created_at': comment.created_at,
            'user_id': comment.user_id,
            'reply_count': comment.reply_count,
            'root_id': comment.root_id,
            'parent_id': comment.parent_id,
            'parent_user_id': comment.parent_user_id,
            'is_voted': 0,
            'score': comment.get_score(sort_type),
            'detected_lang': comment.detected_lang.name if comment.detected_lang else None,
            'status': comment.status.name,
        }
        if comment.highlighted:
            comment_dict['highlighted'] = True

        # 前端需要设置一级评论的 root_id 为 自己的 id
        if comment.root_id is None and comment.parent_id is None:
            comment_dict['root_id'] = comment.id

        return comment_dict

    @classmethod
    def _transform_user_info(cls, user_info_map: dict, source_dict: dict = None, source_key: str = None) -> dict:
        """转换用户信息格式并处理源字典
        从源字典中提取用户ID并删除该字段，然后从用户信息映射表中获取完整信息并确保包含user_id

        Args:
            user_info_map: 用户信息映射表
            source_dict: 源字典，包含user_id的字典
            source_key: 要获取和删除的user_id的键名
        Returns:
            dict: 包含user_id的用户信息字典，即使在user_info_map中未找到用户信息也会返回包含user_id的字典
        """
        if not source_dict or not source_key:
            return {}
        
        user_id = source_dict.pop(source_key, None)
        if not user_id:
            return None
            
        return user_info_map.get(user_id, {}) | {'user_id': user_id}

    @classmethod
    def _fill_user_info(cls, comment_list: list[dict]) -> None:
        """填充评论列表中的用户信息
        Args:
            comment_list: 评论列表
        """
        # 收集所有需要查询的用户ID
        user_ids = set([c['user_id'] for c in comment_list])
        at_user_ids = set([
            at_user['user_id']
            for c in comment_list
            if c.get('at_users')
            for at_user in c['at_users']
            if at_user.get('user_id')
        ])
        parent_user_ids = set([
            c['parent_user_id']
            for c in comment_list
            if c.get('parent_user_id')
        ])

        # 获取所有用户信息
        user_info_map = UserInfoCache().get_user_info(user_ids | at_user_ids | parent_user_ids)

        # 填充评论作者信息
        for c in comment_list:
            # 填充评论作者信息
            c['created_by'] = cls._transform_user_info(user_info_map, c, 'user_id')

            # 填充父评论作者信息
            c['parent_user'] = cls._transform_user_info(user_info_map, c, 'parent_user_id')

            # 填充@用户信息
            if at_users := c.get('at_users'):
                for at_user in at_users:
                    at_user_info = cls._transform_user_info(user_info_map, at_user, 'user_id')
                    at_user.update(at_user_info)

    @classmethod
    def _fill_is_voted(cls, comment_list: list[dict], user_id: int | None) -> None:
        if not user_id:
            return
        comment_ids = [c['id'] for c in comment_list]
        votes = CommentVote.query.filter(
                CommentVote.comment_id.in_(comment_ids),
                CommentVote.user_id == user_id
        ).all()
        vote_map = {vote.comment_id: vote.vote for vote in votes}
        for comment in comment_list:
            comment['is_voted'] = vote_map.get(comment['id'], 0)

    @classmethod
    def _fill_tips_info(cls, comment_list: list[dict], user_id: int | None) -> None:
        
        comment_ids = [c['id'] for c in comment_list]

        stats_data = CommentStatistics.get_comment_statistics_map(comment_ids)
        tip_user_count_map = {}
        for k, v in stats_data.items():
            tip_user_count_map[k] = v['tip_user_count']
        tipped_comment_ids = set()
        if user_id:
            tips = CommentTip.query.filter(
                CommentTip.comment_id.in_(comment_ids),
                CommentTip.send_user_id == user_id
            ).with_entities(
                CommentTip.comment_id
            ).all()
            tipped_comment_ids = {tip.comment_id for tip in tips}
        for comment in comment_list:
            comment['tip_user_count'] = tip_user_count_map.get(comment['id'], 0)
            comment['is_tipped'] = comment['id'] in tipped_comment_ids

    @classmethod
    def _query_comments(cls, *, sort_type: Comment.SortType, query_params: dict,
                        last_score: float = None, last_id: int = None,
                        limit: int = 20, user_id: int = None) -> list:
        """通用的评论查询方法
        Args:
            sort_type: 排序类型(必需)
            query_params: 查询参数,用于构造缓存和数据库查询
            last_score: 上一页最后一条评论的分数
            last_id: 上一页最后一条评论的ID
            limit: 每页数量
            user_id: 当前用户ID
        """
        # 1. 构造并获取缓存
        if (last_score is not None and last_id is None) or (last_score is None and last_id is not None):
            raise InvalidArgument('last_score 和 last_id 必须同时存在或同时不存在')

        cache = CommentListCache(sort_type=sort_type, **query_params)

        # 2. 获取分页数据
        comment_ids = cache.paginate(last_score=last_score, last_id=last_id, limit=limit)
        comments_in_cache: list = CommentCache.get_comments_from_ids(comment_ids)
        # 同分时，comment_id是不按顺序排列的，这里要按id倒序排一下
        comments_in_cache.sort(key=lambda _cache: (_cache.get_score(sort_type), _cache.id), reverse=True)
        cache_len = len(comments_in_cache)

        # 3. 如果缓存数据不足，从数据库补充
        comments_in_db = []
        if cache_len < limit:
            if cache_len > 0:
                last_comment_in_cache = comments_in_cache[-1]
                query_after_score = last_comment_in_cache.get_score(sort_type)
                query_after_id = last_comment_in_cache.id
            else:
                query_after_score = last_score
                query_after_id = last_id

            # 从数据库查询补充数据
            comments_in_db = Comment.get_comments(
                sort_type=sort_type,
                last_score=query_after_score,
                last_id=query_after_id,
                limit=limit - cache_len,
                **query_params
            )

        # 4. 补充用户自己刚发布未审核评论
        pending_comments = []
        if not last_id and user_id:
            pending_cache = UserPendingCommentsCache(user_id)
            pending_comments = pending_cache.query_comments(
                sort_type=sort_type,
                **query_params
            )

        return pending_comments + comments_in_cache + comments_in_db

    @classmethod
    def _check_highlight_comment(cls, highlight_comment_id: int, lang: Language) -> Comment | None:
        """检查高亮评论是否存在"""
        if highlight_comment_id is None:
            return None
        highlight_comment = CommentCache(highlight_comment_id).get_comment(aside=True)
        if highlight_comment is None:
            raise CommentNotFound
        if highlight_comment.lang != lang:
            return None
        return highlight_comment

    @classmethod
    def _remove_comment_from_list(cls, comment_id: int, comment_list: list[Comment]):
        """从列表中删除评论"""
        for i, comment in enumerate(comment_list):
            if comment.id == comment_id:
                comment_list.pop(i)
                break

    @classmethod
    def get_root_comments(cls, business: Comment.Business, business_id: str,
                          lang: Language, sort_type: Comment.SortType,
                          last_score: float = None, last_id: int = None,
                          limit: int = 20, highlight_comment_id: int = None, user_id: int = None) -> list:
        """分页获取评论列表"""

        highlight_comment = cls._check_highlight_comment(highlight_comment_id, lang)

        query_params = {
            'business': business,
            'business_id': business_id,
            'lang': lang
        }

        comments = cls._query_comments(
            sort_type=sort_type,
            query_params=query_params,
            last_score=last_score,
            last_id=last_id,
            limit=limit,
            user_id=user_id
        )

        # 补充高亮及相关一级评论
        if highlight_comment:
            highlight_comments = []
            highlight_comment.highlighted = True
            if highlight_comment.root_id:
                root_highlight_comment = CommentCache(highlight_comment.root_id).get_comment(aside=True)
                root_highlight_comment.highlighted = True
                highlight_comments.append(root_highlight_comment)
                root_highlight_id = root_highlight_comment.id
            else:
                root_highlight_id = highlight_comment.id
            highlight_comments.append(highlight_comment)
            # 如果 comments 列表里，有 highlight 一级评论的这条，就把它删除
            cls._remove_comment_from_list(root_highlight_id, comments)
            return highlight_comments + comments

        return comments

    @classmethod
    def get_comment_replies(cls, root_id: int, user_id: int = None,
                           last_score: float = None, last_id: str = None,
                           limit: int = 20, highlight_comment_id: int = None) -> list:
        """获取评论的回复列表"""
        root = Comment.query.filter(Comment.id == root_id).with_entities(Comment.lang).first()
        lang = root.lang
        highlight_comment = cls._check_highlight_comment(highlight_comment_id, lang)

        query_params = {'root_id': root_id}

        replies = cls._query_comments(
            sort_type=Comment.SortType.TOP,
            query_params=query_params,
            last_score=last_score,
            last_id=last_id,
            limit=limit,
            user_id=user_id
        )

        if highlight_comment:
            # 如果 replies 列表里，有 highlight 评论的这条，就把它删除
            cls._remove_comment_from_list(highlight_comment.id, replies)
            replies = [highlight_comment] + replies

        cls._fill_parent_user_ids(replies)

        return replies

    @classmethod
    def _fill_parent_user_ids(cls, replies):
        parent_ids = set(reply.parent_id for reply in replies if reply.parent_id is not None)
        parent_comments_dict = CommentCache.batch_read(parent_ids, ['user_id'])

        missing_parent_ids = parent_ids - parent_comments_dict.keys()
        if missing_parent_ids:
            parent_comments = Comment.query.filter(
                Comment.id.in_(missing_parent_ids)
            ).with_entities(
                Comment.id, Comment.user_id
            ).all()
            parent_comments_dict.update(
                {pc.id: {"user_id": pc.user_id} for pc in parent_comments}
            )

        for reply in replies:
            if parent_dict := parent_comments_dict.get(reply.parent_id):
                reply.parent_user_id = int(parent_dict.get('user_id'))

    @classmethod
    def create_comment(cls, user_id: int, content: str, lang: Language,
                       business: Comment.Business = None, business_id: str = None, business_code: str = None,
                       root_id: int = None, parent_id: int = None, at_users: list = None) -> Comment:
        """创建评论的公共方法
        Args:
            user_id: 用户ID
            content: 评论内容
            lang: 评论语区
            business: 业务代码，比如 'COIN'
            business_id: 业务ID，比如 '1', 'AB126FD'
            business_code: 业务编码，比如 'BTC'（可选）
            root_id: 根评论ID(可选)
            parent_id: 父评论ID(可选)
            at_users: 提及的用户列表(可选)
        Returns:
            Comment: 新创建的评论对象
        """
        event_to_user_id = user_id

        if parent_id:
            if not root_id:
                raise InvalidArgument("Root comment ID is required")

            # 检查父评论是否存在且状态有效
            parent_comment = CommentManager.get_valid_comment(parent_id, raise_err=True)

            if root_id and root_id != parent_id:
                CommentManager.get_valid_comment(root_id, raise_err=True)

            # 对于回复，从父评论继承业务信息
            business = parent_comment.business
            business_id = parent_comment.business_id
            business_code = parent_comment.business_code
            event_to_user_id = parent_comment.user_id
            parent_user_id = parent_comment.user_id
        else:
            parent_user_id = None

        # 创建评论
        content = clean_whitespace(content)

        if not content or content.isspace():
            raise InvalidArgument("Comment content is required")
        if len(content) > 1000:
            raise InvalidArgument("Content is too long")

        # 忽略掉语言无关的极简字符串
        is_simple = False
        if check_simple_text(content):
            detected_lang = None
            is_simple = True
        else:
            detected_lang = detect_lang(content)

        if (not is_simple) and (detected_lang is not None) and (detected_lang != Language.EN_US) :
            # 评论/回复 内容为简单字符 或者 识别语言为空/英文，都可以发表在任意语区
            if lang == Language.EN_US and detected_lang != lang:
                # 如果提交语区是英文，且识别语言与提交语区不一致
                raise CommentLangMismatch()

        sanitized_content = sanitize(content)

        # 计算内容哈希
        content_hash = hashlib.md5(sanitized_content.encode('utf-8')).hexdigest()

        if cls._check_redundant_content(content_hash, user_id):
            # 如果内容重复，抛出异常
            raise CommentDuplicate()

        comment = Comment(
            business=business,
            business_id=business_id,
            business_code=business_code,
            lang=lang,
            detected_lang=detected_lang,
            is_simple=is_simple,
            user_id=user_id,
            parent_id=parent_id,
            root_id=root_id,
            content=sanitized_content,
            content_hash=content_hash,
            at_users=at_users,
            parent_user_id=parent_user_id,
        )
        db.session_add_and_flush(comment)
        comment.new_score = CommentScoreCalculator.cal(Comment.SortType.NEW, comment)
        moderation = CommentModeration.create_initial_moderation(comment.id)

        db.session.commit()

        CommentEventManager.new_comment_event(
            comment.id, user_id,
            to_user_id=event_to_user_id,
            lang=comment.lang,
            event_type=CommentEvent.EventType.REPLY if parent_id else CommentEvent.EventType.COMMENT,
        )

        # 添加到用户的待审核缓存
        pending_cache = UserPendingCommentsCache(user_id)
        pending_cache.add_comment(comment)

        if comment.at_users:
            # 更新提及用户的信息
            for at_user in at_users:
                at_user_id = at_user.get('user_id')
                if not at_user_id:
                    continue
                UserManager.update_user(
                    at_user_id,
                    at_user.get('name'),
                    at_user.get('account_name'),
                    at_user.get('avatar'),
                )
        # 启动初审流程
        initial_moderate.delay(moderation.id)

        # 创建全文检索记录
        create_comment_fulltext_search.delay(comment.id, lang.name, comment.content)
        return comment

    @classmethod
    def _check_redundant_content(cls, content_hash, user_id) -> bool:
        # 查询近2小时内的5条评论的content_hash
        two_hours_ago = datetime.now(UTC) - timedelta(hours=2)
        recent_hashes = db.session.query(Comment.content_hash).filter(
            Comment.user_id == user_id,
            Comment.created_at >= two_hours_ago
        ).order_by(Comment.id.desc()).limit(5).all()
        # 统计连续重复数量
        repeat_count = 0
        for hash_row in recent_hashes:
            if hash_row.content_hash == content_hash:
                repeat_count += 1
                if repeat_count >= 5:
                    return True
            else:
                repeat_count = 0
        return False

    @classmethod
    def publish(cls, comment: int | Comment):
        """发布评论（审核通过）
        Args:
            comment: 评论或评论id
        """
        try:
            # 获取评论对象
            if isinstance(comment, int):
                comment_id = comment
                comment = CommentCache(comment_id).get_comment(aside=True)
            else:
                comment_id = comment.id

            if not comment:
                return

            _logger.warning('comment_id : {} publish'.format(comment_id))

            origin_status = comment.status
            if origin_status not in [Comment.CommentStatus.CREATED, Comment.CommentStatus.DISABLED]:
                raise InvalidArgument(message="Comment status is not allowed.")

            # 计算分数，并更新状态
            CommentScoreCalculator.update_comment_score(comment)

            # 更新数据库：更新评论状态、计数等
            Comment.query.filter_by(id=comment.id).update({
                'up_count': comment.up_count,
                'down_count': comment.down_count,
                'hot_score': comment.hot_score,
                'top_score': comment.top_score,
                'status': Comment.CommentStatus.PUBLISHED,
                'status_updated_at': now(),
            })
            db.session.commit()
        except Exception as e:
            _logger.error(f"publish error: failed to update database, comment ID {comment.id}: {str(e)}")
            db.session.rollback()
            raise

        # -----------------------------
        # 以下步骤为缓存及其它非核心更新，出现异常时只记录日志，不中断后续流程
        # -----------------------------

        # 1. 更新父评论的回复数
        try:
            if comment.root_id:
                root_cache = CommentCache(comment.root_id)
                root_cache.incr_reply_count(1)
                # 注意：对应数据库数据由定时任务更新
        except Exception as e:
            _logger.error(f"publish error: failed to update parent comment reply count, comment ID {comment_id}: {str(e)}")

        # 2. 更新评论详情缓存
        try:
            comment_cache = CommentCache(comment_id)
            comment_cache.update({
                'vote_count': comment.up_count - comment.down_count,
                'hot_score': comment.hot_score,
                'top_score': comment.top_score,
                'status': Comment.CommentStatus.PUBLISHED.name,
            })
        except Exception as e:
            _logger.error(f"publish error: failed to update comment detail cache, comment ID {comment_id}: {str(e)}")

        # 3. 从待审核缓存中移除评论
        try:
            pending_cache = UserPendingCommentsCache(comment.user_id)
            pending_cache.remove_comment(comment_id)
        except Exception as e:
            _logger.error(f"publish error: failed to remove comment from pending cache, comment ID {comment_id}: {str(e)}")

        # 4. 更新评论列表缓存（添加到各个相关列表中）
        try:
            list_caches = CommentListCache.get_instances_from_comment(comment)
            for list_cache in list_caches:
                list_cache.add_comment(comment)
        except Exception as e:
            _logger.error(f"publish error: failed to update comment list cache, comment ID {comment_id}: {str(e)}")

        # 5. 更新一级评论计数缓存
        try:
            if not comment.root_id:
                count_cache = CommentCountCache(comment.business, comment.business_id, comment.lang)
                count_cache.incr(1)
        except Exception as e:
            _logger.error(f"publish error: failed to update root comment count cache, comment ID {comment_id}: {str(e)}")

        # 6. 更新用户维度统计（针对 DISABLED 状态下修正统计）
        try:
            if origin_status == Comment.CommentStatus.DISABLED:
                CommentUserDisableCountCache().hincrby(str(comment.user_id), -1)
        except Exception as e:
            _logger.error(f"publish error: failed to update user statistics cache, comment ID {comment_id}: {str(e)}")

        # 7. 记录 @ 用户事件
        try:
            if comment.at_users:
                for at_user in comment.at_users:
                    at_user_id = at_user.get('user_id')
                    if not at_user_id:
                        continue
                    CommentEventManager.new_comment_event(
                        comment.id, comment.user_id,
                        to_user_id=at_user_id,
                        lang=comment.lang,
                        event_type=CommentEvent.EventType.AT,
                    )
        except Exception as e:
            _logger.error(f"publish error: failed to record @ user events, comment ID {comment_id}: {str(e)}")

    @classmethod
    def disable(cls, comment: int | Comment):
        """禁止评论（审核不通过）
        Args:
            comment: 评论或评论id
        """
        if isinstance(comment, int):
            comment = Comment.query.get(comment)
        if not comment:
            return

        if comment.status not in [Comment.CommentStatus.PUBLISHED, Comment.CommentStatus.CREATED]:
            raise InvalidArgument(message="Comment status is not allowed.")

        if comment.status == Comment.CommentStatus.CREATED:
            # 如果是未审核状态，直接删除, 不用再处理各种计数等逻辑
            try:
                # 核心数据库更新操作
                comment.status_updated_at = now()
                comment.status = Comment.CommentStatus.DISABLED
                db.session.commit()
            except Exception as e:
                _logger.error(f"de_publish error: failed to update database, comment ID {comment.id}: {str(e)}")
                db.session.rollback()
                raise
            try:
                # 从待审核缓存中移除评论
                pending_cache = UserPendingCommentsCache(comment.user_id)
                pending_cache.remove_comment(comment.id)
            except Exception as e:
                _logger.error(
                    f"publish error: failed to remove comment from pending cache, comment ID {comment.id}: {str(e)}")
                    
            try:
                # 更新评论缓存
                comment_cache = CommentCache(comment.id)
                comment_cache.update_status(comment.status)
            except Exception as e:
                _logger.error(f"de_publish error: failed to update comment cache status, comment ID {comment.id}: {str(e)}")


        else:
            cls._de_publish(comment, Comment.CommentStatus.DISABLED)

        # 更新用户维度统计(admin)
        try:
            CommentUserDisableCountCache().hincrby(str(comment.user_id), 1)
        except Exception as e:
            _logger.error(f"publish error: failed to update user statistics cache, comment ID {comment.id}: {str(e)}")

    @classmethod
    def _de_publish(cls, comment, target_status):
        """取消发布评论，禁用和删除共享的逻辑"""
        comment_id = comment.id

        _logger.warning('comment_id : {} de_publish'.format(comment_id))

        try:
            # 核心数据库更新操作
            origin_status = comment.status
            comment.status = target_status
            if origin_status == Comment.CommentStatus.PUBLISHED:
                comment.status_updated_at = now()
            db.session.commit()
        except Exception as e:
            _logger.error(f"de_publish error: failed to update database, comment ID {comment_id}: {str(e)}")
            db.session.rollback()
            raise

        # -----------------------------
        # 以下步骤为缓存及其它非核心更新，出现异常时只记录日志，不中断后续流程
        # -----------------------------

        # 1. 更新评论缓存
        try:
            comment_cache = CommentCache(comment_id)
            comment_cache.update_status(target_status)
        except Exception as e:
            _logger.error(f"de_publish error: failed to update comment cache status, comment ID {comment_id}: {str(e)}")

        # 2. 更新父评论的回复数
        try:
            if comment.root_id:
                root_cache = CommentCache(comment.root_id)
                reply_count = root_cache.get_reply_count()
                if reply_count and int(reply_count) > 0:
                    root_cache.incr_reply_count(-1)
                else:
                    _logger.error(f"de_publish error: try to decrease reply_count on 0, comment ID {comment_id}: {comment.root_id}")
                # 数据库在定时任务中更新
        except Exception as e:
            _logger.error(f"de_publish error: failed to update parent comment reply count, comment ID {comment_id}: {str(e)}")

        # 3. 从待审核缓存中移除
        try:
            pending_cache = UserPendingCommentsCache(comment.user_id)
            pending_cache.remove_comment(comment_id)
        except Exception as e:
            _logger.error(f"de_publish error: failed to remove from pending cache, comment ID {comment_id}: {str(e)}")

        # 4. 更新评论列表缓存
        try:
            list_caches = CommentListCache.get_instances_from_comment(comment)
            for list_cache in list_caches:
                list_cache.remove_comment(comment_id)
        except Exception as e:
            _logger.error(f"de_publish error: failed to update comment list cache, comment ID {comment_id}: {str(e)}")

        # 5. 更新评论计数缓存
        try:
            if not comment.root_id and origin_status == Comment.CommentStatus.PUBLISHED:
                count_cache = CommentCountCache(comment.business, comment.business_id, comment.lang)
                if count_cache.get_count() > 0:
                    count_cache.decr(1)
                else:
                    _logger.error(f"de_publish error: try to decrease count_cache on 0, comment ID {comment_id}: " +
                                  f"{comment.business}, {comment.business_id}, {comment.lang}")
        except Exception as e:
            _logger.error(f"de_publish error: failed to update comment count cache, comment ID {comment_id}: {str(e)}")

    @classmethod
    def delete_comment(cls, comment: int | Comment, user_id: int = None):
        """删除评论

        Args:
            comment: 评论ID或评论对象
            user_id: 操作用户
        Returns:
            bool: 删除是否成功
        """
        # 获取评论对象
        if isinstance(comment, int):
            comment = Comment.query.get(comment)
            if not comment:
                _logger.error(
                    f"Comment {comment} not found when delete by user {user_id}")
                raise CommentNotFound

        if user_id and comment.user_id != user_id:
            _logger.error(
                f"User {user_id} attempt to delete comment {comment.id} created by user {comment.user_id}")
            raise InvalidAccount

        if comment.status == Comment.CommentStatus.DELETED:
            raise CommentDeleted

        origin_status = comment.status
        if origin_status in [Comment.CommentStatus.DISABLED, Comment.CommentStatus.CREATED]:
            # 如果本来就是禁用状态或者创建状态，还没审核，直接删除，不用再处理各种计数等逻辑
            comment_id = comment.id

            if origin_status == Comment.CommentStatus.CREATED:
                # 从待审核缓存中移除
                try:
                    pending_cache = UserPendingCommentsCache(comment.user_id)
                    pending_cache.remove_comment(comment_id)
                except Exception as e:
                    _logger.error(
                        f"de_publish error: failed to remove from pending cache, comment ID {comment_id}: {str(e)}"
                    )
            # 从 DISABLED 状态下删除评论，按理说，也应该算作被 DISABLE 过一次（发表过不适宜的言论）
            # 产品当前定义是只算处于 DISABLED 状态的评论，所以之前有下面两句
            # 不过目前是直接从表中统计的，这个缓存，只用于记录曾经被 DISABLED 的用户列表，所以就不再 -1 啦
            # 将来如果需要调整逻辑，本缓存还能起到作用
            # if origin_status == Comment.CommentStatus.DISABLED:
            #     CommentUserDisableCountCache().hincrby(str(comment.user_id), -1)

            try:
                comment.status = Comment.CommentStatus.DELETED
                db.session.commit()
            except Exception as e:
                _logger.error(f'delete comment error: failed to update database, comment ID {comment_id}: {str(e)}')
                db.session.rollback()
                raise

            try:
                comment_cache = CommentCache(comment_id)
                comment_cache.update_status(Comment.CommentStatus.DELETED)
            except Exception as e:
                _logger.error(f"delete comment error: failed to update comment cache status, comment ID {comment_id}: {str(e)}")

            # 更新评论列表缓存
            try:
                list_caches = CommentListCache.get_instances_from_comment(comment)
                for list_cache in list_caches:
                    list_cache.remove_comment(comment_id)
            except Exception as e:
                _logger.error(
                    f"de_publish error: failed to update comment list cache, comment ID {comment_id}: {str(e)}"
                )
            return

        cls._de_publish(comment, Comment.CommentStatus.DELETED)

    @staticmethod
    def get_valid_comment(comment_id: int, from_cache=False, raise_err: bool = True) -> Comment | None:
        """获取有效的评论"""
        if from_cache:
            comment = CommentCache(comment_id).get_comment(aside=True)
        else:
            comment = Comment.query.get(comment_id)

        if not comment:
            if raise_err:
                raise CommentNotFound
            return None

        if comment.status == Comment.CommentStatus.DELETED:
            if raise_err:
                raise CommentDeleted
            return None

        if comment.status == Comment.CommentStatus.DISABLED:
            raise CommentDisabled

        return comment

    @staticmethod
    def get_valid_comments(comment_ids: [int]):
        if not comment_ids:
            return {}
        comments = Comment.query.filter(
            Comment.id.in_(comment_ids), Comment.status != Comment.CommentStatus.DELETED,
        ).all()
        return {
            comment.id: comment
            for comment in comments
        }

    @staticmethod
    def get_comment_content_by_ids(comment_ids: [int]):
        if not comment_ids:
            return {}
        comments = Comment.query.with_entities(
            Comment.id,
            Comment.content,
            Comment.status,
            Comment.root_id,
            Comment.parent_id,
            Comment.business,
            Comment.business_id,
            Comment.business_code,
            Comment.user_id,
        ).filter(
            Comment.id.in_(comment_ids)
        ).all()
        return {
            comment.id: comment
            for comment in comments
        }

    @classmethod
    def get_comment_count(cls, business, business_id, lang):
        """获取评论数量"""
        count_cache = CommentCountCache(business, business_id, lang)
        return count_cache.get_count()

    @classmethod
    def get_reply_count(cls, root_id):
        """获取评论数量"""
        cache = CommentCache(root_id)
        return cache.get_reply_count()

    @staticmethod
    def get_new_reply_count(status_updated_at):
        new_root_commend_ids = [
            cv.root_id
            for cv in Comment.query.with_entities(
                Comment.root_id
            ).filter(
                Comment.status_updated_at >= status_updated_at,
                Comment.parent_id != None,
            ).distinct(
                Comment.root_id
            ).all()
        ]

        res = {}
        for _new_root_commend_ids in batch_iter(new_root_commend_ids, 100):
            # 此处置为0，可以保证当变化的根评论没有回复时（比如所有回复都删除了），也正确更新回复数
            for root_commend_id in _new_root_commend_ids:
                res[root_commend_id] = 0

            query = db.session.query(
                Comment.root_id,
                func.count().label('reply_count'),
            ).filter(
                Comment.root_id.in_(_new_root_commend_ids),
                Comment.status == Comment.CommentStatus.PUBLISHED,
            ).group_by(Comment.root_id)

            for _res in query.all():
                res[_res.root_id] = _res.reply_count

        return res

    @staticmethod
    def get_new_root_comment_business_infos(status_updated_at):
        limit = 100
        offset = 0
        business_infos = set()
        while (new_comments := Comment.query.with_entities(
            Comment.business,
            Comment.business_id,
            Comment.lang,
        ).filter(
            Comment.status_updated_at >= status_updated_at,
        ).offset(offset * limit).limit(limit).all()):
            for comment in new_comments:
                business_infos.add(
                    (comment.business, comment.business_id, comment.lang)
                )

            offset += 1

        return business_infos

    @staticmethod
    def get_last_comment_time(business, business_id, lang) -> int | None:
        new_list_cache = CommentListCache(business, business_id, lang)
        last_comment_id = new_list_cache.get_max_score_comment_id()
        if last_comment_id is None:
            return None
        comment = CommentCache(last_comment_id).read_aside()
        return comment.get('created_at', 0)


class CommentVoteManager:

    @staticmethod
    def add_vote(comment_id: int, user_id: int, vote_type: int):
        comment = CommentManager.get_valid_comment(comment_id)

        # 更新投票表
        cv = CommentVote.query.filter(
            CommentVote.comment_id == comment_id,
            CommentVote.user_id == user_id,
        ).first()
        has_cancel = False
        if cv:
            if cv.vote != vote_type:
                if cv.vote != CommentVote.VoteType.DELETED.value:
                    has_cancel = True
                cv.vote = vote_type
            else:
                return
        else:
            db.session.add(
                CommentVote(
                    comment_id=comment_id, user_id=user_id, vote=vote_type, comment_user_id=comment.user_id,
                )
            )

        # 更新评论缓存
        up_incr = 0
        down_incr = 0
        if vote_type == CommentVote.VoteType.UP.value:
            up_incr = 1
            if has_cancel:
                down_incr = -1
        else:
            down_incr = 1
            if has_cancel:
                up_incr = -1

        comment_cache = CommentCache(comment_id)
        comment_cache.read_aside()  # 保证缓存存在
        comment_cache.incr_vote_count(up_incr, down_incr)

        # 记录event
        CommentEventManager.new_comment_event(
            comment_id, user_id, comment.user_id, comment.lang,
            CommentEvent.EventType.UP if vote_type == CommentVote.VoteType.UP.value else CommentEvent.EventType.DOWN,
            need_commit=False
        )

        db.session.commit()

    @staticmethod
    def remove_vote(comment_id: int, user_id: int, vote_type: int):
        comment = CommentManager.get_valid_comment(comment_id)

        cv = CommentVote.query.filter(
            CommentVote.comment_id == comment_id,
            CommentVote.user_id == user_id,
            CommentVote.vote == vote_type
        ).first()
        if not cv:
            return

        # 更新评论缓存
        up_incr = 0
        down_incr = 0
        if vote_type == CommentVote.VoteType.UP.value:
            up_incr = -1
        else:
            down_incr = -1

        comment_cache = CommentCache(comment_id)
        comment_cache.read_aside()  # 保证缓存存在
        comment_cache.incr_vote_count(up_incr, down_incr)

        # 更新投票表
        cv.vote = CommentVote.VoteType.DELETED.value
        db.session.commit()

    @staticmethod
    def get_new_vote_count(updated_at):
        new_commend_ids = [
            cv.comment_id
            for cv in CommentVote.query.with_entities(
                CommentVote.comment_id
            ).filter(
                CommentVote.updated_at >= updated_at
            ).distinct(
                CommentVote.comment_id
            ).all()
        ]

        res = {}
        for _new_commend_ids in batch_iter(new_commend_ids, 100):
            query = db.session.query(
                CommentVote.comment_id,
                func.sum(case((CommentVote.vote == CommentVote.VoteType.UP.value, 1), else_=0)).label('up_count'),
                func.sum(case((CommentVote.vote == CommentVote.VoteType.DOWN.value, 1), else_=0)).label('down_count')
            ).filter(CommentVote.comment_id.in_(_new_commend_ids)).group_by(CommentVote.comment_id)

            for _res in query.all():
                res[_res.comment_id] = (_res.up_count, _res.down_count)

        return res


class CommentTranslateManager:
    """
    评论翻译管理类
    """

    @staticmethod
    def translate_comment(comment_id: int, target: Language, current_lang: str = 'en_US') -> str:
        ct = CommentTranslation.query.filter_by(
            comment_id=comment_id,
            lang=target.name
        ).first()
        if not ct:
            comment = Comment.query.get(comment_id)
            translated_content = None
            translator_used = "ai"
            if comment.is_simple:
                return comment.content

            # 目前ai翻译评论时不会填充源语言，这里为了减少改动先传一个默认语言
            # TODO 翻译接口 source 参数改为非必须
            source_lang = comment.detected_lang
            if source_lang is None:
                if target == Language.EN_US:
                    source_lang = Language.ZH_HANS_CN
                else:
                    source_lang = Language.EN_US
            try:
                ai_translator = AITranslateClient()
                ret = ai_translator.translate(comment.content, source_lang, target,
                                              model_type=ModelType.BALANCED, business=AITranslateBusiness.COIN_COMMENT)

                translated_content = ret.content
            except Exception as e:
                _logger.error(f"AI translation failed: {str(e)}, falling back to Google Translate")
                try:
                    google_translator = GoogleTranslate(
                        config['GOOGLE_TRANSLATE_KEY'],
                        source_lang,
                        target
                    )
                    translated_content = google_translator.translate(comment.content)
                    translator_used = "google"
                except Exception as e:
                    _logger.error(f"Google translation also failed: {str(e)}")
                    try:
                        lang = Language[current_lang.upper()]
                    except (KeyError, ValueError):
                        lang = Language.EN_US
                    raise TranslationError(lang=lang.name)

            ct = CommentTranslation(
                comment_id=comment_id,
                lang=target.name,
                content=translated_content,
                translator=translator_used,
                translated_by=g.user_id
            )
            try:
                db.session.add(ct)
                db.session.commit()
            except Exception as e:
                _logger.error(f"Failed to save translation: {str(e)}")
                db.session.rollback()
                return translated_content
            return translated_content

        return ct.content
