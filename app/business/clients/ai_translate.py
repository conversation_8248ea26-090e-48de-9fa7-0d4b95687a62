# -*- coding: utf-8 -*-
import inspect
import json
import re
from dataclasses import dataclass
from enum import Enum
from typing import Dict, NamedTuple, Optional, List

from flask import current_app

from app.common.constants import Language
from app.config import config as app_config
from app.utils import RESTClient, BaseHTTPClient, g_map


class AITranslateBusiness(Enum):
    COIN_COMMENT = 'coin_comment'


class FormatType(Enum):
    # 文档格式
    TEXT = 'text'
    MARKDOWN = 'markdown'
    HTML = 'html'
    JSON = 'json'


class ModelFamilyName(Enum):
    # 采用哪个平台的模型
    CLAUDE = 'claude'
    GPT = 'gpt'


class ModelType(Enum):
    # 模型类型，不同平台，不同模型类型，对应了哪个实际的模型，翻译服务器会动态调整
    CHEAP = 'cheap'  # 高性价比，速度快
    POWERFUL = 'powerful'  # 高性能，速度慢
    BALANCED = 'balanced' # 性能和费用均衡的模型

class TermsEnum(Enum):
    DEFAULT_TERMS = "DEFAULT_TERMS"
    INSIGHT_TERMS = "INSIGHT_TERMS"


@dataclass
class TransContent:
    content: str
    format_type: Optional[FormatType | str] = None
    business_id: Optional[str] = None
    business_info: Optional[str] = None


@dataclass
class TransTarget:
    lang: Language | str
    business_id: Optional[str] = None
    business_info: Optional[str] = None

    def _str_(self):
        return self.lang.name if isinstance(self.lang, Language) else str(self.lang)


@dataclass
class Business:
    id: str
    info: Optional[str] = None
    name: Optional[str] = None


class AITranslateClient:
    @dataclass
    class Response:
        content: Optional[str | dict | list] = None
        format_type: Optional[str] = None
        source: Optional[str] = None
        target: Optional[str | List[str]] = None
        input_tokens: Optional[int] = None
        output_tokens: Optional[int] = None
        status: Optional[str] = None

    def __init__(self, *,
                 format_type: FormatType = None,
                 terms: TermsEnum = None,
                 # business: TranslationTask.Business = None,
                 model_family=None, model_type=None):
        kwargs = get_keyword_arguments(self.__init__, locals())

        conf = app_config['AI_TRANSLATION_CONFIG']
        self.client: RESTClient = RESTClient(conf['url'])
        self._config: dict = kwargs

    def translate(self, content: str | dict,
                  source: Language | str, target: Language | str | List[Language] | List[str], *,
                  business: AITranslateBusiness | None = None,
                  format_type: FormatType = None, terms: TermsEnum = None,
                  model_family=None, model_type=None) \
            -> Response:
        """同步翻译
        @param content: 翻译内容，可以是要翻译的文本字符串，也可以是个字典，key 是翻译后的 key，value 是要翻译的内容,
        value 的值可以是字符串，也可以是 TransContent 对象，TransContent 可以通过 format_type 属性分别设置文本格式
        @param source: 源语言，可以是 Language 枚举，也可以是字符串（全大写，例如 JA_JP, ZH_HANS_CN）
        @param target: 目标语言，可以和 source 一样是单一的语言，也可以是语言的列表
        @param business: 业务类型
        @param format_type: 文档格式，可以是 TEXT, MARKDOWN, HTML
        @param terms: 术语表
        @param model_family: 模型族类型，GPT或者CLAUDE
        @param model_type: LLM 模型类型的 content 是翻译结果字符串；
        @return: 翻译结果，如果 target 参数为列表，那么返回值的 content 是个字典，key 是 target 语言，value 是翻译结果；
        如果 content 是字典，那么返回值的 content 也是字典，key 是原来的 key，value 是翻译结果；否则返回值；
        如果 target 为列表，同时 content 是字典，那么返回值的 content 是个多重嵌套的字典，target 在外层，content 在内层。
        """

        kwargs = get_keyword_arguments(self.translate, locals(), check_none=True)
        config_args = merge_without_none(self._config, kwargs)

        source = source.name if isinstance(source, Language) else str(source)

        if isinstance(target, list):
            return self._translate_multi_targets(
                content,
                source,
                target,
                config_args
            )
        else:
            target = target.name if isinstance(target, Language) else str(target)
            return self._translate_single_target_single_content(
                content,
                source,
                target,
                config_args
            )

    def _translate_multi_targets(self, content: str, source: str, targets: List[Language] | List[str],
                                 config_args: dict) -> Response:
        """同步翻译多目标语言
        说明：
        * source 是固定的，targets 可以有多个
        * http 请求是并发调用的，但 translate 方法是同步的，所以这里会按最慢的一条来返回，
        还是可能出现阻塞超时，要完全避免超时，请使用异步调用；
        不过同步的好处是可以直接返回结果。
        """
        sources = [source] * len(targets)
        contents = [content] * len(targets)
        config_args_list = [config_args] * len(targets)
        targets = [target.name if isinstance(target, Language) else str(target) for target in targets]
        format_type = config_args.get('format_type') if 'format_type' in config_args else None

        if isinstance(content, list):
            g_ret = g_map(self._translate_single_target_multi_content, contents, sources, targets,
                          config_args_list, size=20)
        else:
            g_ret = g_map(self._translate_single_target_single_content, contents, sources, targets, config_args_list,
                          size=20)

        merged_resp = {}
        sum_input = 0
        sum_output = 0
        ret_format_type = g_ret[0].format_type if format_type is None else format_type
        for resp in g_ret:
            k = resp.target
            content = resp.content
            merged_resp[k] = content
            sum_input += resp.input_tokens
            sum_output += resp.output_tokens

        return self.Response(
            content=merged_resp,
            source=source,
            target=targets,
            format_type=ret_format_type if format_type is None else format_type,
            input_tokens=sum_input,
            output_tokens=sum_output,
        )

    def _translate_single_target_single_content(self, contents: dict | str, source: str, target: str,
                                                config_args) -> Response | None:
        config_args = config_args.copy()
        format_type = config_args.get('format_type') if 'format_type' in config_args else None

        if isinstance(contents, str):
            return self._translate_sync_call(contents, source, target, config_args)

        if not isinstance(contents, dict):
            raise TranslateError(
                f"AI translation failed: expect content type is a dict or str, but get a [{type(contents)}]")

        if '_config_' in contents:
            config_args = merge_without_none(config_args, contents.pop('__config'))

        ret_content = {}
        sum_input = 0
        sum_output = 0
        merged_type = {}

        for k, v in contents.items():
            if not v:
                continue
            resp = self._translate_sync_call(v, source, target, config_args)
            ret_content[k] = resp.content
            sum_input += resp.input_tokens
            sum_output += resp.output_tokens
            if format_type is None:
                merged_type[k] = resp.format_type

        return self.Response(
            content=ret_content,
            source=source,
            target=target,
            format_type=merged_type if format_type is None else format_type,
            input_tokens=sum_input,
            output_tokens=sum_output,
        )

    def _translate_single_target_multi_content(self, contents: list, source: str, target: str,
                                               config_args) -> Response | None:
        format_type = config_args.get('format_type') if 'format_type' in config_args else None
        ret_content = []
        sum_input = 0
        sum_output = 0
        merged_type = []
        for content in contents:
            resp = self._translate_single_target_single_content(content, source, target, config_args)
            ret_content.append(resp.content)
            sum_input += resp.input_tokens
            sum_output += resp.output_tokens
            if format_type is None:
                merged_type.append(resp.format_type)

        return self.Response(
            content=ret_content,
            source=source,
            target=target,
            format_type=merged_type if format_type is None else format_type,
            input_tokens=sum_input,
            output_tokens=sum_output,
        )

    @classmethod
    def _prepare_plain_config(cls, config_args):
        """把配置值都转换成字符串（同时起到复制防修改的作用）"""
        return {k: v.name if isinstance(v, Enum) else str(v) for k, v in config_args.items() if v is not None}

    @classmethod
    def _decide_format_type(cls, config_args, content):
        if 'format_type' not in config_args:
            config_args['format_type'] = (
                FormatType.MARKDOWN.name if is_markdown(content) else FormatType.HTML.name
                if is_html(content) else FormatType.TEXT.name
            )

    @classmethod
    def _extract_content(cls, config_args, content):
        if isinstance(content, str):
            return content
        elif isinstance(content, dict):
            config_args['format_type'] = FormatType.JSON.name
            return json.dumps(content)
        elif isinstance(content, TransContent):
            return content.content
        else:
            raise TranslateError(
                f"AI translation failed: expect content type is a dict or str, but get a [{type(content)}]")

    def _translate_sync_call(self, content: str, source: str, target: str,
                             config_args: dict[str, int | str | Enum]) -> Response | None:
        try:
            config_args = self._prepare_plain_config(config_args)
            content = self._extract_content(config_args, content)
            self._decide_format_type(config_args, content)

            source = source.name if isinstance(source, Language) else str(source)
            target = target.name if isinstance(target, Language) else str(target)

            response = self.client.post('/', {
                'source': source,
                'target': target,
                'content': content,
                **config_args
            })
            if response['code'] != 0:
                current_app.logger.error(f"AI translation failed: {response}")
                raise TranslateError(f"AI translation failed: {response}")
            response['data'].pop('task_id',None)
            response['data'].pop('model_name',None)
            return self.Response(format_type=config_args['format_type'], **response['data'])
        except BaseHTTPClient.BadResponse as e:
            current_app.logger.error(f"AI translation failed: {e}")
            raise TranslateError(f"AI translation failed: {e}")

    @classmethod
    def _assert_resp_error(cls, resps):
        if isinstance(resps, list):
            error_count = 0
            for resp in resps:
                if resp.error_ is not None:
                    error_count += 1
            if error_count == len(resps):
                raise TranslateError(f"AI translation failed: {resps}")
        else:
            if resps.error_ is not None:
                raise TranslateError(f"AI translation failed: {resps}")

    @classmethod
    def _extract_business(cls, config_args, target, content):
        business_id = cls._decide_attr(config_args, target, content, 'business_id')
        business_info = cls._decide_attr(config_args, target, content, 'business_info')
        business = config_args['business']
        if business is None:
            raise TranslateError("AI translation failed: business is required")
        if business_info is None and business_id is None:
            raise TranslateError("AI translation failed: one of business_info and business_id is required")
        return business, business_id, business_info

    @classmethod
    def _decide_attr(cls, config_args, target, content, attr_name):
        attr_value = None

        if isinstance(content, dict):
            attr_value = content.get(attr_name)
        elif isinstance(content, TransContent):
            attr_value = getattr(content, attr_name)
        if attr_value:
            config_args[attr_name] = attr_value
            return attr_value

        if isinstance(target, dict):
            attr_value = target.get(attr_name)
        elif isinstance(target, TransTarget):
            attr_value = getattr(target, attr_name)
        if attr_value:
            config_args[attr_name] = attr_value
            return attr_value

        if attr_name in config_args:
            return config_args[attr_name]
        else:
            return None

    def get_async_result(self, task_id: str) -> Response | None:
        try:
            response = self.client.get(f'/result', task_id=task_id)
            if response['code'] != 0 or response['data'] is None or 'status' not in response['data']:
                current_app.logger.error(f"AI get translation task [task_id] failed: {response}")
                return None
            return self.Response(**response['data'])
        except BaseHTTPClient.BadResponse as e:
            current_app.logger.error(f"AI get translation task [task_id] failed: {e}")
            return None


class TranslateError(Exception):
    pass


def get_keyword_arguments(func, locals_, check_none=False):
    signature = inspect.signature(func)
    return {
        k: locals_[k] for k, v in signature.parameters.items()
        if v.kind == inspect.Parameter.KEYWORD_ONLY and k in locals_ and (not check_none or locals_[k] is not None)
    }


def merge_without_none(a: dict, b: dict) -> dict:
    # 合并字典，过滤掉值为 None 的键，键值相同的情况下，以 b 为准
    f_a = {k: v for k, v in a.items() if v is not None}
    f_b = {k: v for k, v in b.items() if v is not None}
    return f_a | f_b


def is_html(text):
    """
    判断给定文本是否为 HTML。

    参数:
    text (str): 要检查的文本。

    返回:
    bool: 如果文本看起来是 HTML，则返回 True；否则返回 False。
    """
    # 正则表达式用于匹配 HTML 标签
    html_pattern = re.compile(r'<[^>]+>')

    # 如果在文本中找到匹配的 HTML 标签，则认为是 HTML
    return bool(html_pattern.search(text))


def is_markdown(text):
    """
    判断给定文本是否为 Markdown。

    参数:
    text (str): 要检查的文本。

    返回:
    bool: 如果文本看起来是 Markdown，则返回 True；否则返回 False。
    """
    # 常见的 Markdown 语法正则表达式
    markdown_patterns = [
        r'^\#{1,6}\s',  # 标题 (e.g., # Header, ## Header)
        # r'^\s*\*\s',  # 无序列表 (e.g., * item)
        # r'^\s*\d+\.\s',  # 有序列表 (e.g., 1. item)
        r'\[. *?\]\(.* ?\)',  # 链接 (e.g., [text](url))
        r'\!\[. *?\]\(.* ?\)',  # 图片 (e.g., ![alt text](image url))
        r'\*\*.*?\*\*',  # 粗体 (e.g., **bold**)
        # r'\*.*?\*',  # 斜体 (e.g., *italic*)
        # r'`{1,3}.*?`{1,3}',  # 行内代码 (e.g., `code`)
        r'^```[\s\S]*?^```',  # 代码块 (e.g., ``` code ```)
        r'^---$',  # 分隔线 (e.g., ---)
    ]

    # 组合所有正则表达式
    combined_pattern = re.compile('|'.join(markdown_patterns), re.MULTILINE)

    # 如果文本中找到匹配的 Markdown 语法，则认为是 Markdown
    return bool(combined_pattern.search(text))
