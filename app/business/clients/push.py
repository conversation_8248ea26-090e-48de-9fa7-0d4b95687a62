from enum import Enum

from app import config
from app.utils.http_client import RESTClient


class PushClient:

    class PushType(Enum):
        Interaction = "interaction"
        Ban = "ban"
        Warning = "warning"

    def __init__(self):
        conf = config['BACKEND_INTERNAL_CONFIG']
        self.client: RESTClient = RESTClient(conf['url'])

    def send(
        self,
        user_id: int,
        push_type: PushType,
        web_push_params: dict = None,
        message_params: dict = None,
        email_params: dict = None
    ):
        data = dict(user_id=user_id, business="COMMENT", type=push_type.value)
        if web_push_params:
            data['web_push_params'] = web_push_params
        if message_params:
            data['message_params'] = message_params
        if email_params:
            data['email_params'] = email_params
        return self.client.post('push', data)