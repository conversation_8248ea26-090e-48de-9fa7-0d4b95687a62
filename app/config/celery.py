# -*- coding: utf-8 -*-

from enum import Enum
from collections import defaultdict
from typing import Dict, List
from pygtrie import StringTrie


imports = (
    'app.schedules',
)

task_default_queue = 'default'


class Router:

    DEFAULT_QUEUE = task_default_queue

    def __init__(self):
        self._trie = StringTrie(separator='.')

    def set_queue_for_prefix(self, prefix: str, queue: str):
        self._trie[prefix] = queue

    # noinspection PyUnusedLocal
    def route_for_task(self, task: str, args: tuple = None, kwargs: dict = None
                       ) -> str:
        return (kwargs and kwargs.pop('_queue', None)
                or self._trie.longest_prefix(task).value
                or self.DEFAULT_QUEUE)

    def routes(self) -> Dict[str, List[str]]:
        queues = defaultdict(list)
        for prefix, queue in self._trie.items():
            if isinstance(queue, Enum):
                queue = queue.value
            queues[queue].append(prefix)
        return queues
    
    def task_routes(self) -> Dict[str, List[str]]:
        from app import celery
        queues = defaultdict(list)
        for task in celery.tasks:
            queue = self.route_for_task(task)
            if isinstance(queue, Enum):
                queue = queue.value
            queues[queue].append(task)
        return queues


router = Router()

task_routes = router,
celery_queues = {
    'default': {
        'exchange': 'default',
        'exchange_type': 'direct',
        'binding_key': 'default'
    }
}
beat_schedule = {}
