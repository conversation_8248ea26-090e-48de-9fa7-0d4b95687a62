# -*- coding: utf-8 -*-

import os
from typing import Dict, Any


config: Dict[str, Any] = {}


def _load_py_file(filename: str):
    file_path = os.path.join(os.path.dirname(__file__), filename)
    if not os.path.exists(file_path):
        return {}

    mod_name = os.path.splitext(filename)[0]
    mod = __import__(mod_name, globals(), level=1)
    return {k: v for k, v in mod.__dict__.items() if not k.startswith('_') and k.isupper()}


def _load_env_file(filename: str):
    c = _load_py_file(filename)
    for k, v in c.items():
        if k.startswith('ENV_'):
            os.environ[k] = str(v)


def _load_config(filename: str):
    c = _load_py_file(filename)
    for k, v in c.items():
        config[k] = v


def _patch_config():
    postgresql_conf = config['POSTGRESQL']
    username = postgresql_conf['username']
    password = postgresql_conf['password']
    host = postgresql_conf['host']
    db_name = postgresql_conf['db']

    # noinspection SpellCheckingInspection
    config['SQLALCHEMY_DATABASE_URI'] = (
        f'postgresql+psycopg2://'
        f'{username}:{password}@{host}/{db_name}?')
    config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False


_load_env_file('env.py')
_load_config('default.py')
_load_config('testing_release.py')
_load_config('environment.py')
_patch_config()
