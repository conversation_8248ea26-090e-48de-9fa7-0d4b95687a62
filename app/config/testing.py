# -*- coding: utf-8 -*-
import os as _os

_ENV_NUM = int(_os.getenv('ENV_NUM', 1))

_REDIS_DBS = [7,8,9]
_CELERY_REDIS_DBS = [10,11,12]

_REDIS_DB = _REDIS_DBS[_ENV_NUM-1]
_CELERY_REDIS_DB = _CELERY_REDIS_DBS[_ENV_NUM-1]

# postgresql
POSTGRESQL = {
    "host": "pgsql-coinex-comment.cluster-cm6pyfonwyec.ap-east-1.rds.amazonaws.com",
    "username": "comment_user",
    "password": "pSGsvGPWfv4Z!56q",
    "db": f"coinex_comment_{_ENV_NUM}"
}

# redis
REDIS = {
    'host': 'uat-coinex.nqdbud.0001.ape1.cache.amazonaws.com',
    'port': 6379,
    'db': _R<PERSON><PERSON>_DB,
    'password': '',
}

# celery
CELERY_BROKER_URL = f'redis://@uat-coinex.nqdbud.0001.ape1.cache.amazonaws.com:6379/{_CELERY_REDIS_DB}'

# graylog
GRAYLOG_LOGGER_HOST = "127.0.0.1"
GRAYLOG_LOGGER_PORT = 8888

SLACK_MSG_URL = 'https://slack.com/api/chat.postMessage'

# slack token
SLACK_TOKEN = ""

GOOGLE_TRANSLATE_KEY = ""

_TRANSLATE_SERVERS = ['***********', '***********', '***********']

_TRANSLATE_SERVER_IP = _TRANSLATE_SERVERS[_ENV_NUM - 1]

AI_TRANSLATION_CONFIG = {
    'url': f'http://{_TRANSLATE_SERVER_IP}:12311/v1/translate',
}

BACKEND_INTERNAL_CONFIG = {
    "url": 'http://**********:8001/internal/exchange/',
}

AI_MODERATION_CONFIG = {'url': f'http://{_TRANSLATE_SERVER_IP}:12311/v1/moderation'}

ADMIN_CONTACTS = {
    "moderation_timeout_notice": ""
}

WEB_MAX_PAGE = 1000
WEB_MAX_LIMIT = 1000
