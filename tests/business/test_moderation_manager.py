import pytest
from flask import g
from app.models.comment import Comment
from app.common.constants import Language
from tests.common.t_common import default_lang
from tests.common.mock_redis import patch_redis
from tests.common.mock_sqlalchemy import patch_sqlalchemy

DEFAULT_USER_ID = 2


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.user_id = DEFAULT_USER_ID
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestAIModerationIntegration:
    @staticmethod
    def _create_test_comment(content, lang=Language.ZH_HANS_CN):
        """创建测试评论的辅助方法"""
        from app.models import db
        from app.business.comment_manager import CommentManager

        comment = CommentManager.create_comment(
            business=Comment.Business.COIN,
            business_id="1",
            business_code="BTC",
            content=content,
            lang=lang,
            user_id=DEFAULT_USER_ID
        )
        db.session.commit()
        return comment

    def _verify_moderation_result(self, comment_id, expected_status):
        """验证审核结果"""
        from app.models import db
        from app.models.comment import Comment
        from app.models.moderation import CommentModeration

        # 获取评论和审核记录
        comment = db.session.get(Comment, comment_id)
        assert comment is not None
        moderation = CommentModeration.query.filter_by(comment_id=comment_id).first()
        assert moderation is not None

        # 验证评论状态
        assert comment.status == expected_status
        return moderation

    def test_moderation_flow(self, tcontext):
        """测试完整的审核流程，包括正常和敏感内容"""
        from app.schedules.comment_task import initial_moderate
        from app.models.comment import Comment
        from app.models.moderation import CommentModeration

        with tcontext:
            # 测试正常评论
            normal_comment = self._create_test_comment("这是一个正常的评论内容，讨论比特币价格走势")
            assert normal_comment.status == Comment.CommentStatus.CREATED

            # 执行初审
            moderation = initial_moderate(normal_comment.id)
            assert moderation.status == CommentModeration.Status.APPROVED
            self._verify_moderation_result(normal_comment.id, Comment.CommentStatus.PUBLISHED)

            # 测试敏感内容
            sensitive_comment = self._create_test_comment("这是一个包含敏感词的评论 NSFW")
            assert sensitive_comment.status == Comment.CommentStatus.CREATED

            # 执行初审
            moderation = initial_moderate(sensitive_comment.id)
            assert moderation.status == CommentModeration.Status.REJECTED
            self._verify_moderation_result(sensitive_comment.id, Comment.CommentStatus.DISABLED)

    def test_different_languages(self, tcontext):
        """测试不同语言内容的审核"""
        from app.schedules.comment_task import initial_moderate
        from app.models.moderation import CommentModeration

        test_contents = [
            ("This is a normal English comment about Bitcoin", Language.EN_US),
            ("これは普通の日本語のコメントです", Language.JA_JP),
            ("这是一个正常的中文评论", Language.ZH_HANS_CN)
        ]

        with tcontext:
            for content, lang in test_contents:
                comment = self._create_test_comment(content, lang)
                # 验证初始状态
                assert comment.status == Comment.CommentStatus.CREATED

                # 执行初审
                moderation = initial_moderate(comment.id)
                assert moderation.status == CommentModeration.Status.APPROVED
                self._verify_moderation_result(comment.id, Comment.CommentStatus.PUBLISHED)

    def test_edge_cases(self, tcontext):
        """测试边界情况"""
        from app.schedules.comment_task import initial_moderate

        test_cases = [
            "   ",  # 空白内容
            "!@#$%^&*()",  # 特殊字符
            "a" * 1000,  # 超长内容
            "👍 🚀 💰",  # emoji
            "http://example.com",  # URL
            "<script>alert('test')</script>",  # 代码注入
        ]

        with tcontext:
            for content in test_cases:
                comment = self._create_test_comment(content)
                # 验证初始状态
                assert comment.status == Comment.CommentStatus.CREATED

                # 执行初审
                moderation = initial_moderate(comment.id)
                # 验证最终状态（根据实际业务逻辑可能是 PUBLISHED 或 DISABLED）
                self._verify_moderation_result(comment.id, comment.status)

    def test_mixed_content(self, tcontext):
        """测试混合内容的审核"""
        from app.schedules.comment_task import initial_moderate

        with tcontext:
            mixed_content = """
            比特币分析：
            1. 价格趋势看好 📈
            2. 技术面分析：支撑位在 $30,000
            3. 风险提示：市场波动较大

            详情请访问: https://example.com
            """
            comment = self._create_test_comment(mixed_content)
            # 验证初始状态
            assert comment.status == Comment.CommentStatus.CREATED

            # 执行初审
            moderation = initial_moderate(comment.id)
            # 验证最终状态
            self._verify_moderation_result(comment.id, comment.status)


    def test_discrimination_content(self, tcontext):
        """测试歧视性内容的审核"""
        from app.schedules.comment_task import initial_moderate
        from app.models.comment import Comment
        from app.models.moderation import CommentModeration

        discrimination_cases = [
            # 种族歧视
            "这些黑人就是懒惰，根本不适合做这份工作",
            "亚洲人都是数学好的书呆子",

            # 性别歧视
            "女人就该待在家里照顾孩子，不要出来工作",
            "男人要是哭就是娘娘腔，没出息",

            # 宗教歧视
            "信这个教的人都是恐怖分子",
            "某教派的人就是愚昧落后，应该被驱逐",

            # 地域歧视
            "北方人都是粗鲁没素质的",
            "南方人说话太软弱，一点男子气概都没有",

            # 隐晦的歧视
            "我不是歧视，但是他们这些人就是比较懒",
            "这个职位需要一个年轻有活力的面孔",

            # 多重歧视
            "女性穆斯林根本不该出现在公共场合",
        ]

        control_cases = [
            "我觉得这个方案需要改进",
            "不同文化背景的人在工作中可能会有不同的处事方式",
        ]

        with tcontext:
            # 测试歧视性内容
            for content in discrimination_cases:
                comment = self._create_test_comment(content)
                assert comment.status == Comment.CommentStatus.CREATED

                # 执行初审
                moderation = initial_moderate(comment.id)
                print(f'content: {content},reject_type: {moderation.rejected_type}')
                assert moderation.status == CommentModeration.Status.REJECTED
                self._verify_moderation_result(comment.id, Comment.CommentStatus.DISABLED)

            # 测试对照组（正常内容）
            for content in control_cases:
                comment = self._create_test_comment(content)
                assert comment.status == Comment.CommentStatus.CREATED

                # 执行初审
                moderation = initial_moderate(comment.id)
                print(f'content: {content},status: {moderation.status}')
                assert moderation.status == CommentModeration.Status.APPROVED
                self._verify_moderation_result(comment.id, Comment.CommentStatus.PUBLISHED)

    def test_multilingual_discrimination(self, tcontext):
        """测试多语言歧视内容的审核"""
        from app.schedules.comment_task import initial_moderate
        from app.models.comment import Comment
        from app.models.moderation import CommentModeration

        test_cases = [
            # 英文歧视内容
            (Language.EN_US, "Women should stay in the kitchen and not work"),
            # 日文歧视内容
            (Language.JA_JP, "外国人は日本の文化を理解できない"),
            # 中文歧视内容
            (Language.ZH_HANS_CN, "南方人说话太软弱"),
        ]

        with tcontext:
            for lang, content in test_cases:
                comment = self._create_test_comment(content, lang)
                assert comment.status == Comment.CommentStatus.CREATED

                # 执行初审
                moderation = initial_moderate(comment.id)
                print(f'content: {content},reject_type: {moderation.rejected_type}')
                assert moderation.status == CommentModeration.Status.REJECTED
                self._verify_moderation_result(comment.id, Comment.CommentStatus.DISABLED)

    def test_detailed_moderation(self, tcontext):
        """测试详细内容审核功能"""
        from app.schedules.comment_task import initial_moderate
        from app.models.comment import Comment
        from app.models.moderation import CommentModeration

        test_cases = [
            # 正常内容
            {
                "content": "这是一个正常的评论，讨论比特币的投资价值",
                "expected": {
                    "passed": True,
                    "status": CommentModeration.Status.APPROVED,
                    "comment_status": Comment.CommentStatus.PUBLISHED,
                    "reject_type": None,
                    "details_check": {
                        "has_details": False,
                        "violations_count": 0
                    }
                }
            },
            # 单一违规类型 - 歧视性内容
            {
                "content": "南方人就是懒惰，根本不适合做这行",
                "expected": {
                    "passed": False,
                    "status": CommentModeration.Status.REJECTED,
                    "comment_status": Comment.CommentStatus.DISABLED,
                    "reject_type": CommentModeration.RejectType.DISCRIMINATION,
                    "details_check": {
                        "has_details": True,
                        "violations_count": 1,
                        "violation_types": ["DISCRIMINATION"]
                    }
                }
            },
            # 多重违规内容
            {
                "content": "这些XX真是垃圾，滚出去！另外推荐大家买XX药品，包治百病",
                "expected": {
                    "passed": False,
                    "status": CommentModeration.Status.REJECTED,
                    "comment_status": Comment.CommentStatus.DISABLED,
                    "reject_type": CommentModeration.RejectType.ABUSE,  # 主要违规类型
                    "details_check": {
                        "has_details": True,
                        "violations_count": 2,
                        "violation_types": ["ABUSE", "FRAUD"]
                    }
                }
            }
        ]

        with tcontext:
            for case in test_cases:
                # 创建评论
                comment = self._create_test_comment(case["content"])
                assert comment.status == Comment.CommentStatus.CREATED

                # 执行详细审核
                moderation = initial_moderate(comment.id, if_detail=True)

                # 验证基本审核结果
                expected = case["expected"]
                print(f'Content: {case["content"]}')
                print(f'Moderation result: {moderation.moderation_detail}')

                # 验证基本字段
                assert moderation.status == expected["status"]
                assert moderation.rejected_type == expected["reject_type"]

                # 验证详细信息结构
                details_check = expected["details_check"]
                assert bool(moderation.moderation_detail) == details_check["has_details"]

                if moderation.moderation_detail:
                    assert len(moderation.moderation_detail) == 3
                    details = moderation.moderation_detail

                    # 验证违规数量
                    violations_count = len(details.get("all_violations", []))
                    assert violations_count == details_check["violations_count"]

                    if not expected["passed"]:
                        # 验证违规类型
                        if "violation_types" in details_check:
                            assert set(details["all_violations"]) == set(details_check["violation_types"])

                        # 验证违规文本和原因的数量匹配
                        assert len(details["violation_text"]) == len(details["violation_reasons"])
                        assert len(details["violation_text"]) == violations_count

                        # 验证reason字段存在且非空
                        assert moderation.reason
                        assert isinstance(moderation.reason, str)
                        # 验证reason包含所有违规文本
                        for text in details["violation_text"]:
                            assert text in moderation.reason

                # 验证评论最终状态
                self._verify_moderation_result(comment.id, expected["comment_status"])