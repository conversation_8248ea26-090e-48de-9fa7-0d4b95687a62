import pytest
from flask import g
from app.common.constants import Language
from app.models.comment import Comment
from tests.common.t_common import default_lang
from tests.common.mock_redis import patch_redis
from tests.common.mock_sqlalchemy import patch_sqlalchemy
import threading

DEFAULT_USER_ID = 2


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.user_id = DEFAULT_USER_ID
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
# @pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestEmptyCollectionMixin:
    """测试空集合缓存穿透防护 Mixin"""

    def test_comment_list_cache_empty_protection(self, tcontext):
        """测试评论列表缓存的空集合防穿透"""
        from app.cache.comment_cache import CommentListCache
        from app.business.lock import load_if_not_exists

        with tcontext:
            # 创建一个肯定不存在评论的缓存实例
            cache = CommentListCache(
                business=Comment.Business.COIN,
                business_id="999999",  # 不存在的业务ID
                lang=Language.EN_US,
                sort_type=Comment.SortType.NEW
            )
            
            # 清理可能存在的缓存和标记
            cache.delete()
            
            # 验证初始状态
            assert not cache.exists()
            assert not cache._is_marked_as_empty()
            
            # 第一次加载 - 应该触发 reload 并创建空集合标记
            load_if_not_exists(cache)
            
            # 验证空集合标记被创建
            assert cache._is_marked_as_empty()
            
            # 第一次读取 - 应该返回空列表
            data1 = cache.read()
            assert data1 == []
            assert isinstance(data1, list)
            
            # 第二次读取 - 应该直接从空标记返回，不触发数据库查询
            data2 = cache.read()
            assert data2 == []
            assert data1 is not data2  # 每次返回新的空列表实例
            
            # 测试分页方法
            page_data = cache.paginate(limit=10)
            assert page_data == []
            
            # 清理
            cache.delete()

    def test_user_pending_comments_cache_empty_protection(self, tcontext):
        """测试用户待审核评论缓存的空集合防穿透"""
        from app.cache.comment_cache import UserPendingCommentsCache
        from app.business.lock import load_if_not_exists

        with tcontext:
            # 创建一个肯定不存在评论的用户缓存
            cache = UserPendingCommentsCache(user_id=999999)  # 不存在的用户
            
            # 清理可能存在的缓存
            cache.delete()
            
            # 验证初始状态
            assert not cache.exists()
            assert not cache._is_marked_as_empty()
            
            # 第一次加载
            load_if_not_exists(cache)
            
            # 验证空集合标记被创建
            assert cache._is_marked_as_empty()
            
            # 第一次读取
            data1 = cache.read()
            assert data1 == set()
            assert isinstance(data1, set)
            
            # 第二次读取
            data2 = cache.read()
            assert data2 == set()
            
            # 测试查询方法
            comments = cache.query_comments(
                business=Comment.Business.COIN.name,
                business_id="test",
                lang=Language.EN_US
            )
            assert comments == []
            
            # 清理
            cache.delete()

    def test_mixin_with_real_data(self, tcontext):
        """测试 Mixin 在有真实数据时的行为"""
        from app.cache.comment_cache import CommentListCache

        with tcontext:
            # 使用存在评论的业务ID
            cache = CommentListCache(
                business=Comment.Business.COIN,
                business_id="4",  # 从现有测试中看，这个ID有数据
                lang=Language.EN_US,
                sort_type=Comment.SortType.NEW
            )
            
            # 清理缓存
            cache.delete()
            
            # 加载真实数据
            cache.reload()
            
            # 验证有数据时不会有空标记
            assert cache.exists()
            assert not cache._is_marked_as_empty()
            
            # 读取数据
            data = cache.read()
            assert len(data) > 0
            
            # 清理
            cache.delete()

    def test_save_operation_clears_empty_marker(self, tcontext):
        """测试保存操作会清除空标记"""
        from app.cache.comment_cache import CommentListCache

        with tcontext:
            cache = CommentListCache(
                business=Comment.Business.COIN,
                business_id="test_save",
                lang=Language.EN_US,
                sort_type=Comment.SortType.NEW
            )
            
            # 清理缓存
            cache.delete()
            
            # 手动创建空标记
            cache._mark_as_empty()
            assert cache._is_marked_as_empty()
            
            # 保存非空数据
            test_data = {"comment_1": 1.0, "comment_2": 2.0}
            cache.save(test_data)
            
            # 验证空标记被清除
            assert not cache._is_marked_as_empty()
            assert cache.exists()
            
            # 清理
            cache.delete()

    def test_delete_operation_clears_empty_marker(self, tcontext):
        """测试删除操作会清除空标记"""
        from app.cache.comment_cache import CommentListCache

        with tcontext:
            cache = CommentListCache(
                business=Comment.Business.COIN,
                business_id="test_delete",
                lang=Language.EN_US,
                sort_type=Comment.SortType.NEW
            )
            
            # 创建空标记
            cache._mark_as_empty()
            assert cache._is_marked_as_empty()
            
            # 删除缓存
            cache.delete()
            
            # 验证空标记也被清除
            assert not cache._is_marked_as_empty()

    def test_expire_operation_syncs_marker(self, tcontext):
        """测试过期操作会同步标记的TTL"""
        from app.cache.comment_cache import CommentListCache

        with tcontext:
            cache = CommentListCache(
                business=Comment.Business.COIN,
                business_id="test_expire",
                lang=Language.EN_US,
                sort_type=Comment.SortType.NEW
            )
            
            # 创建空标记
            cache._mark_as_empty()
            assert cache._is_marked_as_empty()
            
            # 设置过期时间
            expire_seconds = 300  # 5分钟
            cache.expire(expire_seconds)

            # 验证标记仍然存在（TTL被同步更新）
            assert cache._is_marked_as_empty()
            
            # 检查原缓存和marker缓存的TTL是否同步
            # 获取两个缓存的TTL值
            marker_ttl = cache.redis.ttl(cache._empty_marker_key)
            
            # 验证两个TTL值都接近设置的expire_seconds（允许1-2秒误差）
            assert abs(marker_ttl - expire_seconds) <= 2, f"标记缓存TTL({marker_ttl})与设置值({expire_seconds})差异过大"

            # 测试边界情况：设置很小的TTL值
            small_expire = 5  # 5秒
            cache.expire(small_expire)
            
            marker_ttl_small = cache.redis.ttl(cache._empty_marker_key)
            
            assert abs(marker_ttl_small - small_expire) <= 1, f"小TTL标记缓存({marker_ttl_small})与设置值({small_expire})差异过大"

            # 清理
            cache.delete()


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestEmptyCollectionMixinConcurrency:
    """测试空集合 Mixin 的并发安全性"""

    def test_concurrent_empty_cache_access(self, tcontext):
        """测试并发访问空缓存的安全性"""
        from app.cache.comment_cache import CommentListCache
        from app.business.lock import load_if_not_exists
        from flask import _app_ctx_stack

        with tcontext:
            cache = CommentListCache(
                business=Comment.Business.COIN,
                business_id="concurrent_test",
                lang=Language.EN_US,
                sort_type=Comment.SortType.NEW
            )
            
            # 清理缓存
            cache.delete()
            
            results = []
            errors = []
            
            # 获取当前应用上下文，供线程使用
            app = _app_ctx_stack.top.app
            
            def worker():
                try:
                    with app.app_context():
                        # 先尝试加载
                        load_if_not_exists(cache)
                        # 然后读取
                        data = cache.read()
                        results.append(data)
                except Exception as e:
                    errors.append(e)
                    print(f"线程异常: {e}")

            # 启动多个线程并发访问
            threads = [threading.Thread(target=worker) for _ in range(5)]
            for t in threads:
                t.start()
            for t in threads:
                t.join()

            # 验证结果
            assert len(errors) < 5, f"并发访问出现错误: {errors}"
            assert len(results) == 5 - len(errors)
            assert all(result == [] for result in results)
            
            # 验证最终状态
            assert cache._is_marked_as_empty()
            
            # 清理
            cache.delete()

    def test_mro_method_resolution(self, tcontext):
        """测试方法解析顺序 (MRO)"""
        from app.cache.comment_cache import CommentListCache, UserPendingCommentsCache

        with tcontext:
            # 测试 CommentListCache 的 MRO
            expected_mro = [
                'CommentListCache',
                'EmptyCollectionMixin', 
                'SortedSetCache',
                '_BaseCache',
                'object'
            ]
            
            actual_mro = [cls.__name__ for cls in CommentListCache.__mro__]
            assert actual_mro == expected_mro
            
            # 测试 UserPendingCommentsCache 的 MRO
            expected_mro_user = [
                'UserPendingCommentsCache',
                'EmptyCollectionMixin',
                'SetCache', 
                '_BaseCache',
                'object'
            ]
            
            actual_mro_user = [cls.__name__ for cls in UserPendingCommentsCache.__mro__]
            assert actual_mro_user == expected_mro_user


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestEmptyCollectionMixinExists:

    def test_exists_marker_behavior(self):
        from app.cache.base import EmptyCollectionMixin, SetCache

        class DummySetCache(EmptyCollectionMixin, SetCache):
            pass

        cache = DummySetCache(pk="test_exists")

        # 初始状态，真实缓存和 marker 都不存在
        cache.delete()
        assert not cache.exists()

        # 通过 save 空集合，自动创建 marker
        cache.save(set())
        assert cache.exists()
        assert cache._is_marked_as_empty()

        # 保存非空集合，marker 应被清除
        cache.save({"a", "b"})
        assert cache.exists()
        assert not cache._is_marked_as_empty()

        # 再次保存空集合，marker 应再次出现
        cache.save(set())
        assert cache.exists()
        assert cache._is_marked_as_empty()

        # 清理
        cache.delete()
        assert not cache.exists()

    def test_load_if_not_exists_with_marker(self, monkeypatch):
        from app.cache.base import EmptyCollectionMixin, SetCache
        from app.business.lock import load_if_not_exists

        class DummyLoadCache(EmptyCollectionMixin, SetCache):
            def reload(self):
                # reload 时保存空集合
                self.save(set())

        cache = DummyLoadCache(pk="test_load_if_not_exists")
        cache.delete()

        # 先通过 save 空集合，创建 marker
        cache.save(set())
        assert cache._is_marked_as_empty()

        # mock reload，统计调用次数
        called = {}

        def fake_reload():
            called['count'] = called.get('count', 0) + 1
            cache.save(set())

        monkeypatch.setattr(cache, 'reload', fake_reload)

        # marker 存在时，load_if_not_exists 不应 reload
        result = load_if_not_exists(cache)
        assert result is False
        assert called.get('count', 0) == 0

        # 删除 marker，load_if_not_exists 应该 reload
        cache.delete()
        result = load_if_not_exists(cache)
        assert result is True
        assert called.get('count', 0) == 1

        # 清理
        cache.delete()


