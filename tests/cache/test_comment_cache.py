import datetime
import pytest
from flask import g
from app.common.constants import Language
from tests.common.t_common import default_lang
from tests.common.mock_redis import patch_redis
from tests.common.mock_sqlalchemy import patch_sqlalchemy
import threading
import time
from unittest import mock

DEFAULT_USER_ID = 2


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.user_id = DEFAULT_USER_ID
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentListCache:

    def test_reload(self, tcontext):
        """测试完整的审核流程，包括正常和敏感内容"""
        from app.cache.comment_cache import CommentListCache
        from app.models.comment import Comment

        with tcontext:
            # 测试正常评论
            cache = CommentListCache(Comment.Business.COIN, '4', Language.EN_US)
            cache.reload()
            comment_list = cache.paginate()
            assert len(comment_list) > 0

    def test_get_max_score_comment_id(self, tcontext):
        """测试完整的审核流程，包括正常和敏感内容"""
        from app.cache.comment_cache import CommentListCache
        from app.models.comment import Comment

        with tcontext:
            # 测试正常评论
            cache = CommentListCache(Comment.Business.COIN, '159', Language.EN_US, Comment.SortType.HOT)
            assert cache.get_max_score_comment_id() > 0

    def test_load_if_not_exists(self, tcontext):
        """测试 load_if_not_exists"""
        from app.cache.comment_cache import CommentListCache
        from app.models.comment import Comment
        from app.business.lock import load_if_not_exists

        with tcontext:
            # 测试正常评论
            cache = CommentListCache(Comment.Business.COIN, '161', Language.EN_US)
            cache.delete()
            assert not cache.exists()

            load_if_not_exists(cache)
            assert cache.exists()

            assert load_if_not_exists(cache) is False


@pytest.mark.usefixtures('module_setup')
# @pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentCacheConcurrency:
    def setup_method(self, tcontext):
        # 清理缓存，保证每个测试用例独立
        self.comment_id = None

    def _prepare(self, tcontext):
        from app.cache.comment_cache import CommentCache
        from app.models.comment import Comment

        with tcontext:
            g.user_id = 9999
            g.lang = Language.EN_US
            # 构造一条评论对象并写入数据库
            comment = Comment.query.first()
            assert comment is not None
            self.comment_id = comment.id

            # --- 1. 先写入缓存，测试并发下都直接返回缓存内容 ---
            self.cache = CommentCache(self.comment_id)
            self.cache.save_comment(comment)

    def test_load_if_not_exists_when_cache_exists(self, tcontext):
        """
        并发测试 CommentCache.load_if_not_exists：
        1. 缓存已存在时，所有并发调用都直接返回缓存内容。
        2. 缓存不存在时，只有第一个获得锁的调用 reload，其他等待后直接返回缓存内容。
        """
        self._prepare(tcontext)

        from app.business.lock import load_if_not_exists
        with tcontext:
            assert self.cache.exists()
            results = []
            errors = []
            def worker():
                try:
                    res = load_if_not_exists(self.cache)
                    results.append(res)
                except Exception as e:
                    print(f'Error in thread: {e}')
                    errors.append(e)

            threads = [threading.Thread(target=worker) for _ in range(5)]
            for t in threads:
                t.start()
            for t in threads:
                t.join()
            # 所有线程都应返回 False（未触发 reload）
            assert all(r is False for r in results)
            assert not errors

    def test_load_if_not_exists_when_cache_missing_concurrent(self, tcontext):
        """
        并发测试 CommentCache.load_if_not_exists：
        1. 缓存已存在时，所有并发调用都直接返回缓存内容。
        2. 缓存不存在时，只有第一个获得锁的调用 reload，其他等待后直接返回缓存内容。
        """
        self._prepare(tcontext)

        from app.business.lock import load_if_not_exists
        from flask import _app_ctx_stack

        with tcontext:
            # --- 2. 清空缓存，测试并发下只有一个 reload ---
            self.cache.delete()
            assert not self.cache.exists()

            # reload_count = 0
            # orig_reload = self.cache.reload
            # lock = threading.Lock()
            # def reload_hook():
            #     nonlocal reload_count
            #     with lock:
            #         reload_count += 1
            #     # 模拟 reload 过程耗时，放大并发冲突概率
            #     time.sleep(0.2)
            #     return orig_reload()

            results = []
            timeouts = []
            errors = []  # 用于记录线程异常

            # 获取当前应用上下文，供线程使用
            app = _app_ctx_stack.top.app
            def worker2():
                try:
                    with app.app_context():
                        res = load_if_not_exists(self.cache)
                        results.append(res)
                except TimeoutError as timexcept:
                    timeouts.append(timexcept)
                    print(f"锁超时: {timexcept}")
                except Exception as e:
                    errors.append(e)
                    print(f"线程异常: {e}")

            threads = [threading.Thread(target=worker2) for _ in range(5)]
            for t in threads:
                t.start()
            for t in threads:
                t.join()

            # 只有一个线程会 reload，其他线程等待后直接返回缓存内容
            # 只有一个 True（reload），其余为 False
            print("-----ERRORS-----")
            print(errors)
            print("-----RESULTS-----")
            print(results)
            assert results.count(True) == 1
            assert results.count(False) + len(timeouts)== 4

    def test_cache_load(self, tcontext):
        """测试缓存加载"""
        from app.cache.comment_cache import CommentCache
        from app.models.comment import Comment

        with tcontext:
            # 测试正常评论
            comment = CommentCache(8657).get_comment(aside=True)
            assert comment is not None
            assert comment.status == Comment.CommentStatus.PUBLISHED
