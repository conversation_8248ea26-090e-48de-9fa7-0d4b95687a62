import pytest
from flask import g

from app.common.constants import Language
from app.utils.text import detect_lang


@pytest.fixture(scope='class')
def class_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.lang = 'zh_Hant_HK'
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('class_setup')
class TestTextUtils:
    def test_detect_lang_en(self, tcontext):
        lang = detect_lang('hello world')
        assert lang == Language.EN_US

    def test_detect_lang_zh(self, tcontext):
        lang = detect_lang('你好，世界')
        assert lang == Language.ZH_HANS_CN

    def test_detect_lang_hk(self, tcontext):
        lang = detect_lang('龍馬精神')
        assert lang == Language.ZH_HANT_HK

    def test_detect_lang_ja(self, tcontext):
        lang = detect_lang('こんにちは, 世界, abc')
        assert lang == Language.JA_JP

    def test_detect_lang_zh_hans_cn_common_words(self, tcontext):
        # 测试常用简体中文词汇
        lang = detect_lang('今天天气真好')
        assert lang == Language.ZH_HANS_CN

    def test_detect_lang_zh_hans_cn_numbers(self, tcontext):
        # 测试带数字的简体中文
        lang = detect_lang('我有一百二十三个苹果')
        assert lang == Language.ZH_HANS_CN

    def test_detect_lang_zh_hans_cn_special_chars(self, tcontext):
        # 测试带特殊字符的简体中文
        lang = detect_lang('【通知】请大家注意：明天放假！')
        assert lang == Language.ZH_HANS_CN

    def test_detect_lang_zh_hant_common_words(self, tcontext):
        # 测试常用繁体中文词汇
        lang = detect_lang('今天風和日麗')
        assert lang == Language.ZH_HANT_HK

    def test_detect_lang_zh_hant_numbers(self, tcontext):
        # 测试带数字的繁体中文
        lang = detect_lang('我有壹佰貳拾叁個蘋果')
        assert lang == Language.ZH_HANT_HK

    def test_detect_lang_zh_hant_special_chars(self, tcontext):
        # 测试带特殊字符的繁体中文
        lang = detect_lang('【通知】請大家註意：明天放假！')
        assert lang == Language.ZH_HANT_HK

    def test_detect_lang_zh_mixed(self, tcontext):
        # 测试混合简繁体的文本，应该根据占比判断
        lang = detect_lang('你好啊，今天風和日麗，天氣真好')
        assert lang == Language.ZH_HANT_HK

    def test_detect_lang_ru(self, tcontext):
        # 测试俄语
        lang = detect_lang('Привет, мир!')
        assert lang == Language.RU_KZ

    def test_detect_lang_ko(self, tcontext):
        # 测试韩语
        lang = detect_lang('안녕하세요, 세계!')
        assert lang == Language.KO_KP

    def test_detect_lang_id(self, tcontext):
        # 测试印尼语 - 使用更具特征的印尼语句子
        lang = detect_lang('Selamat pagi, apa kabar? Saya senang bertemu dengan Anda')
        assert lang == Language.ID_ID

    def test_detect_lang_es(self, tcontext):
        # 测试西班牙语
        lang = detect_lang('¡Hola Mundo!')
        assert lang == Language.ES_ES

    def test_detect_lang_fa(self, tcontext):
        # 测试波斯语
        lang = detect_lang('سلام دنیا!')
        assert lang == Language.FA_IR

    def test_detect_lang_tr(self, tcontext):
        # 测试土耳其语
        lang = detect_lang('Merhaba Dünya!')
        assert lang == Language.TR_TR

    def test_detect_lang_vi(self, tcontext):
        # 测试越南语
        lang = detect_lang('Chào thế giới!')
        assert lang == Language.VI_VN

    def test_detect_lang_ar(self, tcontext):
        # 测试阿拉伯语
        lang = detect_lang('مرحبا بالعالم!')
        assert lang == Language.AR_AE

    def test_detect_lang_fr(self, tcontext):
        # 测试法语
        lang = detect_lang('Bonjour le monde!')
        assert lang == Language.FR_FR

    def test_detect_lang_pt(self, tcontext):
        # 测试葡萄牙语 - 使用带有葡语特征的句子（包含 ção, ã 等特殊字符）
        lang = detect_lang('Bom dia! Como você está? A situação está muito boa.')
        assert lang == Language.PT_PT

    def test_detect_lang_de(self, tcontext):
        # 测试德语 - 使用带有德语特征的句子（包含德语特有的词序和umlauts）
        lang = detect_lang('Ich möchte ein Stück Apfelstrudel mit Schlagsahne bestellen')
        assert lang == Language.DE_DE

    def test_detect_lang_th(self, tcontext):
        # 测试泰语
        lang = detect_lang('สวัสดีชาวโลก!')
        assert lang == Language.TH_TH

    def test_detect_lang_it(self, tcontext):
        # 测试意大利语 - 使用带有意大利语特征的句子（包含典型的意大利语词汇和语法结构）
        lang = detect_lang('Buongiorno! Come stai? Il tempo è bellissimo oggi.')
        assert lang == Language.IT_IT

    def test_detect_lang_pl(self, tcontext):
        # 测试波兰语
        lang = detect_lang('Witaj świecie!')
        assert lang == Language.PL_PL

    def test_detect_lang_slavic_bg(self, tcontext):
        # 测试保加利亚语，应该映射到俄语
        lang = detect_lang('Здравей свят!')
        assert lang == Language.RU_KZ

    def test_detect_lang_slavic_uk(self, tcontext):
        # 测试乌克兰语，应该映射到俄语
        lang = detect_lang('Привіт світ!')
        assert lang == Language.RU_KZ

    def test_detect_lang_slavic_sr(self, tcontext):
        # 测试塞尔维亚语，应该映射到俄语
        lang = detect_lang('Здраво свете!')
        assert lang == Language.RU_KZ

    def test_detect_lang_ps_fa(self, tcontext):
        # 测试普什图语，应该映射到波斯语
        lang = detect_lang('سلام دنیا!')
        assert lang == Language.FA_IR

    def test_detect_lang_null(self, tcontext):
        lang = detect_lang('aasssss')
        assert lang is None

    def test_detect_wired_space(self, tcontext):
        lang = detect_lang('bu l l')
        assert lang is Language.FA_IR

    def test_detect_space(self, tcontext):
        lang = detect_lang('bu l l')
        assert lang is Language.EN_US

    def test_detect_not_supported(self, tcontext):
        lang = detect_lang('Lezat bordin goftam az 92,93 payin tar nemiad 😎')
        assert lang is None

    def test_detect_not_supported2(self, tcontext):
        lang = detect_lang('معلوم نیست فعلا که.')
        assert lang is Language.FA_IR

    def test_detect_zh_with_emoji(self, tcontext):
        lang = detect_lang('up up 👌')
        assert lang == Language.EN_US

    def test_detect_ok_emoji(self, tcontext):
        lang = detect_lang('👌')
        assert lang == None

    def test_detect_smile2_emoji(self, tcontext):
        lang = detect_lang('☺️')
        assert lang == None

