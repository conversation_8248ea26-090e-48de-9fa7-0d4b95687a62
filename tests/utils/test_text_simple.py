import pytest
from flask import g

from app.utils.text import check_simple_text


@pytest.fixture(scope='class')
def class_setup(tcontext):
    try:
        with tcontext:
            g.lang = 'zh_Hant_HK'
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('class_setup')
class TestTextSimple:
    def test_empty_text(self, tcontext):
        # 测试空字符串
        assert check_simple_text('') is True
        assert check_simple_text(' ') is True
        assert check_simple_text('\n') is True
        assert check_simple_text('\t') is True
        assert check_simple_text('   \n\t   ') is True

    def test_numbers_only(self, tcontext):
        # 测试纯数字
        assert check_simple_text('123') is True
        assert check_simple_text('123 456') is True
        assert check_simple_text('123\n456') is True
        assert check_simple_text('  123  ') is True

    def test_emoji_only(self, tcontext):
        # 测试纯表情
        assert check_simple_text('😊') is True
        assert check_simple_text('😊 😂') is True
        assert check_simple_text('😊\n😂') is True
        assert check_simple_text('  😊  ') is True
        assert check_simple_text('🩷') is True

    def test_punctuation_only(self, tcontext):
        # 测试纯标点
        assert check_simple_text('!@#$%') is True
        assert check_simple_text('!!!') is True
        assert check_simple_text('! ? .') is True
        assert check_simple_text('  !!!  ') is True
        assert check_simple_text('!\n?') is True

    def test_mention_only(self, tcontext):
        # 测试纯@用户
        assert check_simple_text('@user') is True
        assert check_simple_text('@user @another') is True
        assert check_simple_text('@user\n@another') is True
        assert check_simple_text('  @user  ') is True

    def test_mention_converted(self, tcontext):
        # 测试转义的@用户
        assert check_simple_text('{{mention:user1679}}') is True
        assert check_simple_text('{{mention:user1679}} {{mention:user1680}}') is True
        assert check_simple_text('{{mention:user1679}}\n{{mention:user1680}}') is True
        assert check_simple_text('  {{mention:user1679}}  ') is True

    def test_mixed_content(self, tcontext):
        # 测试混合内容
        assert check_simple_text('@user 123') is True
        assert check_simple_text('@user 😊') is True
        assert check_simple_text('@user !!!') is True
        assert check_simple_text('123 😊 !!!') is True
        assert check_simple_text('@user 123 😊 !!!') is True
        assert check_simple_text('  @user  123  😊  !!!  ') is True
        assert check_simple_text('@user\n123\n😊\n!!!') is True
        assert check_simple_text('{{mention:user1679}} 123') is True
        assert check_simple_text('{{mention:user1679}} 😊') is True
        assert check_simple_text('{{mention:user1679}} !!!') is True
        assert check_simple_text('123 😊 !!!') is True

    def test_normal_text(self, tcontext):
        # 测试普通文本（应该返回 False）
        assert check_simple_text('Hello World') is False
        assert check_simple_text('你好世界') is False
        assert check_simple_text('Hello 123') is False
        assert check_simple_text('你好 @user') is False
        assert check_simple_text('Hello 😊') is False
        assert check_simple_text('你好！') is False

    def test_complex_mixed(self, tcontext):
        # 测试复杂混合情况
        assert check_simple_text('123 @user !!! 😊\n456 @another ### 😂') is True
        assert check_simple_text('  123  \n  @user  \n  !!!  \n  😊  ') is True
        assert check_simple_text('@user123 !!! 😊') is True
        assert check_simple_text('@user_name !!! 😊') is True

