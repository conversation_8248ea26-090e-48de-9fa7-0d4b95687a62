import pytest
from flask import g


@pytest.fixture(scope='class')
def class_setup(tcontext):
    try:
        with tcontext:
            g.lang = 'zh_Hant_HK'
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('class_setup')
class TestText:
    def test_clean_whitespace(self, tcontext):
        # 测试清理空白字符
        from app.utils.text import clean_whitespace

        # 测试不同类型的换行符
        assert clean_whitespace('hello\r\nworld') == 'hello\n\nworld'
        assert clean_whitespace('hello\rworld') == 'hello\nworld'
        assert clean_whitespace('hello\nworld') == 'hello\nworld'

        # 测试连续的换行符
        assert clean_whitespace('hello\n\n\nworld') == 'hello\n\nworld'
        assert clean_whitespace('hello\r\n\r\nworld') == 'hello\n\nworld'
        assert clean_whitespace('hello\r\r\rworld') == 'hello\n\nworld'

        # 测试首尾空白
        assert clean_whitespace('  hello world  ') == 'hello world'
        assert clean_whitespace('\nhello world\n') == 'hello world'
        assert clean_whitespace('\r\nhello world\r\n') == 'hello world'

        # 测试混合情况
        assert clean_whitespace('  hello\n\r\n  world  \r\n') == 'hello\n\n  world'
        assert clean_whitespace('\r\n\n  hello  \r\rworld\n\n') == 'hello  \n\nworld'

        # 测试空字符串
        assert clean_whitespace('') == ''
        assert clean_whitespace('   ') == ''
        assert clean_whitespace('\n\r\n\r') == ''
