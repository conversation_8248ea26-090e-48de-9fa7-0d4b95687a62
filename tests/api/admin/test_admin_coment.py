import pytest
from flask import g
from app.models.comment import Comment
from app.models.moderation import CommentModeration
from app.common.constants import Language
from app.schedules.comment_task import initial_moderate
from tests.common.t_common import default_lang
from tests.common.mock_redis import patch_redis
from tests.common.mock_sqlalchemy import patch_sqlalchemy

DEFAULT_USER_ID = 2


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.user_id = DEFAULT_USER_ID
        yield tcontext
    finally:
        with tcontext:
            pass



@pytest.fixture(scope='module')
def client(tapp):
    """Flask测试客户端"""
    return tapp.test_client()

@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestAdminCommentModerationDetail:
    @staticmethod
    def _create_test_comment(content, lang=Language.ZH_HANS_CN):
        """创建测试评论的辅助方法"""
        from app.models import db
        comment = Comment(
            business=Comment.Business.COIN,
            business_id="1",
            business_code="BTC",
            content=content,
            lang=lang,
            user_id=DEFAULT_USER_ID,
            status=Comment.CommentStatus.CREATED
        )
        db.session.add(comment)
        db.session.commit()
        return comment

    def test_get_moderation_detail_violations(self, tcontext, client):
        """测试各种违规类型的内容审核"""
        test_cases = [
            # 辱骂、攻击性语言
            {
                "content": "你这个白痴，滚出去！",
                "expected_type": CommentModeration.RejectType.ABUSE
            },
            # 违禁品交易
            {
                "content": "出售违禁药品，私聊联系",
                "expected_type": CommentModeration.RejectType.ILLEGAL
            },
            # 色情内容
            {
                "content": "提供成人服务，详情私聊",
                "expected_type": CommentModeration.RejectType.PORN
            },
            # 垃圾广告
            {
                "content": "推广优质理财产品，联系微信：xxx",
                "expected_type": CommentModeration.RejectType.OTHER
            },
            # 暴力内容
            {
                "content": "我要炸了这个地方，你们都得死",
                "expected_type": CommentModeration.RejectType.VIOLENCE
            },
            # 歧视性言论
            {
                "content": "南方人就是懒惰，根本不适合做这行",
                "expected_type": CommentModeration.RejectType.DISCRIMINATION
            },
            # 多重违规（应该返回主要违规类型）
            {
                "content": "你这个蠢货，来买我的违禁药品",
                "expected_type": CommentModeration.RejectType.ILLEGAL  # 假设违禁品是主要违规
            }
        ]

        with tcontext:
            for case in test_cases:
                # 创建评论
                comment = self._create_test_comment(case['content'])

                initial_moderate(comment.id)

                # 调用API获取详细审核结果
                response = client.post(f'/admin/moderation/detail', json={
                    'comment_id': comment.id
                })

                # 验证响应状态码和基本结构
                assert response.status_code == 200
                data = response.get_json()['data']
                print(f'content: {case["content"]},moderation result: {data}')
                # 验证审核结果
                assert data['passed'] is False
                assert data['reject_type'] == case['expected_type'].name
                assert data['reason'] is not None

                # 验证数据库中的审核记录
                moderation = CommentModeration.query.filter_by(comment_id=comment.id).first()
                assert moderation is not None
                assert moderation.status == CommentModeration.Status.REJECTED
                assert moderation.rejected_type == case['expected_type']

                # 验证详细信息结构
                details = moderation.moderation_detail
                assert details is not None
                assert 'all_violations' in details
                assert 'violation_text' in details
                assert 'violation_reasons' in details

                # 验证违规信息
                assert case['expected_type'].name in details['all_violations']
                assert len(details['violation_text']) > 0
                assert len(details['violation_reasons']) > 0

                print(f"Testing content: {case['content']}")
                print(f"Expected type: {case['expected_type'].name}")
                print(f"Actual type: {data['reject_type']}")
                print(f"Violation details: {details}")
                print("---")