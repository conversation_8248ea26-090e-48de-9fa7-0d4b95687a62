import random
import pytest
from flask import g
from undecorated import undecorated

from app.utils.date_ import now
from tests.common.mock_sqlalchemy import patch_sqlalchemy
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang
from app.common.constants import Language
from app.models.comment import Comment, CommentWarning, CommentFullTextSearch
from app.models.moderation import CommentModeration, CommentReport
from app.models.user import UserInfo
from app.models.statistics import CommentUserStatistics
from app.models.base import db

DEFAULT_USER_ID = 2
DEFAULT_ADMIN_ID = 1

def _random_business_code():
    codes = ['ETH', 'BTC', 'ETC', 'CET', 'BSC', 'ADA']
    return random.choice(codes)

@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.user_id = DEFAULT_ADMIN_ID
            # 创建10个测试用户
            for i in range(1, 11):
                user = UserInfo(
                    user_id=i,
                    account_name=f"test_account_{i}",
                    name=f"Test User {i}",
                    avatar=f"test_avatar_{i}"
                )
                db.session.add(user)
                db.session.flush()
                user.user_id = user.id
            db.session.commit()
            
            # 创建100条测试评论
            comments = []
            for i in range(100):
                user_id = random.randint(1, 10)  # 随机选择一个用户
                code = _random_business_code()
                comment = Comment(
                    business=Comment.Business.COIN,
                    business_id=code,
                    business_code=code,
                    content=f"测试评论 {i}",
                    lang=random.choice(list(Language)),  # 随机语言
                    user_id=user_id,
                    status=random.choice(list(Comment.CommentStatus))  # 随机状态
                )
                comments.append(comment)
                    
            db.session.add_all(comments)
            db.session.commit()
            
        yield tcontext
    finally:
        with tcontext:
            # 清理测试数据
            CommentFullTextSearch.query.delete()
            Comment.query.delete()
            UserInfo.query.delete()
            db.session.commit()

@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestAdminCommentResource:
    """测试评论管理接口"""

    @staticmethod
    def _create_test_user():
        """创建测试用户"""
        from app.models import db
        from app.models.user import UserInfo

        users = []
        max_user_id = db.session.query(db.func.max(UserInfo.user_id)).scalar() or 0
        for i in range(2):
            max_user_id += 1
            user = UserInfo(
                user_id = max_user_id + 1,
                account_name=f'account_name_{i}',
                name=f'Test John Doe {i}',
                avatar=f'test_file_key{i}'
            )
            db.session.add(user)
            users.append(user)
        db.session.commit()
        return users

    @staticmethod
    def _create_test_comment(business=Comment.Business.COIN, content="测试评论", user_id=None):
        """创建测试评论"""
        from app.models import db
        from app.business.comment_manager import CommentManager
        
        if not user_id:
            user_id = DEFAULT_USER_ID
            
        code = _random_business_code()
        comment = CommentManager.create_comment(
            business=business,
            business_id=code,
            business_code=code,
            content=content,
            lang=Language.ZH_HANS_CN,
            user_id=user_id
        )
        db.session.commit()
        return comment

    def test_get_comment_list_basic(self, tcontext):
        """测试基本的评论列表获取"""
        with tcontext:
            from app.api.admin.admin_comment import AdminCommentResource
            result = undecorated(AdminCommentResource.get)(
                AdminCommentResource,
                business=Comment.Business.COIN.name,
                page=1,
                limit=10
            )
            
            assert 'items' in result
            assert len(result['items']) == 10
            assert result['total'] > 0
            
    def test_get_comment_list_with_filters(self, tcontext):
        """测试带过滤条件的评论列表获取"""
        with tcontext:
            from app.api.admin.admin_comment import AdminCommentResource
            
            # 测试语言过滤
            result = undecorated(AdminCommentResource.get)(
                AdminCommentResource,
                business=Comment.Business.COIN.name,
                lang=Language.ZH_HANS_CN.name,
                page=1,
                limit=10
            )
            for item in result['items']:
                assert item['lang'] == Language.ZH_HANS_CN.name
                
            # 测试状态过滤
            result = undecorated(AdminCommentResource.get)(
                AdminCommentResource,
                business=Comment.Business.COIN.name,
                status=Comment.CommentStatus.PUBLISHED.name,
                page=1,
                limit=10
            )
            for item in result['items']:
                assert item['status'] == Comment.CommentStatus.PUBLISHED.name
                
            # 测试用户ID过滤
            result = undecorated(AdminCommentResource.get)(
                AdminCommentResource,
                business=Comment.Business.COIN.name,
                user_id=1,
                page=1,
                limit=10
            )
            for item in result['items']:
                assert item['user_id'] == 1
                
            # 测试时间范围过滤
            from datetime import datetime, timedelta
            start_time = now() - timedelta(days=1)
            result = undecorated(AdminCommentResource.get)(
                AdminCommentResource,
                business=Comment.Business.COIN.name,
                start_time=start_time,
                page=1,
                limit=10
            )
            for item in result['items']:
                assert item['created_at'] >= start_time

    def test_user_search(self, tcontext):
        """测试用户搜索功能"""
        with tcontext:
            from app.api.admin.admin_comment import UserSearchResource
            
            # 测试精确匹配
            result = undecorated(UserSearchResource.get)(
                UserSearchResource,
                user_name="Test User 1",
                limit=10
            )
            assert len(result['items']) > 0
            assert "Test User 1" in result['items'][0]['user_name']
            
            # 测试模糊匹配
            result = undecorated(UserSearchResource.get)(
                UserSearchResource,
                user_name="Test",
                limit=10
            )
            assert len(result['items']) == 10  # 应该匹配所有测试用户
            
            # 测试限制条数
            result = undecorated(UserSearchResource.get)(
                UserSearchResource,
                user_name="Test",
                limit=5
            )
            assert len(result['items']) == 5

    def test_get_comment_detail(self, tcontext):
        """测试获取评论详情"""
        with tcontext:
            # 创建测试用户和评论
            self._create_test_user()
            comment = self._create_test_comment()
            
            # 创建警告记录
            warning = CommentWarning(
                comment_id=comment.id,
                title="测试警告",
                content="警告内容"
            )
            db.session.add(warning)
            db.session.commit()
            
            from app.api.admin.admin_comment import AdminCommentDetailResource
            # 获取评论详情
            result = undecorated(AdminCommentDetailResource.get)(
                AdminCommentDetailResource,
                comment_id=comment.id,
                reply_page=1,
                reply_limit=10
            )
            
            # 验证返回结果
            assert 'data' in result
            assert 'warnings' in result
            assert 'replies' in result
            assert 'langs' in result
            
            # 验证警告信息
            warnings = result['warnings']
            assert len(warnings) > 0
            assert warnings[0]['title'] == "测试警告"
            assert warnings[0]['content'] == "警告内容"

    def test_get_comment_reports(self, tcontext):
        """测试获取评论举报信息"""
        with tcontext:
            # 创建测试评论
            comment = self._create_test_comment()
            
            # 创建举报记录
            report = CommentReport(
                comment_id=comment.id,
                comment_user_id=DEFAULT_USER_ID,
                user_id=DEFAULT_USER_ID,
                type=CommentReport.Type.SPAM,
                reason="测试举报"
            )
            db.session.add(report)
            db.session.commit()
            
            from app.api.admin.admin_comment import AdminCommentReportsResource
            # 获取举报信息
            result = undecorated(AdminCommentReportsResource.get)(
                AdminCommentReportsResource,
                comment_id=comment.id
            )
            
            # 验证返回结果
            assert 'items' in result
            assert 'total' in result
            assert 'statistics' in result
            assert 'report_types' in result
            
            # 验证举报详情
            items = result['items']
            assert len(items) > 0
            first_item = items[0]
            assert first_item['type'] == "垃圾"
            assert first_item['reason'] == "测试举报"

    def test_get_moderation_list(self, tcontext):
        """测试获取违规内容列表"""
        with tcontext:
            # 创建测试评论
            comment = self._create_test_comment()
            
            # 创建审核记录
            moderation = CommentModeration(
                comment_id=comment.id,
                status=CommentModeration.Status.REJECTED,
                phase=CommentModeration.Phase.INITIAL,
                moderator_type=CommentModeration.ModeratorType.AI,
                rejected_type=CommentModeration.RejectType.ABUSE,
                trigger=CommentModeration.Trigger.AUTO,
                reason="测试审核"
            )
            db.session.add(moderation)
            db.session.commit()
            
            from app.api.admin.admin_comment import AdminCommentModerationResource
            # 获取违规内容列表
            result = undecorated(AdminCommentModerationResource.get)(
                AdminCommentModerationResource,
                business=Comment.Business.COIN.name,
                page=1,
                limit=10
            )
            
            # 验证返回结果
            assert 'items' in result
            assert 'total' in result
            assert 'page' in result
            assert 'status_map' in result
            assert 'moderator_type_map' in result
            assert 'reject_type_map' in result

    def test_get_user_stats(self, tcontext):
        """测试获取用户评论统计"""
        with tcontext:
            # 创建测试用户
            self._create_test_user()
            
            # 创建用户统计记录
            stats = CommentUserStatistics(
                user_id=DEFAULT_USER_ID,
                comment_count=10,
                up_count=5,
                down_count=2,
                report_count=1
            )
            db.session.add(stats)
            db.session.commit()
            
            from app.api.admin.admin_comment import AdminStatsResource
            # 获取用户统计信息
            result = undecorated(AdminStatsResource.get)(
                AdminStatsResource,
                page=1,
                limit=10
            )
            
            # 验证返回结果
            assert 'items' in result
            assert 'total' in result
            assert 'page' in result
            
            # 验证统计数据
            items = result['items']
            assert len(items) > 0