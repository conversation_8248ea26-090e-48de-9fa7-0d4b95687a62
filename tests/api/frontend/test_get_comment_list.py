import pytest
from flask import g
from undecorated import undecorated

from tests.common.mock_sqlalchemy import patch_sqlalchemy
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang
from app.common.constants import Language
from app.models.comment import Comment



DEFAULT_USER_ID = 2

@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.user_id = DEFAULT_USER_ID
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentListResource:
    business = Comment.Business.COIN
    business_id = "161"
    business_code = "BTC"
    content = "test comment content"

    @staticmethod
    def _verify_comment_format(comment):
        """Helper method to verify comment format"""
        from app.models.comment import Comment
        required_fields = [
            'id', 'content', 'created_at', 'created_by',
            'reply_count', 'score', 'is_voted'
        ]
        for field in required_fields:
            assert field in comment

    @staticmethod
    def _create_mock_comment(business, business_id, business_code, content, parent_id=None, root_id=None, lang=Language.ZH_HANS_CN):
        from app.models import db
        from app.business.comment_manager import CommentManager
        
        comment = CommentManager.create_comment(
            business=business,
            business_id=business_id,
            business_code=business_code,
            content=content,
            lang=lang,
            user_id=g.user_id,
            parent_id=parent_id,
            root_id=root_id if root_id else parent_id if parent_id else None
        )
        db.session.commit()
        return comment

    @staticmethod
    def _call_get_comment_list(**kwargs):
        from app.api.v1.comment import CommentListResource
        return undecorated(CommentListResource.get)(CommentListResource, **kwargs)
        
    def test_get_hot_comments(self, tcontext):
        """Test getting hot comments"""
        with tcontext:
            res = self._call_get_comment_list(
                business=self.business,
                business_id=self.business_id,
                lang=Language.EN_US,
                sort_type=Comment.SortType.HOT,
                limit=10
            )
            assert isinstance(res, list)
            if res:
                self._verify_comment_format(res[0])

    def test_get_new_comments(self, tcontext):
        """测试获取最新评论功能"""
        with tcontext:
            # 先创建3条测试评论
            test_comments = []
            for i in range(3):
                comment = self._create_mock_comment(
                    business=self.business,
                    business_id=self.business_id,
                    business_code=self.business_code,
                    content=f"测试评论 {i}",
                    lang=Language.ZH_HANS_CN
                )
                test_comments.append(comment)

            # 获取评论列表
            res = self._call_get_comment_list(
                business=self.business,
                business_id=self.business_id,
                lang=Language.ZH_HANS_CN,
                sort_type=Comment.SortType.NEW,
                limit=10
            )

            # 验证返回结果
            assert isinstance(res, list)
            assert len(res) >= 3  # 至少应该包含我们刚创建的3条评论
            # 验证新建的3条评论在结果集中
            new_comment_ids = [comment.id for comment in test_comments]
            result_comment_ids = [comment['id'] for comment in res]
            for new_comment_id in new_comment_ids:
                assert new_comment_id in result_comment_ids

            # 验证第一条评论的格式和内容
            if res:
                self._verify_comment_format(res[0])
            
            # 验证新创建的评论都在返回结果中
            returned_ids = [comment['id'] for comment in res]
            for test_comment in test_comments:
                assert test_comment.id in returned_ids


    def test_get_top_comments(self, tcontext):
        """Test getting top comments"""
        with tcontext:
            res = self._call_get_comment_list(
                business=self.business,
                business_id=self.business_id,
                lang=Language.EN_US,
                sort_type=Comment.SortType.TOP,
                limit=10
            )
            assert isinstance(res, list)
            if res:
                self._verify_comment_format(res[0])

    def test_pagination(self, tcontext):
        """Test comment list pagination"""
        with tcontext:
            first_page = self._call_get_comment_list(
                business=self.business,
                business_id=self.business_id,
                lang=Language.EN_US,
                sort_type=Comment.SortType.HOT,
                limit=1
            )
            assert isinstance(first_page, list)
            assert len(first_page) >= 1 # 首页的内容，可能比需要的多

            if first_page:
                last_comment = first_page[-1]
                second_page = self._call_get_comment_list(
                    business=self.business,
                    business_id=self.business_id,
                    lang=Language.EN_US,
                    sort_type=Comment.SortType.HOT,
                    last_score=last_comment['score'],
                    last_id=last_comment['id'],
                    limit=1
                )
                assert isinstance(second_page, list)
                assert len(second_page) == 1

    @staticmethod
    def _ensure_comment_exists(comment_id, business, business_id, business_code, content):
        """
        确保指定 ID 的评论存在，如果不存在则创建
        
        Args:
            comment_id: 需要确保存在的评论 ID
            business: 业务类型
            business_id: 业务 ID
            business_code: 业务代码
            content: 评论内容
            
        Returns:
            Comment: 已存在或新创建的评论对象
        """
        from app.models import db
        from app.models.comment import Comment
        from app.business.comment_manager import CommentManager
        
        # 先查找是否存在指定 ID 的评论
        existing_comment = Comment.query.get(comment_id)
        if existing_comment:
            return existing_comment
            
        # 不存在则创建新评论
        # 手动设置 ID
        comment = Comment(
            id=comment_id,
            business=business,
            business_id=business_id,
            business_code=business_code,
            content=content,
            lang=Language.EN_US,
            user_id=g.user_id
        )
        
        try:
            db.session.add(comment)
            db.session.flush()  # 检查是否有 ID 冲突
            db.session.commit()
            return comment
        except Exception as e:
            db.session.rollback()
            raise e

    def test_highlight_comment(self, tcontext):
        """Test highlight comment feature"""
        with tcontext:
            # 确保 ID 为 2 的评论存在
            comment = self._ensure_comment_exists(
                comment_id=2,
                business=self.business,
                business_id=self.business_id,
                business_code=self.business_code,
                content="This is comment to be highlighted"
            )
            
            res = self._call_get_comment_list(
                business=self.business,
                business_id=self.business_id,
                lang=Language.EN_US,
                sort_type=Comment.SortType.HOT,
                limit=10,
                highlight_comment=2
            )
            assert isinstance(res, list)
            assert len(res) > 0
            assert res[0]['id'] == 2
            assert res[0]['highlighted'] is True

    def test_highlight_nested_reply(self, tcontext):
        """测试高亮嵌套回复的功能"""
        with tcontext:
            # 创建一个父评论
            parent_comment = self._create_mock_comment(
                business=self.business,
                business_id=self.business_id,
                business_code=self.business_code,
                content="父评论"
            )
            
            # 创建一个要高亮的回复
            highlighted_reply = self._create_mock_comment(
                business=self.business,
                business_id=self.business_id,
                business_code=self.business_code,
                content="高亮回复",
                parent_id=parent_comment.id  # 设置父评论ID
            )

            # 获取评论列表，设置高亮回复的ID
            res = self._call_get_comment_list(
                business=self.business,
                business_id=self.business_id,
                lang=Language.EN_US,
                sort_type=Comment.SortType.HOT,
                limit=10,
                highlight_comment=highlighted_reply.id
            )

            # 验证返回结果
            assert isinstance(res, list)
            assert len(res) > 0
            first_highlighted = res[0]
            
            # 验证一级评论是否在第一位
            assert first_highlighted['id'] == parent_comment.id
            assert first_highlighted['highlighted'] is True
            
            # 验证引用的高亮回复是否在第2位
            seceond_highlighted = first_highlighted['children'][0]
            assert seceond_highlighted['id'] == highlighted_reply.id
            assert seceond_highlighted['highlighted'] is True

            # 验证基本字段格式
            self._verify_comment_format(first_highlighted)
            self._verify_comment_format(seceond_highlighted)

    def test_get_chinese_top_comments(self, tcontext):
        """测试中文环境下获取指定用户评论功能"""
        with tcontext:
            # 创建一些测试评论数据
            test_comments = []
            g.user_id = 22406
            # 获取评论列表
            res = self._call_get_comment_list(
                business=Comment.Business.COIN,
                business_id="4",
                business_code="BTC",
                lang=Language.ZH_HANS_CN,
                sort_type=Comment.SortType.TOP,
                limit=10
            )

            # 验证返回结果
            assert isinstance(res, list)
            if res:
                self._verify_comment_format(res[0])

    def test_get_report_types(self, tcontext):
        """测试获取评论举报类型列表"""
        with tcontext:
            from app.api.v1.comment import CommentReportTypesResource
            
            # 调用接口获取举报类型列表
            res = undecorated(CommentReportTypesResource.get)(CommentReportTypesResource)
            
            # 验证返回数据格式
            assert isinstance(res, list)

            for report_type in res:            
                # 验证是结果集是一个key 和 value 都为 str 的 dict
                for key, value in report_type.items():
                    assert isinstance(key, str)
                    assert isinstance(value, str)

    def test_get_comment_count(self, tcontext):
        """测试获取评论数量接口"""
        with tcontext:
            # 先创建几条测试评论
            test_comments = []
            for i in range(3):
                comment = self._create_mock_comment(
                    business=self.business,
                    business_id=self.business_id,
                    business_code=self.business_code,
                    content=f"测试评论 {i}"
                )
                test_comments.append(comment)

            # 创建一条回复评论(不应该被计入根评论数)
            reply = self._create_mock_comment(
                business=self.business,
                business_id=self.business_id,
                business_code=self.business_code,
                content="这是一条回复",
                parent_id=test_comments[0].id
            )

            # 调用获取评论数量接口
            from app.api.v1.comment import CommentCountResource
            res = undecorated(CommentCountResource.get)(
                CommentCountResource,
                business=self.business,
                business_id=self.business_id,
                lang=Language.EN_US
            )

            # 验证返回格式
            assert isinstance(res, dict)
            assert 'root_count' in res
            assert isinstance(res['root_count'], int)

    def test_get_comments_online_last_id(self, tcontext, tapp):
        """获取测试环境评论列表"""
        with tcontext:
            # 创建测试用户
            client = tapp.test_client()
            response = client.get('/res/comment/comment-list?business=COIN&business_id=244&lang=EN_US&sort_type=HOT&limit=50&last_id=6993&last_score=-295.1800038848665')
            assert response.status_code == 200
            result = response.get_json()

            assert 'data' in result
            ret_data = result['data']

            # 验证返回的评论数据
            assert isinstance(ret_data, list)

    def test_get_comments_online(self, tcontext, tapp):
        """获取测试环境评论列表"""
        with tcontext:
            # 创建测试用户
            client = tapp.test_client()
            response = client.get('/res/comment/comment-list?business=COIN&business_id=244&lang=EN_US&sort_type=HOT&limit=50')
            assert response.status_code == 200
            result = response.get_json()

            assert 'data' in result
            ret_data = result['data']

            # 验证返回的评论数据
            assert isinstance(ret_data, list)
