from time import sleep

import pytest
from flask import g
from undecorated import undecorated

from app.common.constants import Language
from tests.common.mock_sqlalchemy import patch_sqlalchemy
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang
from tests.common.patch_celery import patch_celery
from app.models.comment import Comment

DEFAULT_USER_ID = 2


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.user_id = DEFAULT_USER_ID
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentVote:

    business = Comment.Business.COIN
    business_id = "1"
    business_code = "BTC"
    content = "test reply content"

    def _new_comment(self):
        from app.models import db
        from app.business.comment_manager import CommentManager

        comment = CommentManager.create_comment(
            user_id=3,
            content=self.content,
            lang=Language.EN_US,
            business=self.business,
            business_id=self.business_id,
            business_code=str(self.business_code),
        )
        db.session.commit()
        return comment

    def _new_comment_reply(self, parent_id):
        from app.models import db
        from app.business.comment_manager import CommentManager

        comment_reply = CommentManager.create_comment(
            root_id=parent_id,
            parent_id=parent_id,
            content=self.content,
            lang=Language.EN_US,
            user_id=4,
            business=self.business,
            business_id=self.business_id,
            business_code=str(self.business_code),
        )
        db.session.commit()
        return comment_reply

    def test_vote(self, tcontext):
        with (tcontext):
            from app.api.v1.comment import CommentVoteResource, CommentVote
            from app.models.event import CommentEvent
            from app.cache.comment_cache import CommentCache

            comment1 = self._new_comment()
            comment2 = self._new_comment()

            # 测试点赞
            undecorated(CommentVoteResource.post)(CommentVoteResource, comment1.id, **{"vote": 1})
            cvs = CommentVote.query.filter(
                CommentVote.comment_id == comment1.id, CommentVote.user_id == DEFAULT_USER_ID
            ).all()
            assert len(cvs) == 1
            cv = cvs[0]
            assert cv.vote == 1
            comment1_cache = CommentCache(comment1.id)
            assert comment1_cache.read()['up_count'] == '1'

            ces = CommentEvent.query.filter(
                CommentEvent.comment_id == comment1.id, CommentEvent.type == CommentEvent.EventType.UP).all()
            assert len(ces) == 2

            # 测试点踩
            undecorated(CommentVoteResource.post)(CommentVoteResource, comment2.id, **{"vote": -1})
            cvs = CommentVote.query.filter(
                CommentVote.comment_id == comment2.id, CommentVote.user_id == DEFAULT_USER_ID
            ).all()
            assert len(cvs) == 1
            cv = cvs[0]
            assert cv.vote == -1
            comment2_cache = CommentCache(comment2.id)
            assert comment2_cache.read()['down_count'] == '1'

            ces = CommentEvent.query.filter(
                CommentEvent.comment_id == comment2.id, CommentEvent.type == CommentEvent.EventType.DOWN).all()
            assert len(ces) == 2

            # 测试点赞后点赞
            undecorated(CommentVoteResource.post)(CommentVoteResource, comment1.id, **{"vote": 1})
            cvs = CommentVote.query.filter(
                CommentVote.comment_id == comment1.id, CommentVote.user_id == DEFAULT_USER_ID
            ).all()
            assert len(cvs) == 1
            cv = cvs[0]
            assert cv.vote == 1
            comment1_cache = CommentCache(comment1.id)
            assert comment1_cache.read()['up_count'] == '1'

            ces = CommentEvent.query.filter(
                CommentEvent.comment_id == comment1.id, CommentEvent.type == CommentEvent.EventType.UP).all()
            assert len(ces) == 2

            # 测试点踩后点踩
            undecorated(CommentVoteResource.post)(CommentVoteResource, comment2.id, **{"vote": -1})
            cvs = CommentVote.query.filter(
                CommentVote.comment_id == comment2.id, CommentVote.user_id == DEFAULT_USER_ID
            ).all()
            assert len(cvs) == 1
            cv = cvs[0]
            assert cv.vote == -1
            comment2_cache = CommentCache(comment2.id)
            assert comment2_cache.read()['down_count'] == '1'

            ces = CommentEvent.query.filter(
                CommentEvent.comment_id == comment2.id, CommentEvent.type == CommentEvent.EventType.DOWN).all()
            assert len(ces) == 2

            # 测试点赞后点踩
            undecorated(CommentVoteResource.post)(CommentVoteResource, comment1.id, **{"vote": -1})
            cvs = CommentVote.query.filter(
                CommentVote.comment_id == comment1.id, CommentVote.user_id == DEFAULT_USER_ID
            ).all()
            assert len(cvs) == 1
            cv = cvs[0]
            assert cv.vote == -1
            comment1_cache = CommentCache(comment1.id)
            assert comment1_cache.read()['down_count'] == '1'
            assert comment1_cache.read()['up_count'] == '0'

            ces = CommentEvent.query.filter(
                CommentEvent.comment_id == comment1.id, CommentEvent.type == CommentEvent.EventType.UP).all()
            assert len(ces) == 2

            ces = CommentEvent.query.filter(
                CommentEvent.comment_id == comment1.id, CommentEvent.type == CommentEvent.EventType.DOWN).all()
            assert len(ces) == 2

            # 测试点踩后点赞
            undecorated(CommentVoteResource.post)(CommentVoteResource, comment2.id, **{"vote": 1})
            cvs = CommentVote.query.filter(
                CommentVote.comment_id == comment2.id, CommentVote.user_id == DEFAULT_USER_ID
            ).all()
            assert len(cvs) == 1
            cv = cvs[0]
            assert cv.vote == 1
            comment2_cache = CommentCache(comment2.id)
            assert comment2_cache.read()['down_count'] == '0'
            assert comment2_cache.read()['up_count'] == '1'

            ces = CommentEvent.query.filter(
                CommentEvent.comment_id == comment2.id, CommentEvent.type == CommentEvent.EventType.DOWN).all()
            assert len(ces) == 2
            ces = CommentEvent.query.filter(
                CommentEvent.comment_id == comment2.id, CommentEvent.type == CommentEvent.EventType.UP).all()
            assert len(ces) == 2

    def test_remove_vote(self, tcontext):
        with tcontext:
            from app.api.v1.comment import CommentVoteResource, CommentVote
            from app.cache.comment_cache import CommentCache

            comment1 = self._new_comment()
            comment2 = self._new_comment()

            # 测试取消点赞
            undecorated(CommentVoteResource.post)(CommentVoteResource, comment1.id, **{"vote": 1})
            undecorated(CommentVoteResource.delete)(CommentVoteResource, comment1.id, **{"vote": 1})
            cvs = CommentVote.query.filter(
                CommentVote.comment_id == comment1.id, CommentVote.user_id == DEFAULT_USER_ID
            ).all()
            assert len(cvs) == 1
            cv = cvs[0]
            assert cv.vote == 0
            comment1_cache = CommentCache(comment1.id)
            assert comment1_cache.read()['up_count'] == '0'
            assert comment1_cache.read()['down_count'] == '0'

            # 测试取消点踩
            undecorated(CommentVoteResource.post)(CommentVoteResource, comment1.id, **{"vote": -1})
            comment1_cache = CommentCache(comment1.id)
            assert comment1_cache.read()['up_count'] == '0'
            assert comment1_cache.read()['down_count'] == '1'
            undecorated(CommentVoteResource.delete)(CommentVoteResource, comment1.id, **{"vote": -1})
            cvs = CommentVote.query.filter(
                CommentVote.comment_id == comment1.id, CommentVote.user_id == DEFAULT_USER_ID
            ).all()
            assert len(cvs) == 1
            cv = cvs[0]
            assert cv.vote == 0
            comment1_cache = CommentCache(comment1.id)
            assert comment1_cache.read()['up_count'] == '0'
            assert comment1_cache.read()['down_count'] == '0'

            # 测试异常取消点赞
            undecorated(CommentVoteResource.post)(CommentVoteResource, comment2.id, **{"vote": 1})
            undecorated(CommentVoteResource.delete)(CommentVoteResource, comment2.id, **{"vote": -1})
            cvs = CommentVote.query.filter(
                CommentVote.comment_id == comment2.id, CommentVote.user_id == DEFAULT_USER_ID
            ).all()
            assert len(cvs) == 1
            cv = cvs[0]
            assert cv.vote == 1
            comment2_cache = CommentCache(comment2.id)
            assert comment2_cache.read()['up_count'] == '1'
            assert comment2_cache.read()['down_count'] == '0'

            # 测试异常取消点踩
            undecorated(CommentVoteResource.post)(CommentVoteResource, comment2.id, **{"vote": -1})
            undecorated(CommentVoteResource.delete)(CommentVoteResource, comment2.id, **{"vote": 1})
            cvs = CommentVote.query.filter(
                CommentVote.comment_id == comment2.id, CommentVote.user_id == DEFAULT_USER_ID
            ).all()
            assert len(cvs) == 1
            cv = cvs[0]
            assert cv.vote == -1
            comment2_cache = CommentCache(comment2.id)
            assert comment2_cache.read()['up_count'] == '0'
            assert comment2_cache.read()['down_count'] == '1'

    @pytest.mark.usefixtures('patch_celery')
    def test_vote_task(self, tcontext):
        with tcontext:
            from app.api.v1.comment import CommentVoteResource, CommentVote, CommentVoteManager
            from app.schedules.comment_schedule import process_comment_votes
            from app.cache.comment_cache import ProcessCommentVoteCache, CommentCache, CommentListCache
            from app.models.comment import db
            from app.utils.date_ import now

            comment_list = CommentListCache(
                business=str(self.business),
                business_id=self.business_id,
                lang=Language.EN_US,
                sort_type=Comment.SortType.NEW,
            )

            # 初始化数据
            comment1 = self._new_comment()
            comment1_reply = self._new_comment_reply(comment1.id)
            sleep(2)
            comment1_reply2 = self._new_comment_reply(comment1.id)
            comment2 = self._new_comment()
            _now = now()

            # 测试单条评论定时任务更新
            CommentVoteManager.add_vote(comment1.id, 10, 1)
            CommentVoteManager.add_vote(comment1.id, 11, 1)
            CommentVoteManager.add_vote(comment1.id, 12, -1)

            sleep(1)
            process_comment_votes()
            db.session.flush(comment1)
            # assert comment1.hot_score == 2
            assert comment1.top_score == 1
            assert comment1.up_count == 2
            assert comment1.down_count == 1
            assert ProcessCommentVoteCache().get_last_evaluation() > _now.timestamp()

            comment1_cache = CommentCache(comment1.id)
            assert comment1_cache.get_vote_count() == 1

            comment_list = CommentListCache(
                business=str(self.business),
                business_id=self.business_id,
                lang=Language.EN_US,
                sort_type=Comment.SortType.NEW,
            )
            assert len(comment_list.paginate()) == 2
            assert comment_list.paginate()[0] == comment2.id

            # 测试定时任务查询周期选择
            CommentVoteManager.add_vote(comment2.id, 10, 1)
            CommentVoteManager.add_vote(comment2.id, 11, -1)
            CommentVoteManager.add_vote(comment2.id, 12, -1)

            sleep(1)
            now2 = now()
            ProcessCommentVoteCache().save_evaluation_time(int(now2.timestamp()))
            sleep(1)
            process_comment_votes()
            db.session.flush(comment2)

            ## 未更新到comment2
            assert comment2.hot_score == 0
            assert comment2.top_score == 0
            assert comment2.up_count == 0
            assert comment2.down_count == 0
            assert ProcessCommentVoteCache().get_last_evaluation() > now2.timestamp()

            # 测试多条评论定时任务更新
            ProcessCommentVoteCache().save_evaluation_time(int(_now.timestamp()))
            process_comment_votes()
            db.session.flush(comment2)

            ## 更新到comment2
            assert comment2.hot_score < comment1.hot_score
            assert comment2.top_score == -1
            assert comment2.up_count == 1
            assert comment2.down_count == 2
            assert ProcessCommentVoteCache().get_last_evaluation() > now2.timestamp()

            comment2_cache = CommentCache(comment2.id)
            assert comment2_cache.get_vote_count() == -1

            comment_list = CommentListCache(
                business=str(self.business),
                business_id=self.business_id,
                lang=Language.EN_US,
                sort_type=Comment.SortType.NEW,
            )
            assert len(comment_list.paginate()) == 2
            assert comment_list.paginate()[0] == comment2.id

            comment_list = CommentListCache(
                business=str(self.business),
                business_id=self.business_id,
                lang=Language.EN_US,
                sort_type=Comment.SortType.HOT,
            )
            assert len(comment_list.paginate()) == 2
            assert comment_list.paginate()[0] == comment1.id

            comment_list = CommentListCache(
                business=str(self.business),
                business_id=self.business_id,
                lang=Language.EN_US,
                sort_type=Comment.SortType.TOP,
            )
            assert len(comment_list.paginate()) == 2
            assert comment_list.paginate()[0] == comment1.id

            # 测试单条回复定时任务更新
            CommentVoteManager.add_vote(comment1_reply.id, 10, 1)
            CommentVoteManager.add_vote(comment1_reply.id, 11, 1)
            CommentVoteManager.add_vote(comment1_reply.id, 12, 1)
            CommentVoteManager.add_vote(comment1_reply.id, 14, 1)
            process_comment_votes()
            db.session.flush(comment1_reply)

            assert comment1_reply.hot_score == 0
            assert comment1_reply.top_score == 4
            assert comment1_reply.up_count == 4
            assert comment1_reply.down_count == 0

            comment_reply_list = CommentListCache(
                business=str(self.business),
                business_id=self.business_id,
                sort_type=Comment.SortType.TOP,
                lang=Language.EN_US,
                root_id=str(comment1.id),
            )
            assert len(comment_reply_list.paginate()) == 1
            assert comment_reply_list.paginate()[0] == comment1_reply.id

            # 测试多条回复定时任务更新
            CommentVoteManager.add_vote(comment1_reply2.id, 10, 1)
            CommentVoteManager.add_vote(comment1_reply2.id, 11, 1)
            CommentVoteManager.add_vote(comment1_reply2.id, 12, 1)

            process_comment_votes()
            db.session.flush(comment1_reply2)
            assert comment1_reply2.hot_score == 0
            assert comment1_reply2.top_score == 3
            assert comment1_reply2.up_count == 3
            assert comment1_reply2.down_count == 0

            comment_reply_list = CommentListCache(
                business=str(self.business),
                business_id=self.business_id,
                sort_type=Comment.SortType.NEW,
                lang=Language.EN_US,
                root_id=str(comment1.id),
            )
            assert len(comment_reply_list.paginate()) == 0  # 回复没有new排序

            comment_reply_list = CommentListCache(
                business=str(self.business),
                business_id=self.business_id,
                sort_type=Comment.SortType.TOP,
                lang=Language.EN_US,
                root_id=str(comment1.id),
            )
            assert len(comment_reply_list.paginate()) == 2
            assert comment_reply_list.paginate()[0] == comment1_reply.id

            comment_reply_list = CommentListCache(
                business=str(self.business),
                business_id=self.business_id,
                sort_type=Comment.SortType.HOT,
                lang=Language.EN_US,
                root_id=str(comment1.id),
            )
            assert len(comment_reply_list.paginate()) == 0  # 回复没有hot排序

    def test_vote_count(self, tcontext):
        with tcontext:
            from app.api.v1.comment import CommentVoteCountResource
            from app.models.statistics import CommentUserStatistics
            from app.models import db
            # 初始化数据
            up_count = 100
            db.session.add(CommentUserStatistics(
                user_id=g.user_id,
                up_count=up_count,
            ))
            db.session.commit()
            # 测用户总点赞数接口
            res = undecorated(CommentVoteCountResource.get)(CommentVoteCountResource)
            assert res['UP'] == up_count

