import pytest
from flask import g
from undecorated import undecorated

from app.common.constants import Language
from app.models.comment import Comment
from tests.common.mock_sqlalchemy import patch_sqlalchemy
from tests.common.mock_redis import patch_redis
from tests.common.patch_celery import patch_celery
from tests.common.t_common import default_lang

DEFAULT_USER_ID = 2


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.user_id = DEFAULT_USER_ID
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentMessage:
    business = Comment.Business.COIN
    business_id = "1"
    business_code = "BTC"
    content = "test content"
    reply_content = "test reply content"

    def _new_comment(self, user_id):
        from app.models import db
        from app.business.comment_manager import CommentManager

        comment = CommentManager.create_comment(
            user_id=user_id,
            content=self.content,
            lang=Language.EN_US,
            business=self.business,
            business_id=self.business_id,
            business_code=str(self.business_code),
        )
        db.session.commit()
        return comment

    def _new_comment_reply(self, parent_id, user_id):
        from app.models import db
        from app.business.comment_manager import CommentManager

        comment_reply = CommentManager.create_comment(
            root_id=parent_id,
            parent_id=parent_id,
            content=self.reply_content,
            lang=Language.EN_US,
            user_id=user_id,
            business=self.business,
            business_id=self.business_id,
            business_code=str(self.business_code),
        )
        db.session.commit()
        return comment_reply

    def _create_test_data(self, at_user=False):
        from app.business.user import UserManager
        from app.business.comment_message import CommentEventManager
        from app.models.event import CommentEvent

        comment1 = self._new_comment(3)
        comment1_reply = self._new_comment_reply(comment1.id, 4)
        comment1_reply2 = self._new_comment_reply(comment1.id, 3)

        UserManager.update_user(3, "user_name3", "account_name3", "avatar_3")
        UserManager.update_user(4, "user_name4", "account_name4", "avatar_4")
        UserManager.update_user(5, "user_name5", "account_name5", "avatar_5")
        UserManager.update_user(6, "user_name6", "account_name6", "avatar_6")
        UserManager.update_user(7, "user_name7", "account_name7", "avatar_7")
        UserManager.update_user(8, "user_name8", "account_name8", "avatar_8")

        ## comment1 event时间线
        ### 3 new 3
        # CommentEventManager.new_comment_event(
        #     comment1.id, 3, 3, Language.EN_US, CommentEvent.EventType.COMMENT
        # )
        ### 3 @ 3
        CommentEventManager.new_comment_event(
            comment1.id, 3, 3, Language.EN_US, CommentEvent.EventType.AT
        )
        ### 3 @ 4
        CommentEventManager.new_comment_event(
            comment1.id, 3, 4, Language.EN_US, CommentEvent.EventType.AT
        )
        ### 3 @ 5
        CommentEventManager.new_comment_event(
            comment1.id, 3, 5, Language.EN_US, CommentEvent.EventType.AT
        )
        ### 3 up 3
        CommentEventManager.new_comment_event(
            comment1.id, 3, 3, Language.EN_US, CommentEvent.EventType.UP
        )
        ### 4 up 3
        CommentEventManager.new_comment_event(
            comment1.id, 4, 3, Language.EN_US, CommentEvent.EventType.UP
        )
        ### 5 down 3
        CommentEventManager.new_comment_event(
            comment1.id, 5, 3, Language.EN_US, CommentEvent.EventType.DOWN
        )

        if at_user:
            CommentEventManager.new_comment_event(
                comment1.id, 6, 3, Language.EN_US, CommentEvent.EventType.DOWN
            )
            CommentEventManager.new_comment_event(
                comment1.id, 7, 3, Language.EN_US, CommentEvent.EventType.AT
            )
            CommentEventManager.new_comment_event(
                comment1.id, 3, 8, Language.EN_US, CommentEvent.EventType.AT
            )

        ## comment1_reply event时间线
        ### 4 reply 3
        # CommentEventManager.new_comment_event(
        #     comment1_reply.id, 4, 3, Language.EN_US, CommentEvent.EventType.REPLY
        # )
        ### 4 @ 3
        CommentEventManager.new_comment_event(
            comment1_reply.id, 4, 3, Language.EN_US, CommentEvent.EventType.AT
        )
        ### 4 @ 5
        CommentEventManager.new_comment_event(
            comment1_reply.id, 4, 5, Language.EN_US, CommentEvent.EventType.AT
        )
        ### 5 up 4
        CommentEventManager.new_comment_event(
            comment1_reply.id, 5, 4, Language.EN_US, CommentEvent.EventType.UP
        )
        ### 3 down 4
        CommentEventManager.new_comment_event(
            comment1_reply.id, 3, 4, Language.EN_US, CommentEvent.EventType.DOWN
        )

        ## comment1_reply2 event时间线
        ### 3 reply 3
        # CommentEventManager.new_comment_event(
        #     comment1_reply2.id, 3, 3, Language.EN_US, CommentEvent.EventType.REPLY
        # )

        return comment1, comment1_reply, comment1_reply2


    def test_get_message(self, tcontext):
        with (tcontext):
            from app.api.v1.comment_message import (
                CommentMessageResource, CommentMessageMarkReadResource,
                CommentMessageMarkAllReadResource, CommentMessageUnreadCountResource,
            )

            # 初始化数据
            self._create_test_data()

            ## 多用户视角数据
            ### 3
            g.user_id = 3
            res = undecorated(CommentMessageResource.get)(CommentMessageResource)
            # print('check auth', res)

            assert len(res) == 8
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_source": "SELF"})
            assert len(res) == 4
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_source": "OTHERS"})
            assert len(res) == 4

            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "COMMENT"})
            assert len(res) == 1
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "UP"})
            assert len(res) == 2
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "DOWN"})
            assert len(res) == 2
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "AT"})
            assert len(res) == 1
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "REPLY"})
            assert len(res) == 2

            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"read_status": "UNREAD"})
            assert len(res) == 4
            message_ids = [msg['message_id'] for msg in res]
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"read_status": "READ"})
            assert len(res) == 4

            res = undecorated(CommentMessageUnreadCountResource.get)(CommentMessageResource)
            assert res['unread_count'] == 4
            res = undecorated(CommentMessageMarkReadResource.post)(
                CommentMessageResource, **{"messages_ids": message_ids[0:3]}
            )
            assert res['updated_count'] == 3
            res = undecorated(CommentMessageUnreadCountResource.get)(CommentMessageResource)
            assert res['unread_count'] == 1
            res = undecorated(CommentMessageMarkAllReadResource.post)(CommentMessageMarkAllReadResource)
            assert res['updated_count'] == 1
            res = undecorated(CommentMessageUnreadCountResource.get)(CommentMessageResource)
            assert res['unread_count'] == 0

            ### 4
            g.user_id = 4
            res = undecorated(CommentMessageResource.get)(CommentMessageResource)
            assert len(res) == 5
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_source": "SELF"})
            assert len(res) == 2
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_source": "OTHERS"})
            assert len(res) == 3

            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "COMMENT"})
            assert len(res) == 0
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "UP"})
            assert len(res) == 2
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "DOWN"})
            assert len(res) == 1
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "AT"})
            assert len(res) == 1
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "REPLY"})
            assert len(res) == 1

            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"read_status": "UNREAD"})
            assert len(res) == 3
            message_ids = [msg['message_id'] for msg in res]
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"read_status": "READ"})
            assert len(res) == 2

            res = undecorated(CommentMessageUnreadCountResource.get)(CommentMessageResource)
            assert res['unread_count'] == 3
            res = undecorated(CommentMessageMarkReadResource.post)(
                CommentMessageResource, **{"messages_ids": message_ids[0:2]}
            )
            assert res['updated_count'] == 2
            res = undecorated(CommentMessageUnreadCountResource.get)(CommentMessageResource)
            assert res['unread_count'] == 1
            res = undecorated(CommentMessageMarkAllReadResource.post)(CommentMessageMarkAllReadResource)
            assert res['updated_count'] == 1
            res = undecorated(CommentMessageUnreadCountResource.get)(CommentMessageResource)
            assert res['unread_count'] == 0

            ### 5
            g.user_id = 5
            res = undecorated(CommentMessageResource.get)(CommentMessageResource)
            assert len(res) == 4
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_source": "SELF"})
            assert len(res) == 2
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_source": "OTHERS"})
            assert len(res) == 2

            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "COMMENT"})
            assert len(res) == 0
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "UP"})
            assert len(res) == 1
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "DOWN"})
            assert len(res) == 1
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "AT"})
            assert len(res) == 2
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"message_type": "REPLY"})
            assert len(res) == 0

            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"read_status": "UNREAD"})
            assert len(res) == 2
            message_ids = [msg['message_id'] for msg in res]
            res = undecorated(CommentMessageResource.get)(CommentMessageResource, **{"read_status": "READ"})
            assert len(res) == 2

            res = undecorated(CommentMessageUnreadCountResource.get)(CommentMessageResource)
            assert res['unread_count'] == 2
            res = undecorated(CommentMessageMarkReadResource.post)(
                CommentMessageResource, **{"messages_ids": message_ids[0:1]}
            )
            assert res['updated_count'] == 1
            res = undecorated(CommentMessageUnreadCountResource.get)(CommentMessageResource)
            assert res['unread_count'] == 1
            res = undecorated(CommentMessageMarkAllReadResource.post)(CommentMessageMarkAllReadResource)
            assert res['updated_count'] == 1
            res = undecorated(CommentMessageUnreadCountResource.get)(CommentMessageResource)
            assert res['unread_count'] == 0

    @pytest.mark.usefixtures('patch_celery')
    def test_message_push_task(self, tcontext):
        with tcontext:
            from app.schedules.comment_schedule import push_comment_interaction_info
            # 测试触达任务
            self._create_test_data()

            # push_comment_interaction_info()

    def test_at_users(self, tcontext):
        with tcontext:
            from app.api.v1.comment import AtUserResource

            # 测试最近互动列表
            self._create_test_data(True)

            ## 多用户视角数据
            ### 3
            g.user_id = 3
            res = undecorated(AtUserResource.get)(AtUserResource)
            assert len(res) == 4
            relate_user_ids = [i['user_id'] for i in res]
            assert 4 in relate_user_ids
            assert 5 in relate_user_ids
            assert 6 not in relate_user_ids
            assert 7 in relate_user_ids
            assert 8 in relate_user_ids

            ### 4
            g.user_id = 4
            res = undecorated(AtUserResource.get)(AtUserResource)
            assert len(res) == 2
            relate_user_ids = [i['user_id'] for i in res]
            assert 3 in relate_user_ids
            assert 5 in relate_user_ids

            ### 5
            g.user_id = 5
            res = undecorated(AtUserResource.get)(AtUserResource)
            assert len(res) == 2
            relate_user_ids = [i['user_id'] for i in res]
            assert 4 in relate_user_ids
            assert 3 in relate_user_ids

    def test_get_message_via_api(self, tcontext, tapp):
        """使用 test_client 测试获取互动消息的 API"""
        with tcontext:
            # 2. 创建测试客户端
            client = tapp.test_client()
            user_id = 3
            headers = {
                'User-Id': str(user_id),
                'User-Name': 'Test User',
                'Account-Name': 'test_account',
                'Avatar': 'test_avatar',
            }

            # 3. 测试获取消息列表
            response = client.get('/res/comment-message/message-list', headers=headers,
                                  query_string={
                                      'end_time': **********,
                                  })
            assert response.status_code == 200
            result = response.get_json()
            assert result['code'] == 0
            messages = result['data']
            assert isinstance(messages, list)



