import pytest
from flask import g
from undecorated import undecorated

from tests.common.mock_sqlalchemy import patch_sqlalchemy
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang
from app.common.constants import Language
from app.models.comment import Comment


DEFAULT_USER_ID = 23167


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            from app.models import UserInfo, db
            from sqlalchemy import func
            random_user = db.session.query(UserInfo.user_id).order_by(func.random()).first()
            g.user_id = random_user.user_id if random_user else DEFAULT_USER_ID  # 如果没有用户记录,使用默认值
            g.lang = default_lang
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentRootResource:
    business = Comment.Business.COIN
    business_id = "1"
    business_code = "BTC"
    content = "test root comment content"

    @staticmethod
    def _verify_comment_format(comment):
        """Helper method to verify comment format"""
        required_fields = [
            'id', 'content', 'created_at', 'created_by',
            'reply_count', 'score', 'is_voted'
        ]
        for field in required_fields:
            assert field in comment

    @staticmethod
    def _create_root_comment(business, business_id, business_code, content):
        """
        创建一个根评论

        Args:
            business: 业务类型
            business_id: 业务 ID
            business_code: 业务代码
            content: 评论内容

        Returns:
            Comment: 新创建的评论对象
        """
        from app.models import db
        from app.business.comment_manager import CommentManager

        # 使用 CommentManager 创建评论，让数据库自动生成 ID
        comment = CommentManager.create_comment(
            business=business,
            business_id=business_id,
            business_code=business_code,
            content=content,
            lang=Language.EN_US,
            user_id=g.user_id
        )

        db.session.commit()
        return comment

    @staticmethod
    def _create_mock_reply(root_id, parent_id, content):
        """创建一个回复评论"""
        from app.models import db
        from app.business.comment_manager import CommentManager

        reply = CommentManager.create_comment(
            root_id=root_id,
            parent_id=parent_id,
            content=content,
            lang=Language.EN_US,
            user_id=g.user_id,
        )
        db.session.commit()
        return reply

    @staticmethod
    def _call_get_root_comment(**kwargs):
        """调用 CommentRootResource.get 方法"""
        from app.api.v1.comment import CommentRootResource
        return undecorated(CommentRootResource.get)(CommentRootResource, **kwargs)

    def test_get_root_comment_direct(self, tcontext):
        """测试直接获取一级评论详情"""
        with tcontext:
            # 创建一个一级评论
            root_comment = self._create_root_comment(
                business=self.business,
                business_id=self.business_id,
                business_code=self.business_code,
                content="This is a root comment for testing"
            )

            # 调用接口获取一级评论详情
            res = self._call_get_root_comment(
                comment_id=root_comment.id
            )

            # 验证返回结果
            assert res is not None
            self._verify_comment_format(res)
            assert res['id'] == root_comment.id
            assert res['content'] == root_comment.content

    def test_get_root_comment_from_reply(self, tcontext):
        """测试通过回复获取对应的一级评论详情"""
        with tcontext:
            # 创建一个一级评论
            root_comment = self._create_root_comment(
                business=self.business,
                business_id=self.business_id,
                business_code=self.business_code,
                content="This is a root comment for testing replies"
            )

            # 创建一个回复
            reply = self._create_mock_reply(
                root_id=root_comment.id,
                parent_id=root_comment.id,
                content="This is a reply to the root comment"
            )

            # 通过回复ID获取一级评论详情
            res = self._call_get_root_comment(
                comment_id=reply.id
            )

            # 验证返回结果
            assert res is not None
            self._verify_comment_format(res)
            assert res['id'] == root_comment.id  # 返回的应该是根评论的ID
            assert res['content'] == root_comment.content

    def test_get_root_comment_nonexistent(self, tcontext):
        """测试获取不存在的评论"""
        with tcontext:
            from app.exceptions import CommentNotFound

            # 尝试获取一个不存在的评论ID
            nonexistent_id = 99999999

            # 验证会抛出 CommentNotFound 异常
            with pytest.raises(CommentNotFound):
                self._call_get_root_comment(
                    comment_id=nonexistent_id
                )
