import pytest
from flask import g
from app.common.constants import Language
from app.models import UserInfo, db
from app.models.comment import Comment
from tests.common.mock_sqlalchemy import patch_sqlalchemy
from tests.common.mock_redis import patch_redis

DEFAULT_USER_ID = 2


@pytest.fixture(scope='module')
def module_setup(tcontext):
    """设置测试环境"""
    try:
        with tcontext:
            # 预先创建测试用户信息
            user_info = UserInfo(
                user_id=DEFAULT_USER_ID,
                name=f"Test_User_{DEFAULT_USER_ID}",
                account_name=f"test_user_{DEFAULT_USER_ID}",
                avatar="test_avatar.jpg"
            )
            db.session.add(user_info)
            db.session.commit()

            # 设置全局用户ID
            g.user_id = DEFAULT_USER_ID
            yield tcontext
    finally:
        with tcontext:
            # 清理测试数据
            UserInfo.query.filter_by(user_id=DEFAULT_USER_ID).delete()
            db.session.commit()


@pytest.fixture(autouse=True)
def mock_update_user(monkeypatch):
    def mock_update():
        pass

    monkeypatch.setattr('app.business.user.UserManager.update_user_from_request', mock_update)


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentTranslateResource:
    """测试评论翻译相关功能"""

    @staticmethod
    def _create_test_comment(content, lang=Language.ZH_HANS_CN):
        """创建测试评论的辅助方法"""
        from app.models import db
        from app.business.comment_manager import CommentManager

        comment = CommentManager.create_comment(
            business=Comment.Business.COIN,
            business_id="1",
            business_code="BTC",
            content=content,
            lang=lang,
            user_id=DEFAULT_USER_ID
        )
        db.session.commit()
        return comment

    def test_translate_comment(self, tcontext, tapp):
        """测试评论翻译功能"""
        with tcontext:
            # 1. 创建一个中文评论
            zh_content = "这是一个测试评论"
            comment = self._create_test_comment(zh_content, Language.ZH_HANS_CN)

            # 2. 请求翻译接口
            client = tapp.test_client()
            headers = {
                'User-Id': str(DEFAULT_USER_ID),
                'Content-Type': 'application/json'
            }

            response = client.post(
                f'/res/comment/{comment.id}/translate',
                json={'target': Language.EN_US.name},
                headers=headers
            )

            # 3. 验证翻译结果
            assert response.status_code == 200
            result = response.get_json()
            print(result)
            assert 'data' in result
            assert 'content' in result['data']
            translated = result['data']['content']
            assert translated != zh_content  # 确保内容被翻译
            assert isinstance(translated, str)
            assert len(translated) > 0
            print(f'translated: {translated}')

    def test_show_translate_button(self, tcontext, tapp):
        """测试是否显示翻译按钮的逻辑"""
        with tcontext:
            # 1. 创建一个英文评论
            en_content = "This is a test comment"
            comment = self._create_test_comment(en_content, Language.EN_US)

            # 2. 对于中文用户，应该显示翻译按钮
            client = tapp.test_client()
            response = client.get(
                f'/res/comment/{comment.id}/show-translate',
                query_string={'user_lang': Language.ZH_HANS_CN.name},
                headers={'User-Id': DEFAULT_USER_ID}
            )

            assert response.status_code == 200
            result = response.get_json()
            print(f'result: {result}')
            assert 'data' in result
            assert result['data']['show_translate'] is True

            # 3. 对于英文用户，不应该显示翻译按钮
            response = client.get(
                f'/res/comment/{comment.id}/show-translate',
                query_string={'user_lang': Language.EN_US.name},
                headers={'User-Id': DEFAULT_USER_ID}
            )
            assert response.status_code == 200
            result = response.get_json()
            assert 'data' in result
            assert result['data']['show_translate'] is False

    def test_translate_non_existent_comment(self, tcontext, tapp):
        """测试翻译不存在的评论"""
        with tcontext:
            client = tapp.test_client()
            response = client.post(
                '/res/comment/99999/translate',
                json={'target': Language.EN_US.name},
                headers={'User-Id': DEFAULT_USER_ID}
            )
            result = response.get_json()

            assert response.status_code == 200  # API 返回 200 但包含错误信息
            assert result['code'] == 2  # 验证错误码
            assert 'message' in result  # 验证包含错误消息
            print(f"Error response: {result}")

def test_translate_multiple_languages(self, tcontext, tapp):
        """测试多语言翻译场景"""
        with tcontext:
            # 创建不同语言的评论
            comments_data = [
                ("这是中文评论", Language.ZH_HANS_CN),
                ("This is English comment", Language.EN_US),
                ("これは日本語のコメントです", Language.JA_JP)
            ]

            client = tapp.test_client()
            for content, source_lang in comments_data:
                comment = self._create_test_comment(content, source_lang)

                # 测试翻译到其他语言
                for target_lang in [lang for lang in [Language.ZH_HANS_CN, Language.EN_US, Language.JA_JP] if
                                    lang != source_lang]:
                    response = client.post(
                        f'/res/comment/{comment.id}/translate',
                        json={'target': target_lang.name},
                        headers={'User-Id': DEFAULT_USER_ID}
                    )

                    assert response.status_code == 200
                    result = response.get_json()
                    assert 'data' in result
                    assert 'comment' in result['data']
                    translated = result['data']['comment']
                    print(f'translated: {translated}')
                    assert translated != content  # 确保内容被翻译
