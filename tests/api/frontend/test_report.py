from time import sleep

import pytest
from flask import g
from undecorated import undecorated

from app.common.constants import Language
from app.models.comment import Comment
from tests.common.mock_sqlalchemy import patch_sqlalchemy
from tests.common.mock_redis import patch_redis
from tests.common.patch_celery import patch_celery
from tests.common.t_common import default_lang

DEFAULT_USER_ID = 2


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.user_id = DEFAULT_USER_ID
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentReport:
    business = Comment.Business.COIN
    business_id = "1"
    business_code = "BTC"
    content = "test reply content"
    root_comment_id = 2  # 添加父评论ID

    def _new_comment(self):
        from app.models import db
        from app.business.comment_manager import CommentManager

        comment = CommentManager.create_comment(
            user_id=3,
            content=self.content,
            lang=Language.EN_US,
            business=self.business,
            business_id=self.business_id,
            business_code=str(self.business_code),
        )
        db.session.commit()
        return comment

    def test_report_type(self, tcontext):
        with tcontext:
            from app.api.v1.comment import CommentReportTypesResource
            from app.business.moderation_manager import CommentReportManager

            # 测试获取举报类型
            res = undecorated(CommentReportTypesResource.get)(CommentReportTypesResource)
            assert len(res) == len(CommentReportManager.report_type_dict)

    def test_report(self, tcontext):
        with tcontext:
            from app.api.v1.comment import CommentReportResource
            from app.models.moderation import CommentReport

            comment = self._new_comment()
            reason = "haha"

            # 测试举报
            undecorated(CommentReportResource.post)(CommentReportResource, comment.id, **{
                "type": CommentReport.Type.FAKE.name,
                "reason": reason,
            })

            crs = CommentReport.query.filter(
                CommentReport.comment_id == comment.id, CommentReport.user_id == g.user_id,
                CommentReport.type == CommentReport.Type.FAKE, CommentReport.reason == reason
            ).all()

            assert len(crs) == 1

            # 测试重复举报
            undecorated(CommentReportResource.post)(CommentReportResource, comment.id, **{
                "type": CommentReport.Type.FAKE.name,
                "reason": reason,
            })

            crs = CommentReport.query.filter(
                CommentReport.comment_id == comment.id, CommentReport.user_id == g.user_id,
                CommentReport.type == CommentReport.Type.FAKE, CommentReport.reason == reason
            ).all()

            assert len(crs) == 2

    @pytest.mark.usefixtures('patch_celery')
    def test_report_task(self, tcontext):
        with tcontext:
            from app.api.v1.comment import CommentReportManager
            from app.models.moderation import CommentReport, CommentReportReview
            from app.models import db
            from app.schedules.comment_schedule import process_comment_report

            comment = self._new_comment()
            reason = "haha"

            # 小于10次举报不生成审批
            for i in range(9):
                CommentReportManager.new_report(
                    comment.id, i + 1, CommentReport.Type.FAKE, reason
                )

            db.session.commit()

            process_comment_report()

            db.session.refresh(comment)
            assert comment.report_count == 9

            crrs = CommentReportReview.query.filter(
                CommentReportReview.comment_id == comment.id
            ).all()
            assert len(crrs) == 0

            sleep(1)

            # 10次举报生成审批
            CommentReportManager.new_report(
                comment.id, i + 2, CommentReport.Type.FAKE, reason
            )

            process_comment_report()

            db.session.refresh(comment)
            assert comment.report_count == 10

            crrs = CommentReportReview.query.filter(
                CommentReportReview.comment_id == comment.id
            ).all()
            assert len(crrs) == 1

            sleep(1)

            CommentReportManager.new_report(
                comment.id, i + 3, CommentReport.Type.FAKE, reason
            )
            # 多次执行不生产审批
            process_comment_report()

            db.session.refresh(comment)
            assert comment.report_count == 11

            crrs = CommentReportReview.query.filter(
                CommentReportReview.comment_id == comment.id
            ).all()
            assert len(crrs) == 1
