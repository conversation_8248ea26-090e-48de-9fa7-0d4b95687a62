import pytest
from flask import g
from undecorated import undecorated

from tests.common.mock_sqlalchemy import patch_sqlalchemy
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang
from app.common.constants import Language
from app.models.comment import Comment
from sqlalchemy import select, exists, func


DEFAULT_USER_ID = 23167

@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            from app.models import UserInfo, db
            random_user = db.session.query(UserInfo.user_id).order_by(func.random()).first()
            g.user_id = random_user.user_id if random_user else DEFAULT_USER_ID  # 如果没有用户记录,使用默认值1
            g.lang = default_lang
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentReplyListResource:
    business = Comment.Business.COIN
    business_id = "161"
    business_code = "BTC"
    content = "test reply content"
    root_comment_id = 2  # 添加父评论ID

    @staticmethod
    def _verify_reply_format(reply):
        """Helper method to verify reply format"""
        required_fields = [
            'id', 'content', 'created_at', 'created_by',
            'reply_count', 'score', 'is_voted', 'parent_id', 'parent_user'
        ]
        for field in required_fields:
            assert field in reply

    @staticmethod
    def _ensure_comment_exists(comment_id, business, business_id, business_code, content):
        """
        确保指定 ID 的评论存在，如果不存在则创建
        
        Args:
            comment_id: 需要确保存在的评论 ID
            business: 业务类型
            business_id: 业务 ID
            business_code: 业务代码
            content: 评论内容
            
        Returns:
            Comment: 已存在或新创建的评论对象
        """
        from app.models import db
        from app.models.comment import Comment
        from app.business.comment_manager import CommentManager
        
        # 先查找是否存在指定 ID 的评论
        existing_comment = Comment.query.get(comment_id)
        if existing_comment:
            return existing_comment
            
        # 不存在则创建新评论
        # 手动设置 ID
        comment = Comment(
            id=comment_id,
            business=business,
            business_id=business_id,
            business_code=business_code,
            content=content,
            lang=Language.EN_US,
            user_id=g.user_id
        )
        
        try:
            db.session.add(comment)
            db.session.flush()  # 检查是否有 ID 冲突
            db.session.commit()
            return comment
        except Exception as e:
            db.session.rollback()
            raise e
        
    @staticmethod
    def _create_mock_reply(parent_id, content):
        from app.models import db
        from app.business.comment_manager import CommentManager
        
        reply = CommentManager.create_comment(
            root_id=TestCommentReplyListResource.root_comment_id,
            parent_id=parent_id,
            content=content,
            lang=Language.ZH_HANS_CN,
            user_id=g.user_id,
        )
        db.session.commit()
        return reply

    @staticmethod
    def _call_get_reply_list(root_id, **kwargs):
        from app.api.v1.comment import CommentRepliesResource
        return undecorated(CommentRepliesResource.get)(CommentRepliesResource, root_id, **kwargs)
        
    def test_get_replies(self, tcontext):
        """测试获取评论回复列表"""
        with tcontext:
            comment = self._ensure_comment_exists(
                comment_id=self.root_comment_id,
                business=self.business,
                business_id=self.business_id,
                business_code=self.business_code,
                content="This is comment to be replied"
            )

            # 创建3条测试回复
            test_replies = []
            for i in range(3):
                reply = self._create_mock_reply(
                    parent_id=self.root_comment_id,
                    content=f"测试回复 {i}"
                )
                test_replies.append(reply)

            # 获取回复列表
            res = self._call_get_reply_list(
                self.root_comment_id,
                lang=Language.ZH_HANS_CN,
                limit=10
            )

            # 验证返回结果
            assert isinstance(res, dict)
            replies = res['replies']
            assert len(replies) >= 3  # 至少包含刚创建的3条回复
            
            # 验证第一条回复的格式
            if res:
                self._verify_reply_format(replies[0])
            
            # 验证新创建的回复都在返回结果中
            returned_ids = [reply['id'] for reply in replies]
            for test_reply in test_replies:
                assert test_reply.id in returned_ids

            # 验证按 top score 倒序排序
            for i in range(len(replies) - 1):
                assert replies[i]['score'] >= replies[i + 1]['score']

    def test_pagination(self, tcontext):
        """测试回复列表分页"""
        with tcontext:
            comment = self._ensure_comment_exists(
                comment_id=self.root_comment_id,
                business=self.business,
                business_id=self.business_id,
                business_code=self.business_code,
                content="This is comment to be replied"
            )

            test_replies = []
            for i in range(3):
                reply = self._create_mock_reply(
                    parent_id=self.root_comment_id,
                    content=f"测试回复 {i}"
                )
                test_replies.append(reply)
                
            first_page = self._call_get_reply_list(
                self.root_comment_id,
                lang=Language.ZH_HANS_CN,
                limit=1
            )
            assert isinstance(first_page['replies'], list)
            assert len(first_page) >= 1

            if first_page:
                last_reply = first_page['replies'][-1]
                second_page = self._call_get_reply_list(
                    self.root_comment_id,
                    lang=Language.ZH_HANS_CN,
                    last_score=last_reply['score'],
                    last_id=last_reply['id'],
                    limit=1
                )
                assert isinstance(second_page['replies'], list)
                assert len(second_page['replies']) <= 1  # 如果本来没有回复，第2页就应该返回空列表

    def test_get_replies_online(self, tcontext):
        """临时测试获取评论回复列表"""
        pass
        # with tcontext:
        #     # 获取回复列表
        #     g.user_id = 2
        #     g.lang = Language.ZH_HANS_CN
        #     res = self._call_get_reply_list(
        #         6908,
        #         # last_score=float('5.415e-7'),
        #         # last_id=1083,
        #         # limit=2
        #     )

        #     # 验证返回结果
        #     assert isinstance(res, dict)
        #     replies = res['replies']
        #     assert isinstance(replies, list)
            
        #     # 验证第一条回复的格式
        #     if res and len(res) > 0:
        #         self._verify_reply_format(replies[0])
            
