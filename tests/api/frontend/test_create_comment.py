import pytest
from flask import g
from undecorated import undecorated
from sqlalchemy import select, exists, func

from tests.common.mock_sqlalchemy import patch_sqlalchemy
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang
from app.common.constants import Language
from app.models.comment import Comment



DEFAULT_USER_ID = 2

@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            from app.models import UserInfo, db
            random_user = db.session.query(UserInfo.user_id).order_by(func.random()).first()
            g.user_id = random_user.user_id if random_user else DEFAULT_USER_ID  # 如果没有用户记录,使用默认值1
            g.lang = default_lang
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentCreateResource:
    business = Comment.Business.COIN

    @staticmethod
    def _generate_random_content(lang=Language.ZH_HANS_CN):
        """生成随机评论内容"""
        import random
        comments_en = [
            "This coin has been performing well 👍",
            "The technical chart looks strong",
            "I recommend keeping an eye on this project",
            "The team has a strong background, worth watching",
            "The volatility is a bit high, need to be cautious",
            "Long-term",
        ]
        comments_zh = [
            "这个币种最近表现不错 👍",
            "技术面看起来很强势",
            "建议大家关注一下这个项目",
            "团队背景很硬，值得期待",
            "波动有点大，需要谨慎",
            "长期看好这个方向"
        ]
        if lang == Language.ZH_HANS_CN:
            return random.choice(comments_zh)
        else:
            return random.choice(comments_en)

    @staticmethod
    def _create_test_comment(business_code, business_id="1", lang=Language.ZH_HANS_CN, user_id=None):
        """创建测试评论的辅助方法"""
        from app.models import db
        from app.business.comment_manager import CommentManager
        
        content = TestCommentCreateResource._generate_random_content(lang)
        comment = CommentManager.create_comment(
            business=Comment.Business.COIN,
            business_id=business_id,
            business_code=business_code,
            content=content,
            lang=lang,
            user_id=DEFAULT_USER_ID if not user_id else user_id
        )
        db.session.commit()
        return comment

    def test_create_btc_comments(self, tcontext):
        """测试创建BTC相关评论"""
        with tcontext:
            for _ in range(3):
                comment = self._create_test_comment(business_code="BTC")
                assert comment.business_code == "BTC"
                assert comment.content is not None
                assert len(comment.content) > 0

    def test_create_eth_comments(self, tcontext):
        """测试创建ETH相关评论"""
        with tcontext:
            for _ in range(2):
                comment = self._create_test_comment(business_code="ETH")
                assert comment.business_code == "ETH"
                assert comment.content is not None
                assert len(comment.content) > 0

    def test_create_multilang_comments(self, tcontext):
        """测试创建多语言评论"""
        with tcontext:
            # 创建中文评论
            zh_comment = self._create_test_comment(
                business_code="BNB",
                lang=Language.ZH_HANS_CN
            )
            assert zh_comment.lang == Language.ZH_HANS_CN

            # 创建英文评论
            en_comment = self._create_test_comment(
                business_code="BNB",
                lang=Language.EN_US
            )
            assert en_comment.lang == Language.EN_US

    @staticmethod
    def _create_test_users():
        """创建测试用户"""
        from app.models import db
        from app.models.user import UserInfo

        users = []
        max_user_id = db.session.query(db.func.max(UserInfo.user_id)).scalar() or 0
        for i in range(2):
            max_user_id += 1
            user = UserInfo(
                user_id = max_user_id + 1,
                account_name=f'account_name_{i}',
                name=f'Nickname {i}',
                avatar=f'test_file_key{i}'
            )
            db.session.add(user)
            users.append(user)
        db.session.commit()
        return users

    def test_create_comment_with_at_users(self, tcontext, tapp):
        from app.models.user import UserInfo
        from app.cache.user import UserInfoCache
        from app.business.comment_manager import CommentManager

        """测试创建带有@用户的评论"""
        with tcontext:
            # 创建测试用户
            test_users = self._create_test_users()
            created_by_user_id = 123
            
            # 准备请求数据
            data = {
                'business': Comment.Business.COIN.name,
                'business_code': 'BTC',
                'business_id': '4',
                'lang': Language.EN_US.name,
                'content': self._generate_random_content(lang=Language.EN_US),
                'at_users': [user.to_dict() for user in test_users]
            }

            # 通过 API 接口创建评论
            client = tapp.test_client()
            response = client.post('/res/comment', json=data, headers={
                    'User-Id': created_by_user_id,
                    'User-Name': 'Test User',
                    'Account-Name': 'test_account',
                    'Avatar': 'test_avatar',
                })
            assert response.status_code == 200
            result = response.get_json()

            assert 'data' in result
            ret_data = result['data']

            # 验证返回的评论数据
            assert 'id' in ret_data
            assert 'created_at' in ret_data
            created_id = ret_data['id']

            assert g.user_id == created_by_user_id
            # 直接使用 CommentManager 获取并验证评论
            comments = CommentManager.get_root_comments(
                business=Comment.Business.COIN,
                business_id='4',
                lang=Language.EN_US,
                sort_type=Comment.SortType.HOT,
                user_id=g.user_id
            )
            
            # 验证能在列表中找到该评论
            found_comment = next(
                (c for c in comments if c.id == created_id),
                None
            )
            assert found_comment is not None
            assert len(found_comment.at_users) == len(test_users)

            # 验证用户信息缓存
            user_ids = [user.user_id for user in test_users]
            user_info_map = UserInfoCache().get_user_info(user_ids)
            for user in test_users:
                user_info_cache = user_info_map.get(user.user_id)
                assert user_info_cache is not None
                user_info_cache['name'] = user.name

                user_info = UserInfo.query.filter_by(user_id=user.user_id).first()
                assert user_info is not None
                user_info.name = user.name

@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentModerationFlow:
    """测试评论创建和初审流程"""
    
    @staticmethod
    def _verify_comment_state(comment_id, expected_status):
        """验证评论状态和缓存"""
        from app.models import db
        from app.models.comment import Comment
        from app.cache.comment_cache import CommentCache, UserPendingCommentsCache, CommentListCache
        
        # 验证数据库状态
        comment = db.session.get(Comment, comment_id)
        assert comment is not None
        assert comment.status == expected_status
        
        # 验证缓存状态
        cached = CommentCache(comment_id)
        cached_comment = cached.get_comment()
        assert cached_comment is not None
        assert cached_comment.status == expected_status
            
        # 根据状态，验证评论是否在用户待审核缓存中
        pending_cache = UserPendingCommentsCache(DEFAULT_USER_ID)
        if expected_status == Comment.CommentStatus.CREATED:
            assert pending_cache.sismember(comment_id)
        else:
            assert not pending_cache.sismember(comment_id)
            
        # 验证评论是否在评论列表缓存中
        for list_cache in CommentListCache.get_instances_from_comment(comment):
            score = list_cache.zscore(str(comment_id))
            if expected_status == Comment.CommentStatus.CREATED:
                assert score is None
            elif expected_status == Comment.CommentStatus.PUBLISHED:
                assert score is not None
            elif expected_status == Comment.CommentStatus.DISABLED:
                assert score is None
            elif expected_status == Comment.CommentStatus.DELETED:
                assert score is None
            
    def test_comment_moderation_flow(self, tcontext):
        """测试评论创建到初审的完整流程"""
        from app.models import db
        from app.business.comment_manager import CommentManager
        from app.models.comment import Comment
        from app.models.moderation import CommentModeration
        from app.schedules.comment_task import initial_moderate
        
        with tcontext:
            # 1. 创建评论
            content = "这是一个测试评论，需要进行初审 #测试"
            comment = CommentManager.create_comment(
                business=Comment.Business.COIN,
                business_id="1",
                business_code="BTC",
                content=content,
                lang=Language.ZH_HANS_CN,
                user_id=DEFAULT_USER_ID
            )
            db.session.commit()
            assert comment.id is not None
                        
            # 验证评论初始状态
            assert comment.status == Comment.CommentStatus.CREATED
            self._verify_comment_state(comment.id, comment.status)
            
            # 2. 执行初审
            moderation = initial_moderate(
                comment_id=comment.id
            )
            
            # 3. 验证初审结果
            assert moderation is not None
            assert moderation.status == CommentModeration.Status.APPROVED
            # 重新查询评论以确保获取最新状态
            comment = db.session.get(Comment, comment.id)
            assert comment.status == Comment.CommentStatus.PUBLISHED                
            self._verify_comment_state(comment.id, comment.status)
            


    def test_comment_moderation_with_sensitive_content(self, tcontext):
        """测试包含敏感内容的评论初审流程"""
        from app.models import db
        from app.business.comment_manager import CommentManager
        from app.models.comment import Comment
        from app.models.moderation import CommentModeration
        from app.schedules.comment_task import initial_moderate
        
        with tcontext:
            # 创建包含敏感内容的评论
            content = "这是我的联系方式：***********"
            comment = CommentManager.create_comment(
                business=Comment.Business.COIN,
                business_id="1",
                business_code="BTC",
                content=content,
                lang=Language.ZH_HANS_CN,
                user_id=DEFAULT_USER_ID
            )
            db.session.commit()
            
            assert comment.id is not None
                        
            # 验证评论初始状态
            assert comment.status == Comment.CommentStatus.CREATED
            self._verify_comment_state(comment.id, comment.status)
            
            # 2. 执行初审
            moderation = initial_moderate(
                comment_id=comment.id
            )
            
            # 3. 验证初审结果
            assert moderation is not None
            assert moderation.status == CommentModeration.Status.REJECTED
            # 重新查询评论以确保获取最新状态
            comment = db.session.get(Comment, comment.id)
            assert comment.status == Comment.CommentStatus.DISABLED                
            self._verify_comment_state(comment.id, comment.status)

@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_sqlalchemy')
class TestCommentDeleteResource:
    """测试评论删除功能"""

    @staticmethod
    def _create_test_comment(user_id=DEFAULT_USER_ID):
        """创建测试评论的辅助方法"""
        from app.models import db
        from app.business.comment_manager import CommentManager
        
        comment = CommentManager.create_comment(
            business=Comment.Business.COIN,
            business_id="1",
            business_code="BTC",
            content="This is a test comment for deletion",
            lang=Language.EN_US,
            user_id=user_id
        )
        db.session.commit()
        return comment

    def test_delete_comment_success(self, tcontext, tapp):
        """测试成功删除评论"""
        with tcontext:
            # 1. 创建测试评论
            comment = self._create_test_comment()
            comment_id = comment.id
            
            # 2. 删除评论
            client = tapp.test_client()
            response = client.delete(f'/res/comment/{comment_id}', headers={
                'User-Id': str(DEFAULT_USER_ID),
                'User-Name': 'Test User',
                'Account-Name': 'test_account',
                'Avatar': 'test_avatar',
            })
            
            # 3. 验证响应
            assert response.status_code == 200
            result = response.get_json()
            assert result['code'] == 0
            
            # 4. 验证评论状态
            from app.models.comment import Comment
            deleted_comment = Comment.query.get(comment_id)
            assert deleted_comment.status == Comment.CommentStatus.DELETED
            
            # 5. 验证缓存状态
            from app.cache.comment_cache import CommentCache, CommentListCache
            comment_cache = CommentCache(comment_id)
            cached_comment = comment_cache.get_comment()
            assert cached_comment is None or cached_comment.status == Comment.CommentStatus.DELETED
            
            # 验证评论是否从列表缓存中移除
            for list_cache in CommentListCache.get_instances_from_comment(deleted_comment):
                score = list_cache.zscore(str(comment_id))
                assert score is None

    def test_delete_comment_unauthorized(self, tcontext, tapp):
        """测试未授权用户删除评论"""
        with tcontext:
            # 1. 创建测试评论
            comment = self._create_test_comment()
            comment_id = comment.id
            
            # 2. 使用不同的用户ID尝试删除
            other_user_id = DEFAULT_USER_ID + 1
            client = tapp.test_client()
            response = client.delete(f'/res/comment/{comment_id}', headers={
                'User-Id': str(other_user_id),
                'User-Name': 'Other User',
                'Account-Name': 'other_account',
                'Avatar': 'other_avatar',
            })
            
            # 3. 验证响应
            assert response.status_code == 200
            result = response.get_json()
            assert result['code'] == 33# 或其他适当的错误码
            
            # 4. 验证评论状态未改变
            from app.models.comment import Comment
            comment = Comment.query.get(comment_id)
            assert comment.status == Comment.CommentStatus.CREATED

    def test_delete_nonexistent_comment(self, tcontext, tapp):
        """测试删除不存在的评论"""
        with tcontext:
            # 使用一个不存在的评论ID
            non_existent_id = 99999
            
            client = tapp.test_client()
            response = client.delete(f'/res/comment/{non_existent_id}', headers={
                'User-Id': str(DEFAULT_USER_ID),
                'User-Name': 'Test User',
                'Account-Name': 'test_account',
                'Avatar': 'test_avatar',
            })
            
            # 验证响应
            assert response.status_code == 200
            result = response.get_json()
            assert result['code'] == 7007

    