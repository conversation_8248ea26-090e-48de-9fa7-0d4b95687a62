#!/usr/bin/python
# -*- coding: utf-8 -*-
import contextlib
import time

from app.common.constants import Language
from app.utils.date_ import current_timestamp
from faker import Faker

from app.models import db
from app.models.comment import User

default_lang = Language.ZH_HANT_HK.value
common_user_agent = "Mozilla/5.0 (Macintosh; " \
             "Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36"
common_header = {
    'Accept-Language': default_lang,
    "platform": "web",
    "User-Agent": common_user_agent,
}

faker = Faker()


def create_user(user_id=None, user_name=None, account_name=None,):
    test_user = User(
        id=user_id,
        user_name=user_name,
        account_name=account_name,
    )
    return test_user


def common_func():
    print('common_func')


class AppFaker(object):
    origin_current_time_code = current_timestamp.__code__
    origin_commit = db.session.commit

    @staticmethod
    def _fake_current():
        return **********

    @staticmethod
    def replace_current_time(func=None):
        if func:
            current_timestamp.__code__ = func.__code__
        else:
            current_timestamp.__code__ = AppFaker._fake_current.__code__

    @staticmethod
    def restore_current_time():
        current_timestamp.__code__ = AppFaker.origin_current_time_code

    @staticmethod
    def replace_commit():
        db.session.commit = db.session.flush

    @staticmethod
    def restore_commit():
        pass


@contextlib.contextmanager
def calc_time(name):
    start = time.time()
    yield
    print(f"{name} use time {round(time.time() - start, 5)}")

