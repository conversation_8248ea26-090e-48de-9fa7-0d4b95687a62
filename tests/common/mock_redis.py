import string
from functools import partial
import random

import pytest
import redis
import fakeredis


@pytest.fixture(scope='function')
def patch_redis():
    from app.cache.base import _BaseCache
    origin_redis = _BaseCache.redis
    _BaseCache.redis = MockRedis(origin_redis)
    yield
    _BaseCache.redis = origin_redis


class MockPipeline(object):
    """Mock Pipeline 类，模拟 Redis pipeline 功能"""
    
    def __init__(self, mock_redis):
        self.mock_redis = mock_redis
        self.commands = []  # 存储待执行的命令
        
    def hgetall(self, name):
        """添加 hgetall 命令到 pipeline"""
        self.commands.append(('hgetall', name))
        return self
        
    def hmget(self, name, fields):
        """添加 hmget 命令到 pipeline"""
        self.commands.append(('hmget', name, fields))
        return self
        
    def get(self, name):
        """添加 get 命令到 pipeline"""
        self.commands.append(('get', name))
        return self
        
    def hget(self, name, key):
        """添加 hget 命令到 pipeline"""
        self.commands.append(('hget', name, key))
        return self
        
    def execute(self):
        """执行所有存储的命令并返回结果"""
        results = []
        for command in self.commands:
            method_name = command[0]
            args = command[1:]
            
            # 获取 MockRedis 对应的方法并调用
            method = getattr(self.mock_redis, method_name)
            result = method(*args)
            results.append(result)
            
        self.commands = []  # 清空命令列表
        return results


class MockRedis(object):

    def __init__(self, origin_redis: redis.StrictRedis):
        self.write_keys = set()
        self.origin_redis = origin_redis
        self.fake_redis = fakeredis.FakeStrictRedis()
        self.data = {}
        
    def pipeline(self):
        """返回一个 MockPipeline 实例"""
        return MockPipeline(self)

    def decr(self, key, amount=1):
        return self.default_write(
            key,
            partial(self.fake_redis.decr, key, amount),
            self.copy_string,
            self.default_copy_ttl,
        )

    def has_write_key(self, _key):
        return _key in self.write_keys

    def set_write_key(self, _key):
        self.write_keys.add(_key)

    def exists_in_origin(self, _key):
        return self.origin_redis.exists(_key)

    def exists_in_fake(self, _key):
        return self.fake_redis.exists(_key)

    def default_read(self, _key, origin_read_func, fake_read_func, args=None, kwargs=None):
        if self.has_write_key(_key):
            read_func = fake_read_func
        else:
            read_func = origin_read_func

        if args:
            return read_func(*args, **(kwargs or {}))
        else:
            return read_func(_key)

    def default_write(self, _key, fake_write_func: partial, copy_data_func, copy_ttl_func=None):
        if not self.has_write_key(_key):
            if self.exists_in_origin(_key):
                copy_data_func(_key)
                if copy_ttl_func is not None:
                    copy_ttl_func(_key)

            self.set_write_key(_key)
        return fake_write_func()

    """base"""
    def default_copy_data(self, _key):
        match self.origin_redis.type(_key).decode():
            case "string":  # string, bytes, bit
                self.copy_string(_key)
            case "list":    # list
                self.copy_list(_key)
            case "set":     # set
                self.copy_set(_key)
            case "zset":    # no used
                pass
            case "hash":    # hash,
                self.copy_hash(_key)
            case _:         # hyperloglog
                try:
                    self.origin_redis.pfcount(_key)
                except redis.exceptions as e:
                    # 这个地方可能会有其他原因造成的报错，这里仅简单的print出来，不对错误类型做具体检查
                    print(e)
                    return

                self.copy_string(_key)

    def default_copy_ttl(self, _key):
        # 不做是否存在检查，默认存在，对一些测试时效性的场景可能不太友好
        if (ttl := self.origin_redis.ttl(_key)) > 0:
            self.fake_redis.expire(_key, ttl)

    def delete(self, *names):
        count = 0
        for _key in names:
            if self.has_write_key(_key):
                count += self.fake_redis.delete(_key)
            else:
                self.set_write_key(_key)
        return count

    def exists(self, *names):
        count = 0
        for _key in names:
            if self.has_write_key(_key):
                count += self.fake_redis.exists(_key)
            else:
                count += self.origin_redis.exists(_key)
        return count

    def expire(self, name, time):
        return self.default_write(
            name,
            partial(self.fake_redis.expire, name, time),
            self.default_copy_data,
            None,
        )

    def ttl(self, name):
        return self.default_read(
            name,
            self.origin_redis.ttl,
            self.fake_redis.ttl,
        )

    def expireat(self, name, time):
        return self.default_write(
            name,
            partial(self.fake_redis.expireat, name, time),
            self.default_copy_data,
            None,
        )

    """bytes/string"""
    def copy_string(self, _key):
        if (value := self.origin_redis.get(_key)) is not None:
            self.fake_redis.set(_key, value)

    def set(self, name, value, ex=None, nx=False):
        return self.default_write(
            name,
            partial(self.fake_redis.set, name, value, ex=ex, nx=nx),
            self.copy_string,
            None if ex else self.default_copy_ttl,
        )

    def get(self, name):
        return self.default_read(
            name,
            self.origin_redis.get,
            self.fake_redis.get,
        )

    def incr(self, name, amount):
        return self.default_write(
            name,
            partial(self.fake_redis.incr, name, amount),
            self.copy_string,
            self.default_copy_ttl,
        )

    """bits"""
    def getbit(self, name, offset):
        return self.default_read(
            name,
            self.origin_redis.getbit,
            self.fake_redis.getbit,
            (name, offset),
        )

    def setbit(self, name, offset, value):
        return self.default_write(
            name,
            partial(self.fake_redis.setbit, name, offset, value),
            self.copy_string,
            self.default_copy_ttl,
        )

    def bitcount(self, key):
        return self.default_read(
            key,
            self.origin_redis.bitcount,
            self.fake_redis.bitcount,
        )

    """hash"""
    def copy_hash(self, _key):
        if value_map := self.origin_redis.hgetall(_key):
            self.fake_redis.hset(_key, mapping=value_map)

    def hgetall(self, name):
        return self.default_read(
            name,
            self.origin_redis.hgetall,
            self.fake_redis.hgetall,
        )

    def hlen(self, name):
        return self.default_read(
            name,
            self.origin_redis.hlen,
            self.fake_redis.hlen,
        )

    def hkeys(self, name):
        return self.default_read(
            name,
            self.origin_redis.hkeys,
            self.fake_redis.hkeys,
        )

    def hexists(self, name, key):
        return self.default_read(
            name,
            self.origin_redis.hexists,
            self.fake_redis.hexists,
            (name, key),
        )

    def hget(self, name, key):
        return self.default_read(
            name,
            self.origin_redis.hget,
            self.fake_redis.hget,
            (name, key),
        )

    def hmget(self, name, keys):
        return self.default_read(
            name,
            self.origin_redis.hmget,
            self.fake_redis.hmget,
            (name, keys),
        )

    def hset(self, name, key=None, value=None, mapping=None):
        return self.default_write(
            name,
            partial(self.fake_redis.hset, name, key, value, mapping),
            self.copy_hash,
            self.default_copy_ttl,
        )

    def hdel(self, name, *keys):
        return self.default_write(
            name,
            partial(self.fake_redis.hdel, name, *keys),
            self.copy_hash,
            self.default_copy_ttl,
        )

    def hincrby(self, name, key, amount):
        return self.default_write(
            name,
            partial(self.fake_redis.hincrby, name, key, amount),
            self.copy_hash,
            self.default_copy_ttl,
        )

    """set"""
    def copy_set(self, _key):
        if values := self.origin_redis.smembers(_key):
            self.fake_redis.sadd(_key, *values)

    def scard(self, name):
        return self.default_read(
            name,
            self.origin_redis.scard,
            self.fake_redis.scard,
        )

    def smembers(self, name):
        return self.default_read(
            name,
            self.origin_redis.smembers,
            self.fake_redis.smembers,
        )

    def sismember(self, name, value):
        return self.default_read(
            name,
            self.origin_redis.sismember,
            self.fake_redis.sismember,
            (name, value),
        )

    def sadd(self, name, *values):
        return self.default_write(
            name,
            partial(self.fake_redis.sadd, name, *values),
            self.copy_set,
            self.default_copy_ttl,
        )

    def srem(self, name, *values):
        return self.default_write(
            name,
            partial(self.fake_redis.srem, name, *values),
            self.copy_set,
            self.default_copy_ttl,
        )

    """sorted set"""
    def zrange(self, _key, start, end, withscores=False):
        return self.default_read(
            _key,
            self.origin_redis.zrange,
            self.fake_redis.zrange,
            (_key, start, end, ),
            {"withscores": withscores}
        )

    def zrangebyscore(self, _key, min, max):
        return self.default_read(
            _key,
            self.origin_redis.zrangebyscore,
            self.fake_redis.zrangebyscore,
            (_key, min, max),
        )

    def zcopy_set(self, _key):
        if values := self.origin_redis.zrange(_key, 0, -1, withscores=True):
            mapping = dict(values)
            self.fake_redis.zadd(_key, mapping)

    def zcard(self, name):
        return self.default_read(
            name,
            self.origin_redis.zcard,
            self.fake_redis.zcard,
        )

    def zscore(self, name, value):
        return self.default_read(
            name,
            partial(self.origin_redis.zscore, value=value),
            partial(self.fake_redis.zscore, value=value),
        )

    def zadd(self, name, mapping, *args, **kwargs):
        return self.default_write(
            name,
            partial(self.fake_redis.zadd, name, mapping,  *args, **kwargs),
            self.zcopy_set,
            self.default_copy_ttl,
        )

    def zrem(self, name, *values):
        return self.default_write(
            name,
            partial(self.fake_redis.zrem, name, *values),
            self.zcopy_set,
            self.default_copy_ttl,
        )

    def zremrangebyrank(self, name, start, end):
        return self.default_write(
            name,
            partial(self.fake_redis.zremrangebyrank, name, start, end),
            self.zcopy_set,
            self.default_copy_ttl,
        )

    def zremrangebyscore(self, name, min, max):
        return self.default_write(
            name,
            partial(self.fake_redis.zremrangebyscore, name, min, max),
            self.zcopy_set,
            self.default_copy_ttl,
        )

    def zrevrange(self, name, start, end, withscores=False, score_cast_func=float):
        return self.default_read(
            name,
            self.origin_redis.zrevrange,
            self.fake_redis.zrevrange,
            (name, start, end),
            {"withscores": withscores, "score_cast_func": score_cast_func}
        )

    def zrevrangebyscore(self, name, _max, _min, start, num, withscores=False, score_cast_func=float):
        return self.default_read(
            name,
            self.origin_redis.zrevrangebyscore,
            self.fake_redis.zrevrangebyscore,
            (name, _max, _min, start, num),
            {"withscores": withscores, "score_cast_func": score_cast_func}
        )

    def zrevrank(self, name, value, withscore=False):
        return self.default_read(
            name,
            self.origin_redis.zrevrank,
            self.fake_redis.zrevrank,
            (name, value, withscore),
        )

    """list"""
    def copy_list(self, _key):
        if values := self.origin_redis.lrange(_key, 0, -1):
            self.fake_redis.rpush(_key, *values)

    def llen(self, name):
        return self.default_read(
            name,
            self.origin_redis.llen,
            self.fake_redis.llen,
        )

    def lrange(self, name, start, end):
        return self.default_read(
            name,
            self.origin_redis.lrange,
            self.fake_redis.lrange,
            (name, start, end),
        )

    def lpop(self, name):
        return self.default_write(
            name,
            partial(self.fake_redis.lpop, name),
            self.copy_list,
            self.default_copy_ttl,
        )

    def rpush(self, name, *values):
        return self.default_write(
            name,
            partial(self.fake_redis.rpush, name, *values),
            self.copy_list,
            self.default_copy_ttl,
        )

    """hyperloglog"""
    def copy_hyperloglog(self, _key):
        # 使用copy字符串的方式copy hyperloglog类型值，在新库中无法将对应key正确识别为hyperloglog类型，考虑目前使用的原数据为email和手机号，稀疏性较强，这里只简单实现数量与原数据一致，不保证内容一致
        if self.origin_redis.exists(_key):
            self.fake_redis.pfadd(_key, *(''.join(random.sample(string.ascii_letters + string.digits, 20)) for _ in range(self.origin_redis.pfcount(_key))))

    def pfcount(self, *sources):
        # 目前代码中只有查一个key的用法，节省拼接复杂返回结果的逻辑，这里限制只能传一个key，但函数签名仍保持接收多个参数
        if len(sources) != 1:
            raise TypeError

        key = sources[0]
        return self.default_read(
            key,
            self.origin_redis.pfcount,
            self.fake_redis.pfcount,
        )

    def pfadd(self, name, *values):
        return self.default_write(
            name,
            partial(self.fake_redis.pfadd, name, *values),
            self.copy_hyperloglog,
            self.default_copy_ttl,
        )
