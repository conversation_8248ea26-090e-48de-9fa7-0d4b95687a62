import pytest

from types import MethodType
from _pytest.monkeypatch import MonkeyPatch


@pytest.fixture(scope="function")
def patch_sqlalchemy(tapp):
    context = tapp.app_context()
    from app.models import db

    m = MonkeyPatch()

    try:
        with context:
            origin_rollback = db.session.rollback

            def mock_commit(self):
                self._nested = self.begin_nested()

            def mock_rollback(self):
                if self._nested is not None:
                    self._nested.rollback()
                else:
                    origin_rollback()

            m.setattr(db.session, 'commit', MethodType(mock_commit, db.session))
            m.setattr(db.session, 'rollback', MethodType(mock_rollback, db.session))
            db.session._nested = None
        yield
    finally:
        with context:
            m.undo()
            db.session._nested = None
            db.session.rollback()
