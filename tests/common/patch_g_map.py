from functools import wraps

import pytest

from app.utils.iterable import g_map


@pytest.fixture(scope="function")
def patch_g_map(tapp):
    context = tapp.app_context()

    # 替换已经import的func，只能修改代码指针的引用
    origin_g_map_code = g_map.__code__

    # 并行改串行
    def fake_g_map(func, *iterables, size=None, ordered=False, fail_safe=None) -> list:
        res = []
        for iterable in iterables:
            for i in iterable:
                if fail_safe is not None:
                    @wraps(func)
                    def _func(*args, **kwargs):
                        # noinspection PyBroadException
                        try:
                            return func(*args, **kwargs)
                        except Exception:
                            return fail_safe() if callable(fail_safe) else fail_safe
                else:
                    _func = func

                res.append(_func(i))
        return res

    try:
        with context:
            g_map.__code__ = fake_g_map.__code__
        yield
    finally:
        with context:
            g_map.__code__ = origin_g_map_code
