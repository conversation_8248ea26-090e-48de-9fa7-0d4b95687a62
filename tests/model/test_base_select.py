import pytest
from flask import g

from tests.common.t_common import default_lang
from app.common.constants import Language
from app.models.comment import Comment
from sqlalchemy import select, exists


DEFAULT_USER_ID = 2

@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.user_id = DEFAULT_USER_ID
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
class TestBaseResource:
    def test_select_exists(self, tcontext):
        from app.models import db
        with tcontext:
            stmt = select(exists().where(Comment.id == 2))
            print(db.session.scalar(stmt))

    def test_select_not_exists(self, tcontext):
        from app.models import db
        with tcontext:
            stmt = select(exists().where(Comment.id == 1))
            print(db.session.scalar(stmt))
