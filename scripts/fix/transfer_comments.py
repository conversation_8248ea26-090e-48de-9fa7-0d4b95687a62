#!/usr/bin/env python3
"""
评论迁移脚本
用于将指定的评论从一个币种迁移到另一个币种
包括数据库和缓存的完整迁移
"""
import os
import sys
import argparse
from argparse import ArgumentError
from typing import List, Dict, Set

from rich.console import Console
from rich.table import Table
from rich.prompt import Confirm

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from app.models import db
from app.common.constants import Language


class CommentMigrator:
    """评论迁移器"""
    
    def __init__(self, source_code, target_code, comment_ids, console: Console = Console()):
        self.source_code = source_code
        self.target_code = target_code
        self.root_comment_ids = comment_ids if comment_ids else []
        self.console = console

        self.source_id = self._get_business_id_from_code(source_code)
        self.target_id = self._get_business_id_from_code(target_code)

        self.all_comment_ids = []
        if not self._get_comments():
            raise ArgumentError

        self.migrated_comments: List[int] = []
        self.affected_caches: Set[str] = set()

    def _get_comments(self) -> bool:
        from app.models.comment import Comment

        """获取需要迁移的评论"""
        root_comments = Comment.query.filter(
            Comment.business == Comment.Business.COIN,
            Comment.id.in_(self.root_comment_ids)
        ).all()

        if not root_comments:
            self.console.print(f"[yellow]未找到需要迁移的评论[/yellow]")
            return False

        # 确认源币种的业务代码
        for comment in root_comments:
            if comment.business_code != self.source_code:
                self.console.print(f"[red]评论 {comment.id} 的币种是“{comment.business_code}”，与源币种不匹配[/red]")
                return False
            if comment.parent_id is not None:
                self.console.print(f"[red]评论 {comment.id} 不是一级评论，不能迁移[/red]")
                return False

        self.root_comments = root_comments

        self.all_comment_ids.extend(self.root_comment_ids)

        for root_id in self.root_comment_ids:
            # 获取所有子评论
            child_comments = Comment.query.filter(
                Comment.root_id == root_id
            ).with_entities(Comment.id, Comment.status).all()
            self.all_comment_ids.extend([child.id for child in child_comments])
            self.console.print(
                f'发现 {len([child.id for child in child_comments if child.status != Comment.CommentStatus.PUBLISHED])} '
                f'条未发布的回复'
            )

        return True

    def preview_comments_to_migrate(self):
        """获取需要迁移的评论列表"""
        # 显示评论信息
        table = Table(title=f"待迁移评论列表 (源币种: {self.source_code})")
        table.add_column("评论ID", style="cyan")
        table.add_column("用户ID", style="green")
        table.add_column("内容预览", style="white")
        table.add_column("状态", style="yellow")
        table.add_column("创建时间", style="blue")

        comments = self.root_comments

        for comment in comments[:10]:  # 只显示前10条
            content_preview = comment.content[:50] + "..." if len(comment.content) > 50 else comment.content
            table.add_row(
                str(comment.id),
                str(comment.user_id),
                content_preview,
                comment.status.name,
                str(comment.created_at)
            )
            
        if len(comments) > 10:
            table.add_row("...", "...", f"还有 {len(comments) - 10} 条评论", "...", "...")
            
        self.console.print(table)
        self.console.print(f"\n[bold]总计: {len(comments)} 条一级评论，{len(self.root_comment_ids) - len(comments)} 条回复。[/bold]")
        
        return comments
    
    def migrate_database_records(self):
        """迁移数据库记录"""
        self.console.print(f"\n[bold blue]开始迁移数据库记录...[/bold blue]")
        from app.models.comment import Comment

        comment_ids = self.all_comment_ids
        
        try:
            # 1. 更新主评论表
            self.console.print("1. 更新评论表...")
            Comment.query.filter(Comment.id.in_(comment_ids)).update({
                'business_id': self.target_id,
                'business_code': self.target_code,
            }, synchronize_session=False)

            # 记录迁移的评论ID
            self.migrated_comments.extend(comment_ids)
            db.session.commit()
            
        except Exception as e:
            self.console.print(f"[red]✗ 数据库迁移失败: {str(e)}[/red]")
            db.session.rollback()
            raise
    
    def clear_source_caches(self):
        """清理源币种的缓存"""
        self.console.print(f"\n[bold blue]清理源币种缓存...[/bold blue]")

        from app.cache.comment_cache import CommentListCache, CommentCountCache
        from app.models.comment import Comment

        # 按语言分组评论
        comments_by_lang: Dict[Language, List[Comment]] = {}
        root_comments = self.root_comments

        for comment in root_comments:
            if comment.lang not in comments_by_lang:
                comments_by_lang[comment.lang] = []
            comments_by_lang[comment.lang].append(comment)
        
        # 1. 清理评论列表缓存
        self.console.print("1. 清理评论列表缓存...")
        for lang, lang_comments in comments_by_lang.items():

            for sort_type in Comment.SortType:
                list_cache = CommentListCache(
                    business=Comment.Business.COIN,
                    business_id=self.source_id,
                    lang=lang,
                    sort_type=sort_type
                )
                if list_cache.exists():
                    # 从列表中移除评论
                    for comment in lang_comments:
                        list_cache.remove_comment(comment.id)
                    self.affected_caches.add(f"list:{self.source_id}:{lang.name}:{sort_type.name}")

        # 3. 更新评论计数缓存
        self.console.print("2. 更新源币种评论计数缓存...")
        for lang, lang_comments in comments_by_lang.items():
            count_cache = CommentCountCache(Comment.Business.COIN.name, self.source_id, lang)

            # 计算一级评论数量（root_id为None的评论）
            root_comments_count = len(lang_comments)
            if root_comments_count > 0 and count_cache.exists():
                count_cache.decr(root_comments_count)
                self.affected_caches.add(f"count:{self.source_id}:{lang.name}")

        self.console.print("[green]✓ 源币种缓存清理完成[/green]")

    def rebuild_target_caches(self):
        """重建目标币种的缓存"""
        self.console.print(f"\n[bold blue]重建目标币种缓存...[/bold blue]")
        from app.models.comment import Comment
        from app.cache.comment_cache import CommentCache, CommentListCache, CommentCountCache

        # 按语言分组评论
        comments_by_lang: Dict[Language, List[Comment]] = {}

        root_comments = self.root_comments
        all_comment_ids = self.all_comment_ids
        target_id = self.target_id
        target_code = self.target_code

        for comment in root_comments:
            if comment.lang not in comments_by_lang:
                comments_by_lang[comment.lang] = []
            comments_by_lang[comment.lang].append(comment)
        
        # 1. 重建评论详情缓存
        self.console.print("1. 重建评论详情缓存...")
        for comment_id in all_comment_ids:
            comment_cache = CommentCache(comment_id)
            if not comment_cache.exists():
                continue
            comment_cache.hset('business_id', target_id)
            comment_cache.hset('business_code', target_code)
            self.affected_caches.add(f"comment:{comment_id}")

        # 2. 重建评论列表缓存
        self.console.print("2. 重建评论列表缓存...")
        for lang, lang_comments in comments_by_lang.items():
            for sort_type in Comment.SortType:
                list_cache = CommentListCache(
                    business=Comment.Business.COIN,
                    business_id=target_id,
                    lang=lang,
                    sort_type=sort_type
                )
                if not list_cache.exists():
                    continue

                # 添加评论到列表缓存
                for comment in lang_comments:
                    list_cache.add_comment(comment)

                self.affected_caches.add(f"list:{target_code}:{lang.name}:{sort_type.name}")

        # 3. 更新目标币种评论计数缓存
        self.console.print("3. 更新目标币种评论计数缓存...")
        for lang, lang_comments in comments_by_lang.items():
            count_cache = CommentCountCache(Comment.Business.COIN.name, target_id, lang)
            if not count_cache.exists():
                continue
            # 计算一级评论数量（root_id为None的评论）
            count_cache.incr(len(lang_comments))
            self.affected_caches.add(f"count:{target_code}:{lang.name}")

        self.console.print("[green]✓ 目标币种缓存重建完成[/green]")

    def migrate_comments(self):
        """执行完整的评论迁移"""
        source_code = self.source_code
        target_code = self.target_code
        root_comments = self.root_comments
        all_comment_ids = self.all_comment_ids

        self.console.print(f"[bold]开始迁移评论[/bold]")
        self.console.print(f"源币种: {source_code}")
        self.console.print(f"目标币种: {target_code}")

        # 确认迁移
        if not Confirm.ask(f"\n确认要迁移 {len(root_comments)} 条评论和它们的 {len(all_comment_ids) - len(root_comments)} 条回复吗？"):
            self.console.print("[yellow]迁移已取消[/yellow]")
            return False

        # 4. 迁移数据库记录
        self.migrate_database_records()

        # 5. 清理源币种缓存
        self.clear_source_caches()

        # 6. 重建目标币种缓存
        self.rebuild_target_caches()

        # 7. 显示迁移结果
        self.show_migration_result()

        return True

    def show_migration_result(self):
        """显示迁移结果"""
        self.console.print(f"\n[bold green]✓ 迁移完成！[/bold green]")
        self.console.print(f"迁移总评论数量: {len(self.migrated_comments)}")
        self.console.print(f"影响缓存数量: {len(self.affected_caches)}")
        
        if self.migrated_comments:
            self.console.print(f"\n迁移的评论ID: {', '.join(map(str, self.migrated_comments[:10]))}")
            if len(self.migrated_comments) > 10:
                self.console.print(f"... 还有 {len(self.migrated_comments) - 10} 条")

    @staticmethod
    def _get_business_id_from_code(business_code):
        from app.models.comment import Comment

        comment = Comment.query.filter_by(business_code=business_code).order_by(Comment.id.desc()).first()
        if not comment:
            raise ValueError(f"未找到业务代码 {business_code} 对应的业务ID")
        return comment.business_id


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='评论迁移脚本')
    parser.add_argument('--source-code', required=True, help='源币种ID')
    parser.add_argument('--target-code', required=True, help='目标币种ID')
    parser.add_argument('--comment-ids', help='指定要迁移的评论ID列表，用逗号分隔（可选）')
    parser.add_argument('--preview', action='store_true', help='仅预览，不执行实际迁移')
    
    args = parser.parse_args()
    
    console = Console()

    try:
        # 解析评论ID列表
        comment_ids = None
        if args.comment_ids:
            comment_ids = [int(id.strip()) for id in args.comment_ids.split(',')]
        
        migrator = CommentMigrator(args.source_code, args.target_code, comment_ids, console)

        if args.preview:
            console.print("[yellow]预览模式 - 不会执行实际迁移[/yellow]")
            migrator.preview_comments_to_migrate()
        else:
            success = migrator.migrate_comments()
            if not success:
                sys.exit(1)
                
    except KeyboardInterrupt:
        console.print("\n[yellow]操作已取消[/yellow]")
        sys.exit(0)


if __name__ == '__main__':
    from app import create_app
    
    app = create_app()
    
    with app.app_context():
        main()
