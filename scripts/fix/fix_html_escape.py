#!/usr/bin/env python3
import os
import sys
from rich.console import Console

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    """主函数"""

    from app.models.comment import Comment
    from app.models import db

    count = 0
    try:
        for comment in Comment.query.filter(Comment.content.like('%&amp;amp;%')).all():
            print(f'---\n{comment.content}')
            comment.content = comment.content.replace('&amp;amp;', '&amp;')
            count += 1
        db.session.commit()
        print(f"\n '&' 多重转义修复完成，共修复 {count} 条记录")

    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(0)
    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    from app import create_app

    app = create_app()

    with app.app_context():
        main()
