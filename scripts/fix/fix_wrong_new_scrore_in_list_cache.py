#!/usr/bin/env python3
import os
import sys
from rich.console import Console

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    """主函数"""
    console = Console()

    try:
        from app.cache.comment_cache import CommentListCache, Comment
        from app import redis

        # 获取所有符合 comment_list:*:NEW 模式的键
        pattern = "comment_list:root:COIN:*:*:NEW"
        keys = redis.keys(pattern)

        fixed_count = 0
        for _key in keys:
            # 从key中解析出参数
            key = _key.decode()
            parts = key.split(':')
            if len(parts) != 6:  # comment_list:root:business:business_id:lang:NEW
                continue

            # 获取该列表缓存中的所有评论
            result = redis.zrange(key, 0, -1, withscores=True)
            items = [(v[0].decode(), v[1]) for v in result]

            for comment_id_str, score in items:
                comment_id_float = float(comment_id_str)
                if comment_id_float != score:
                    redis.zadd(key, {comment_id_str: comment_id_float})
                    console.print(f"Fixed score for comment {comment_id_str} in {key}, old value {score}")
                    fixed_count += 1

        console.print(f"\n[green]修复完成，共修复 {fixed_count} 条记录[/green]")

    except KeyboardInterrupt:
        console.print("\n操作已取消")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]错误: {str(e)}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    app.testing = True
    
    with app.app_context():
        main() 