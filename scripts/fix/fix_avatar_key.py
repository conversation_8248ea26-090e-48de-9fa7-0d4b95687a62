#!/usr/bin/env python3
import os
import sys
from rich.console import Console

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    """主函数"""
    console = Console()

    from app.cache.user import UserInfo, UserInfoCache
    from app.models import db

    try:
        offset = 0
        limit = 100
        while user_infos := UserInfo.query.offset(offset * limit).limit(limit).all():
            for user_info in user_infos:
                avatar = user_info.avatar
                if avatar is None:
                    continue

                if not avatar.startswith('private/'):
                    continue

                avatar = avatar.removeprefix('private/')
                user_info.avatar = avatar
                db.session.commit()

                console.print(f'\nfix_key: {avatar}')

                UserInfoCache().set_user_info(user_info.user_id, avatar=avatar)

            offset += 1

        console.print(f"\n[green]修复完成[/green]")

    except KeyboardInterrupt:
        console.print("\n操作已取消")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]错误: {str(e)}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    from app import create_app

    app = create_app()

    with app.app_context():
        main()
