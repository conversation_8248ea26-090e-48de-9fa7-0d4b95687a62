#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量审核历史评论的脚本
只处理脚本启动时间之前的评论，避免并发冲突
当且仅当违规类型为"OTHER"时，将评论状态改为禁用
"""

import os
import sys
import time
import threading
import csv
import logging
from datetime import datetime, timedelta
from typing import List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from sqlalchemy import and_, desc
from sqlalchemy.orm import sessionmaker
from rich.console import Console
from rich.progress import Progress, TaskID, BarColumn, TextColumn, TimeRemainingColumn, TimeElapsedColumn
from rich.table import Table
from rich.panel import Panel

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

from app.models import db
from app.models.comment import Comment
from app.models.moderation import CommentModeration
from app.business.moderation_manager import ModerationManager
from app.business.comment_manager import CommentManager

console = Console()

# 线程安全的计数器
class ThreadSafeCounter:
    """线程安全的计数器"""
    def __init__(self):
        self._value = 0
        self._lock = threading.Lock()

    def increment(self, delta=1):
        with self._lock:
            self._value += delta

    @property
    def value(self):
        with self._lock:
            return self._value

# 线程安全的列表
class ThreadSafeList:
    """线程安全的列表"""
    def __init__(self):
        self._list = []
        self._lock = threading.Lock()

    def append(self, item):
        with self._lock:
            self._list.append(item)

    def extend(self, items):
        with self._lock:
            self._list.extend(items)

    @property
    def items(self):
        with self._lock:
            return self._list.copy()

class HistoricalCommentReauditScript:
    """历史评论重新审核脚本（多线程版本）"""

    def __init__(self, batch_size: int = 100, dry_run: bool = True, max_workers: int = 4, log_mode: bool = False):
        """
        初始化参数
        Args:
            batch_size: 每批处理的评论数量
            dry_run: 是否为试运行模式
            max_workers: 最大线程数
            log_mode: 是否使用日志模式（后台运行时使用）
        """
        self.batch_size = batch_size
        self.dry_run = dry_run
        self.max_workers = max_workers
        self.log_mode = log_mode

        # 线程安全的计数器
        self.processed_count = ThreadSafeCounter()
        self.disabled_count = ThreadSafeCounter()
        self.error_count = ThreadSafeCounter()
        self.skipped_count = ThreadSafeCounter()

        # 线程安全的列表，用于收集被禁用的评论信息
        self.disabled_comments = ThreadSafeList()

        # 时间统计
        self.start_time = None
        self.progress_lock = threading.Lock()
        self.last_log_time = 0

        # 记录脚本启动时间，只处理此时间之前的评论
        self.script_start_time = datetime.utcnow()

        # 创建数据库会话工厂
        self.Session = sessionmaker(bind=db.engine)

        # 保存Flask应用实例，用于在线程中创建应用上下文
        from flask import current_app
        self.app = current_app._get_current_object()

        # 配置日志
        if self.log_mode:
            self.setup_logging()

        # 显示启动信息
        self.show_startup_info()

    def setup_logging(self):
        """配置日志输出"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def show_startup_info(self):
        """显示启动信息"""
        if self.log_mode:
            self.logger.info("=" * 60)
            self.logger.info("历史评论联系方式违规处理脚本（多线程版本）")
            self.logger.info("=" * 60)
            self.logger.info(f"📅 脚本启动时间: {self.script_start_time}")
            self.logger.info(f"🔧 运行模式: {'试运行模式' if self.dry_run else '正式运行模式'}")
            self.logger.info(f"📦 批处理大小: {self.batch_size}")
            self.logger.info(f"🧵 最大线程数: {self.max_workers}")
            self.logger.info(f"ℹ️ 只会处理启动时间之前创建的评论")
            self.logger.info("=" * 60)
        else:
            startup_panel = Panel(
                f"📅 脚本启动时间: {self.script_start_time}\n"
                f"🔧 运行模式: {'试运行模式' if self.dry_run else '正式运行模式'}\n"
                f"📦 批处理大小: {self.batch_size}\n"
                f"🧵 最大线程数: {self.max_workers}\n"
                f"ℹ️ 只会处理启动时间之前创建的评论",
                title="历史评论联系方式违规处理脚本（多线程版本）",
                style="blue"
            )
            console.print(startup_panel)

    def log_progress(self, force=False):
        """记录进度（日志模式）"""
        if not self.log_mode:
            return

        current_time = time.time()
        # 每30秒或强制记录一次进度
        if not force and current_time - self.last_log_time < 30:
            return

        self.last_log_time = current_time
        elapsed_time = current_time - self.start_time if self.start_time else 0
        speed = self.processed_count.value / elapsed_time if elapsed_time > 0 else 0

        if hasattr(self, 'total_comments') and speed > 0:
            remaining_items = self.total_comments - self.processed_count.value
            eta_seconds = remaining_items / speed
            eta_formatted = self.format_time_remaining(eta_seconds)
            percentage = (self.processed_count.value / self.total_comments) * 100
        else:
            eta_formatted = "计算中..."
            percentage = 0

        self.logger.info(
            f"📊 进度更新: {self.processed_count.value}/{getattr(self, 'total_comments', '?')} "
            f"({percentage:.1f}%) | 禁用: {self.disabled_count.value} | "
            f"跳过: {self.skipped_count.value} | 错误: {self.error_count.value} | "
            f"速度: {speed:.1f}条/秒 | 预计剩余: {eta_formatted}"
        )

    def get_total_count(self) -> int:
        """获取符合条件的评论总数"""
        return Comment.query.filter(
            and_(
                Comment.status == Comment.CommentStatus.PUBLISHED,
                Comment.created_at < self.script_start_time
            )
        ).count()

    def get_comment_batches(self, total_comments: int) -> List[tuple]:
        """
        将评论分批，每批返回 (offset, limit)
        Args:
            total_comments: 总评论数
        Returns:
            批次列表
        """
        batches = []
        offset = 0
        while offset < total_comments:
            current_batch_size = min(self.batch_size, total_comments - offset)
            batches.append((offset, current_batch_size))
            offset += current_batch_size
        return batches

    def get_latest_moderation_record(self, session, comment_id: int) -> Optional[CommentModeration]:
        """
        获取评论的最新审核记录
        Args:
            session: 数据库会话
            comment_id: 评论ID
        Returns:
            最新的审核记录或None
        """
        return session.query(CommentModeration).filter(
            CommentModeration.comment_id == comment_id
        ).order_by(desc(CommentModeration.created_at)).first()

    def is_processed_by_script(self, moderation_record: CommentModeration) -> bool:
        """
        检查审核记录是否由此脚本处理
        Args:
            moderation_record: 审核记录
        Returns:
            是否由脚本处理
        """
        if not moderation_record or not moderation_record.remark:
            return False
        return "处理历史评论联系方式违规" in moderation_record.remark

    def process_comment_batch(self, batch_info: tuple, progress_task=None) -> dict:
        """
        处理一批评论
        Args:
            batch_info: (offset, limit) 批次信息
            progress_task: 进度条任务（可选）
        Returns:
            处理结果统计
        """
        offset, limit = batch_info
        results = {
            'processed': 0,
            'disabled': 0,
            'skipped': 0,
            'errors': 0
        }

        # 在线程中创建Flask应用上下文
        with self.app.app_context():
            # 确保Redis连接在当前线程中可用
            from app import redis
            from app.cache.base import _BaseCache
            if _BaseCache.redis is None:
                _BaseCache.redis = redis
            
            # 创建线程独立的数据库会话
            session = self.Session()

            try:
                # 获取这批评论
                comments = session.query(Comment).filter(
                    and_(
                        Comment.status == Comment.CommentStatus.PUBLISHED,
                        Comment.created_at < self.script_start_time
                    )
                ).order_by(Comment.id.desc()).offset(offset).limit(limit).all()

                for comment in comments:
                    try:
                        # 检查最新审核记录是否已经被脚本处理过
                        latest_moderation = self.get_latest_moderation_record(session, comment.id)
                        if latest_moderation and self.is_processed_by_script(latest_moderation):
                            results['skipped'] += 1
                            self.skipped_count.increment()
                        else:
                            # 进行审核
                            moderation_result = ModerationManager.initial_moderate(comment, if_detail=False)

                            # 检查审核结果
                            passed = moderation_result.status == CommentModeration.Status.APPROVED
                            reject_type = moderation_result.rejected_type

                            if not passed and reject_type == CommentModeration.RejectType.OTHER:
                                # 需要禁用评论
                                comment_info = {
                                    'comment_id': comment.id,
                                    'content': comment.content,
                                    'user_id': comment.user_id,
                                    'created_at': comment.created_at.isoformat() if comment.created_at else '',
                                    'reason': moderation_result.reason or '',
                                    'processed_at': datetime.utcnow().isoformat()
                                }

                                if not self.dry_run:
                                    session.query(CommentModeration).filter(
                                        CommentModeration.id == moderation_result.id
                                    ).update({
                                        'remark': "处理历史评论联系方式违规"
                                    })
                                    session.commit()
                                    # 再次检查评论状态
                                    current_comment = Comment.query.get(comment.id)
                                    if current_comment and current_comment.status == Comment.CommentStatus.PUBLISHED:
                                        moderation_result.remark = "处理历史评论联系方式违规"
                                        # 禁用评论
                                        CommentManager.disable(current_comment)
                                        # 记录被禁用的评论信息
                                        self.disabled_comments.append(comment_info)
                                else:
                                    comment_info['dry_run'] = True
                                    self.disabled_comments.append(comment_info)

                                results['disabled'] += 1
                                self.disabled_count.increment()

                        results['processed'] += 1
                        self.processed_count.increment()

                        # 每10条评论更新一次进度
                        if results['processed'] % 10 == 0:
                            if self.log_mode:
                                self.log_progress()
                            else:
                                self.update_progress_safely(progress_task)

                    except Exception as e:
                        error_msg = f"处理评论 {comment.id} 时发生错误: {str(e)}"
                        if self.log_mode:
                            self.logger.error(error_msg)
                        else:
                            console.print(f"❌ {error_msg}", style="red")
                        results['errors'] += 1
                        self.error_count.increment()

            except Exception as e:
                error_msg = f"处理批次 {offset}-{offset + limit} 时发生错误: {str(e)}"
                if self.log_mode:
                    self.logger.error(error_msg)
                else:
                    console.print(f"❌ {error_msg}", style="red")
                results['errors'] += limit  # 整批失败
                self.error_count.increment(limit)

            finally:
                session.close()

        # 批次完成日志
        if self.log_mode and results['disabled'] > 0:
            self.logger.info(f"✅ 批次 {offset}~{offset + limit - 1} 完成，禁用 {results['disabled']} 条评论")

        return results

    def update_progress_safely(self, progress_task):
        """线程安全地更新进度条（仅在交互模式下使用）"""
        if self.log_mode:
            return

        with self.progress_lock:
            if hasattr(self, 'progress') and hasattr(self, 'task'):
                # 计算处理速度和剩余时间
                elapsed_time = time.time() - self.start_time
                speed = self.processed_count.value / elapsed_time if elapsed_time > 0 else 0

                if speed > 0 and hasattr(self, 'total_comments'):
                    remaining_items = self.total_comments - self.processed_count.value
                    eta_seconds = remaining_items / speed
                    eta_formatted = self.format_time_remaining(eta_seconds)
                else:
                    eta_formatted = "计算中..."

                # 更新进度条
                self.progress.update(
                    self.task,
                    completed=self.processed_count.value,
                    disabled=self.disabled_count.value,
                    skipped=self.skipped_count.value,
                    errors=self.error_count.value,
                    speed=speed,
                    eta=eta_formatted
                )

    def format_time_remaining(self, seconds: float) -> str:
        """
        格式化剩余时间显示
        Args:
            seconds: 剩余秒数
        Returns:
            格式化的时间字符串
        """
        if seconds < 60:
            return f"{int(seconds)}秒"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}分{secs}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}时{minutes}分"

    def run(self):
        """运行脚本"""
        # 获取总评论数
        self.total_comments = self.get_total_count()

        if self.total_comments == 0:
            if self.log_mode:
                self.logger.warning("没有找到符合条件的历史评论")
            else:
                console.print("❗ 没有找到符合条件的历史评论", style="yellow")
            return

        if self.log_mode:
            self.logger.info(f"📊 共有 {self.total_comments} 条历史评论需要处理")
            self.logger.info(f"🧵 使用 {self.max_workers} 个线程并行处理")
        else:
            console.print(f"📊 共有 [bold]{self.total_comments}[/bold] 条历史评论需要处理")
            console.print(f"🧵 使用 [bold]{self.max_workers}[/bold] 个线程并行处理\n")

        # 记录开始时间
        self.start_time = time.time()

        # 获取所有批次
        batches = self.get_comment_batches(self.total_comments)
        if self.log_mode:
            self.logger.info(f"📦 总共分为 {len(batches)} 个批次处理")
        else:
            console.print(f"📦 总共分为 [bold]{len(batches)}[/bold] 个批次处理")

        if self.log_mode:
            # 日志模式：简单的线程池处理
            self.run_with_logging(batches)
        else:
            # 交互模式：带进度条的处理
            self.run_with_progress_bar(batches)

        # 输出最终统计
        self.show_final_stats()

    def run_with_logging(self, batches):
        """使用日志模式运行"""
        # 使用线程池处理批次
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            try:
                # 提交所有批次任务
                future_to_batch = {
                    executor.submit(self.process_comment_batch, batch): batch
                    for batch in batches
                }

                # 等待任务完成
                for future in as_completed(future_to_batch):
                    batch = future_to_batch[future]
                    try:
                        result = future.result()
                        # 静默处理，只在有错误时输出
                        if result['errors'] > 0:
                            self.logger.error(f"批次 {batch} 处理时发生 {result['errors']} 个错误")
                    except Exception as e:
                        self.logger.error(f"批次 {batch} 处理失败: {str(e)}")

            except KeyboardInterrupt:
                self.logger.warning("检测到中断信号，正在停止所有线程...")
                # 等待所有正在执行的任务完成或超时
                executor.shutdown(wait=True, cancel_futures=True)
                self.logger.warning("所有线程已停止")
                raise

        # 强制记录最终进度
        self.log_progress(force=True)

    def run_with_progress_bar(self, batches):
        """使用进度条模式运行"""
        # 创建进度条
        with Progress(
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
                TextColumn("•"),
                TextColumn("[blue]{task.completed}/{task.total}"),
                TextColumn("•"),
                TextColumn("[green]禁用:{task.fields[disabled]}"),
                TextColumn("[yellow]跳过:{task.fields[skipped]}"),
                TextColumn("[red]错误:{task.fields[errors]}"),
                TextColumn("•"),
                TextColumn("[cyan]{task.fields[speed]:.1f}条/秒"),
                TextColumn("•"),
                TimeElapsedColumn(),
                TextColumn("•"),
                TextColumn("[magenta]剩余:{task.fields[eta]}"),
                console=console,
                refresh_per_second=1
        ) as progress:

            self.progress = progress
            self.task = progress.add_task(
                f"处理评论中（{self.max_workers}线程）...",
                total=self.total_comments,
                disabled=0,
                skipped=0,
                errors=0,
                speed=0.0,
                eta="计算中..."
            )

            # 使用线程池处理批次
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                try:
                    # 提交所有批次任务
                    future_to_batch = {
                        executor.submit(self.process_comment_batch, batch, self.task): batch
                        for batch in batches
                    }

                    # 等待任务完成
                    for future in as_completed(future_to_batch):
                        batch = future_to_batch[future]
                        try:
                            result = future.result()
                            # 批次完成后的日志（静默模式，减少输出）
                            if result['disabled'] > 0:
                                console.print(
                                    f"✅ 批次 {batch[0]}~{batch[0] + batch[1] - 1} 完成，禁用 {result['disabled']} 条评论")
                        except Exception as e:
                            console.print(f"❌ 批次 {batch} 处理失败: {str(e)}", style="red")

                except KeyboardInterrupt:
                    console.print("\n⚠️ 检测到中断信号，正在停止所有线程...", style="yellow")
                    # 等待所有正在执行的任务完成或超时
                    executor.shutdown(wait=True, cancel_futures=True)
                    console.print("🛑 所有线程已停止", style="yellow")
                    raise

    def export_disabled_comments_to_csv(self):
        """导出被禁用的评论到CSV文件"""
        disabled_list = self.disabled_comments.items

        if not disabled_list:
            if self.log_mode:
                self.logger.info("没有被禁用的评论需要导出")
            else:
                console.print("📝 没有被禁用的评论需要导出", style="yellow")
            return None

        # 生成文件名（包含时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"disabled_comments_{timestamp}.csv"
        filepath = os.path.join(os.path.dirname(__file__), filename)

        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'comment_id',
                    'content',
                    'user_id',
                    'created_at',
                    'reason',
                    'processed_at'
                ]

                if self.dry_run:
                    fieldnames.append('dry_run')

                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for comment_info in disabled_list:
                    writer.writerow(comment_info)

            if self.log_mode:
                self.logger.info(f"成功导出 {len(disabled_list)} 条被禁用评论信息到: {filepath}")
            else:
                console.print(f"📄 成功导出 {len(disabled_list)} 条被禁用评论信息到: [bold green]{filepath}[/bold green]")
            return filepath

        except Exception as e:
            error_msg = f"导出CSV文件时发生错误: {str(e)}"
            if self.log_mode:
                self.logger.error(error_msg)
            else:
                console.print(f"❌ {error_msg}", style="red")
            return None

    def show_final_stats(self):
        """显示最终统计信息"""
        total_time = time.time() - self.start_time if self.start_time else 0
        avg_speed = self.processed_count.value / total_time if total_time > 0 else 0

        if self.log_mode:
            # 日志模式输出
            self.logger.info("=" * 60)
            self.logger.info("📋 执行结果统计")
            self.logger.info("=" * 60)
            total = self.processed_count.value
            self.logger.info(f"总处理数量: {total}")
            self.logger.info(f"禁用数量: {self.disabled_count.value} ({(self.disabled_count.value / total * 100):.1f}%)" if total > 0 else "禁用数量: 0 (0%)")
            self.logger.info(f"跳过数量: {self.skipped_count.value} ({(self.skipped_count.value / total * 100):.1f}%)" if total > 0 else "跳过数量: 0 (0%)")
            self.logger.info(f"错误数量: {self.error_count.value} ({(self.error_count.value / total * 100):.1f}%)" if total > 0 else "错误数量: 0 (0%)")
            
            self.logger.info("=" * 60)
            self.logger.info("⏰ 时间统计")
            self.logger.info("=" * 60)
            self.logger.info(f"脚本启动时间: {self.script_start_time}")
            self.logger.info(f"总执行时间: {self.format_time_remaining(total_time)}")
            self.logger.info(f"平均处理速度: {avg_speed:.1f} 条/秒")
            self.logger.info(f"使用线程数: {self.max_workers}")
            self.logger.info(f"运行模式: {'试运行模式 (未实际修改数据)' if self.dry_run else '正式运行模式'}")
            
        else:
            # Rich模式输出
            # 创建统计表格
            stats_table = Table(title="📋 执行结果统计")
            stats_table.add_column("项目", style="cyan", width=15)
            stats_table.add_column("数量", style="magenta", justify="right", width=10)
            stats_table.add_column("比例", style="green", justify="right", width=10)

            total = self.processed_count.value
            stats_table.add_row("总处理数量", str(total), "100.0%")
            stats_table.add_row(
                "禁用数量",
                str(self.disabled_count.value),
                f"{(self.disabled_count.value / total * 100):.1f}%" if total > 0 else "0%"
            )
            stats_table.add_row(
                "跳过数量",
                str(self.skipped_count.value),
                f"{(self.skipped_count.value / total * 100):.1f}%" if total > 0 else "0%"
            )
            stats_table.add_row(
                "错误数量",
                str(self.error_count.value),
                f"{(self.error_count.value / total * 100):.1f}%" if total > 0 else "0%"
            )

            console.print("\n")
            console.print(stats_table)

            # 时间统计
            time_info = Panel(
                f"⏰ 脚本启动时间: {self.script_start_time}\n"
                f"⏱️ 总执行时间: {self.format_time_remaining(total_time)}\n"
                f"🚀 平均处理速度: {avg_speed:.1f} 条/秒\n"
                f"🧵 使用线程数: {self.max_workers}\n"
                f"🎯 运行模式: {'试运行模式 (未实际修改数据)' if self.dry_run else '正式运行模式'}",
                title="时间统计",
                style="blue"
            )
            console.print(time_info)

        # 导出被禁用评论到CSV
        if self.disabled_count.value > 0:
            if self.log_mode:
                self.logger.info(f"正在导出 {self.disabled_count.value} 条被禁用评论的详细信息...")
            else:
                console.print(f"\n📊 正在导出 {self.disabled_count.value} 条被禁用评论的详细信息...")
            
            csv_file = self.export_disabled_comments_to_csv()

            if csv_file:
                if self.log_mode:
                    self.logger.info(f"脚本执行完成！共禁用了 {self.disabled_count.value} 条违规评论")
                    self.logger.info(f"详细信息已导出到: {csv_file}")
                else:
                    console.print(f"\n✅ 脚本执行完成！共禁用了 [bold red]{self.disabled_count.value}[/bold red] 条违规评论",
                                  style="bold green")
                    console.print(f"📄 详细信息已导出到: [bold blue]{csv_file}[/bold blue]")
            else:
                if self.log_mode:
                    self.logger.info(f"脚本执行完成！共禁用了 {self.disabled_count.value} 条违规评论")
                    self.logger.warning("CSV导出失败")
                else:
                    console.print(f"\n✅ 脚本执行完成！共禁用了 [bold red]{self.disabled_count.value}[/bold red] 条违规评论",
                                  style="bold green")
                    console.print("⚠️ CSV导出失败", style="yellow")
        else:
            if self.log_mode:
                self.logger.info("脚本执行完成！没有发现需要禁用的评论")
            else:
                console.print(f"\n✅ 脚本执行完成！没有发现需要禁用的评论", style="bold green")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='批量审核历史评论（多线程版本）')
    parser.add_argument(
        '--batch-size',
        type=int,
        default=100,
        help='每批处理的评论数量（默认: 100）'
    )
    parser.add_argument(
        '--max-workers',
        type=int,
        default=4,
        help='最大线程数（默认: 4，建议不超过8）'
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='试运行模式，不实际修改数据'
    )
    parser.add_argument(
        '--no-dry-run',
        action='store_true',
        help='正式运行模式，实际修改数据'
    )
    parser.add_argument(
        '--log-mode',
        action='store_true',
        help='启用日志模式（适用于后台运行）'
    )

    args = parser.parse_args()

    # 验证线程数
    if args.max_workers < 1 or args.max_workers > 16:
        if args.log_mode:
            print("❌ 线程数必须在 1-16 之间")
        else:
            console.print("❌ 线程数必须在 1-16 之间", style="red")
        return

    # 确定是否为试运行模式
    if args.no_dry_run:
        dry_run = False
        if args.log_mode:
            print("⚠️ 警告：即将在正式模式下运行脚本！")
            print("这将会实际修改数据库中的评论状态")
            print(f"将使用 {args.max_workers} 个线程并行处理")
        else:
            console.print("⚠️ 警告：即将在正式模式下运行脚本！", style="bold red")
            console.print("这将会实际修改数据库中的评论状态")
            console.print(f"将使用 {args.max_workers} 个线程并行处理")
    else:
        dry_run = True

    # 自动检测是否在非交互式环境中运行
    if not args.log_mode and not sys.stdout.isatty():
        args.log_mode = True
        if not args.no_dry_run:  # 只在非正式模式下提示
            print("检测到非交互式环境，自动启用日志模式")

    script = HistoricalCommentReauditScript(
        batch_size=args.batch_size,
        dry_run=dry_run,
        max_workers=args.max_workers,
        log_mode=args.log_mode
    )

    try:
        script.run()
    except KeyboardInterrupt:
        if args.log_mode:
            print("脚本被用户中断")
        else:
            console.print("\n⚠️ 脚本被用户中断", style="yellow")
    except Exception as e:
        if args.log_mode:
            print(f"脚本执行过程中发生错误: {str(e)}")
        else:
            console.print(f"❌ 脚本执行过程中发生错误: {str(e)}", style="red")
        raise


if __name__ == '__main__':
    from app import create_app

    app = create_app()

    with app.app_context():
        main()
