#!/usr/bin/env python3
import os
import sys
from rich.console import Console

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())
from app.utils import check_simple_text


def main():
    """主函数"""
    console = Console()

    from app.models import db
    from app.models.comment import Comment

    try:
        limit = 1000
        count = 0
        last_id = 0

        while True:
            # 基于主键分页查询，获取ID大于last_id的limit条记录
            comments = Comment.query.filter(Comment.id > last_id).order_by(Comment.id.asc()).limit(limit).all()

            # 如果没有获取到数据，说明已经处理完所有记录，退出循环
            if not comments:
                break

            for comment in comments:
                if comment.is_simple is not None:
                    continue
                if check_simple_text(comment.content):
                    comment.is_simple = True
                    count += 1
                    console.print(f'\nfind simple text({comment.id}): {comment.content}')
                else:
                    comment.is_simple = False

            # 更新last_id为当前批次的最后一条记录的ID
            last_id = comments[-1].id
            db.session.commit()

        console.print(f"\n[green]修复完成[/green]共有 {count} 条极简评论")

    except KeyboardInterrupt:
        console.print("\n操作已取消")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]错误: {str(e)}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    from app import create_app

    app = create_app()

    with app.app_context():
        main()
