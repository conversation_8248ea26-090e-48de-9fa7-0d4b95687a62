#!/usr/bin/env python3
import os
import sys
from os.path import exists

from rich.console import Console

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    """主函数"""
    console = Console()

    try:
        from app.cache.comment_cache import CommentListCache, Comment
        from app.common.constants import Language
        from app import redis

        # 获取所有符合 comment_list: 模式的键
        pattern = "comment_list:*"
        keys = redis.keys(pattern)
        console.print(f'共查到列表缓存 {len(keys)} 条')

        fixed_count = 0
        for _key in keys:
            key = _key.decode()
            if not redis.exists(key):
                console.print(f'[yellow]Skipping key {key} as it does not exist[/yellow]')
                continue

            ttl = redis.ttl(key)
            # 从键名解析参数来重新构建 CommentListCache 对象
            parts = key.split(':')

            # 根据键的格式确定对应的参数
            if not len(parts) == 6 and not len(parts) == 4:
                # 一级评论：comment_list:root:{business}:{business_id}:{lang}:{sort_type}
                # 回复： comment_list:reply:{root_id}:{sort_type}
                console.print(f"[yellow]Skipping key with unexpected format: {key}[/yellow]")
                continue

            # 尝试重新加载
            try:
                if len(parts) == 6:
                    business = parts[2]
                    if (not business.startswith('Business.')) and ttl >= 0:
                        continue

                    # 删除现有的键
                    redis.delete(key)
                    console.print(f"Deleted cache key {key} with TTL {ttl}")

                    business = 'COIN'
                    business_id = parts[3]
                    lang = parts[4]
                    sort_type = parts[5]

                    # 假设 CommentListCache 具有相应的参数构造函数
                    cache = CommentListCache(
                        business=Comment.Business[business],
                        business_id=business_id,
                        lang=Language[lang],
                        sort_type=Comment.SortType[sort_type]
                    )

                else:
                    if ttl >= 0:
                        continue

                    # 删除现有的键
                    redis.delete(key)
                    console.print(f"Deleted cache key {key} with TTL {ttl}")

                    root_id = parts[2]
                    sort_type = parts[3]

                    # 假设 CommentListCache 具有相应的参数构造函数
                    cache = CommentListCache(
                        root_id=root_id,
                        sort_type=Comment.SortType[sort_type]
                    )

                cache.reload()
                console.print(f"Reloaded cache for {cache.key}")
                fixed_count += 1
            except Exception as e:
                console.print(f"[yellow]Failed to reload {key}: {e}[/yellow]")

        console.print(f"\n[green]修复完成，共修复 {fixed_count} 条记录[/green]")

    except KeyboardInterrupt:
        console.print("\n操作已取消")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]错误: {str(e)}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    app.testing = True
    
    with app.app_context():
        main()

