#!/usr/bin/env python3
import os
import sys
from rich.console import Console
import csv
from collections import defaultdict

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    """主函数"""
    console = Console()

    from app.models import db
    from app.models.comment import Comment
    from app.common.constants import Language
    from app.cache.comment_cache import CommentCache, CommentListCache

    try:
        # 创建用户统计数据结构
        user_stats = defaultdict(lambda: {"root_count": 0, "reply_count": 0})
        
        # 查找所有符合条件的一级评论
        last_id = 0
        limit = 1000
        fixed_count = 0

        while root_comments := Comment.query.filter(
            Comment.lang == Language.EN_US.name,
            Comment.detected_lang != Language.EN_US.name,
            Comment.is_simple == False,
            Comment.root_id.is_(None),  # 一级评论
            Comment.id > last_id
        ).order_by(Comment.id.asc()).limit(limit).all():
            
            for root_comment in root_comments:
                # 记录用户统计
                user_stats[root_comment.user_id]["root_count"] += 1
                detected_lang = root_comment.detected_lang

                if detected_lang is None:
                    continue

                # 保存原始语言，用于更新缓存
                original_lang = root_comment.lang
                
                # 更新一级评论语区
                root_comment.lang = detected_lang
                fixed_count += 1
                
                # 查找并更新所有回复
                replies = Comment.query.filter(
                    Comment.root_id == root_comment.id
                ).all()
                
                for reply in replies:
                    # 记录用户统计
                    user_stats[reply.user_id]["reply_count"] += 1
                    
                    # 更新回复语区
                    reply.lang = detected_lang
                    fixed_count += 1
                
                console.print(f"已修复评论 {root_comment.id} 及其 {len(replies)} 条回复的语区: {Language.EN_US.name} -> {detected_lang}")
            
            # 提交更改
            db.session.commit()
            
            # 更新缓存
            for root_comment in root_comments:
                # 更新评论缓存
                comment_cache = CommentCache(root_comment.id)
                if comment_cache.exists():
                    comment_cache.save_comment(root_comment)
                
                # 从原语言列表缓存中移除
                old_list_cache = CommentListCache(
                    business=root_comment.business,
                    business_id=root_comment.business_id,
                    lang=Language.EN_US,
                    sort_type=Comment.SortType.HOT
                )
                if old_list_cache.exists():
                    old_list_cache.remove_comment(root_comment.id)
                
                old_list_cache = CommentListCache(
                    business=root_comment.business,
                    business_id=root_comment.business_id,
                    lang=Language.EN_US,
                    sort_type=Comment.SortType.NEW
                )
                if old_list_cache.exists():
                    old_list_cache.remove_comment(root_comment.id)
                
                old_list_cache = CommentListCache(
                    business=root_comment.business,
                    business_id=root_comment.business_id,
                    lang=Language.EN_US,
                    sort_type=Comment.SortType.TOP
                )
                if old_list_cache.exists():
                    old_list_cache.remove_comment(root_comment.id)
                
                # 添加到新语言列表缓存
                new_list_cache = CommentListCache(
                    business=root_comment.business,
                    business_id=root_comment.business_id,
                    lang=root_comment.lang,
                    sort_type=Comment.SortType.HOT
                )
                if new_list_cache.exists():
                    new_list_cache.add_comment(root_comment)
                
                new_list_cache = CommentListCache(
                    business=root_comment.business,
                    business_id=root_comment.business_id,
                    lang=root_comment.lang,
                    sort_type=Comment.SortType.NEW
                )
                if new_list_cache.exists():
                    new_list_cache.add_comment(root_comment)
                
                new_list_cache = CommentListCache(
                    business=root_comment.business,
                    business_id=root_comment.business_id,
                    lang=root_comment.lang,
                    sort_type=Comment.SortType.TOP
                )
                if new_list_cache.exists():
                    new_list_cache.add_comment(root_comment)
                
                # 更新回复的缓存
                for reply in Comment.query.filter(Comment.root_id == root_comment.id).all():
                    reply_cache = CommentCache(reply.id)
                    if reply_cache.exists():
                        reply_cache.save_comment(reply)
                
                # 更新回复列表缓存
                reply_list_cache = CommentListCache(
                    root_id=root_comment.id,
                    sort_type=Comment.SortType.TOP
                )
                if reply_list_cache.exists():
                    reply_list_cache.reload()
            
            last_id = root_comments[-1].id
            
            if not root_comments:
                break

        # 创建CSV文件记录受影响的用户
        csv_file = open('affected_users_lang_isolation.csv', 'w', newline='')
        csv_writer = csv.writer(csv_file)
        csv_writer.writerow(['user_id', 'root_comments', 'replies'])

        for user_id, stats in user_stats.items():
            csv_writer.writerow([
                user_id,
                stats["root_count"],
                stats["reply_count"]
            ])
        
        # 关闭CSV文件
        csv_file.close()
        
        # 输出统计信息
        console.print(f"\n[green]修复完成[/green]")
        console.print(f"共修复 {fixed_count} 条评论")
        console.print(f"受影响用户数: {len(user_stats)}")
        console.print(f"用户统计已保存到 affected_users_lang_isolation.csv")

    except KeyboardInterrupt:
        console.print("\n操作已取消")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]错误: {str(e)}[/red]")
        console.print_exception(show_locals=True)
        sys.exit(1)


if __name__ == '__main__':
    from app import create_app

    app = create_app()

    with app.app_context():
        main()
