#!/usr/bin/env python3
import os
import sys
import hashlib
import time
from rich.console import Console

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())
from app.utils.text import sanitize


def main():
    """主函数"""
    console = Console()
    start_time = time.time()

    from app.models import db
    from app.models.comment import Comment

    try:
        limit = 1000
        count = 0
        last_id = 0
        query_time_total = 0
        hash_time_total = 0
        commit_time_total = 0

        console.print("[yellow]开始处理评论内容哈希...[/yellow]")

        while True:
            query_start = time.time()
            # 基于主键分页查询，获取ID大于last_id的limit条记录，并且只选择content_hash为空的记录
            comments = Comment.query.filter(Comment.id > last_id, Comment.content_hash == None).order_by(Comment.id.asc()).limit(limit).all()
            query_time = time.time() - query_start
            query_time_total += query_time

            # 如果没有获取到数据，说明已经处理完所有记录，退出循环
            if not comments:
                break

            console.print(f"[blue]查询到 {len(comments)} 条记录，查询耗时: {query_time:.2f}秒[/blue]")

            hash_start = time.time()
            for comment in comments:
                if comment.content_hash is not None:
                    continue
                # 计算内容哈希
                content_hash = hashlib.md5(comment.content.encode('utf-8')).hexdigest()
                comment.content_hash = content_hash
                count += 1

            hash_time = time.time() - hash_start
            hash_time_total += hash_time
            console.print(f"[cyan]处理 {len(comments)} 条评论哈希值，耗时: {hash_time:.2f}秒[/cyan]")

            # 更新last_id为当前批次的最后一条记录的ID
            last_id = comments[-1].id

            commit_start = time.time()
            db.session.commit()
            commit_time = time.time() - commit_start
            commit_time_total += commit_time

            console.print(f"[magenta]提交批次数据到数据库，耗时: {commit_time:.2f}秒[/magenta]")
            console.print(f"[green]已处理 {count} 条评论[/green]")

        total_time = time.time() - start_time
        console.print(f"\n[green]修复完成[/green]共更新了 {count} 条评论的内容哈希")
        console.print(f"总耗时: {total_time:.2f}秒")
        console.print(f"查询总耗时: {query_time_total:.2f}秒 ({(query_time_total/total_time*100):.1f}%)")
        console.print(f"哈希计算总耗时: {hash_time_total:.2f}秒 ({(hash_time_total/total_time*100):.1f}%)")
        console.print(f"数据库提交总耗时: {commit_time_total:.2f}秒 ({(commit_time_total/total_time*100):.1f}%)")

    except KeyboardInterrupt:
        console.print("\n操作已取消")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]错误: {str(e)}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    from app import create_app

    app = create_app()

    with app.app_context():
        main()
