#!/usr/bin/env python3
import os
import sys
from rich.console import Console
from sqlalchemy import distinct

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


def main():
    """主函数"""
    console = Console()

    from app.models import db
    from app.models.comment import Comment
    from app.cache.comment_cache import CommentListCache
    from app.common.constants import Language

    business_id_list = [row[0] for row in db.session.query(distinct(Comment.business_id)).all()]

    count = 0
    # 对每个组合的三种排序类型都 reload
    for business_id in business_id_list:
        for lang in Language:
            # 只对非空的 business_id 和 lang 进行处理
            if not business_id or not lang:
                continue
            for sort_type in [Comment.SortType.HOT, Comment.SortType.NEW, Comment.SortType.TOP]:
                console.print(f'Reloading comment list cache for business_id: {business_id}, lang: {lang}, sort_type: {sort_type}')
                count += 1
                clc = CommentListCache(Comment.Business.COIN, business_id, lang, sort_type)
                clc.reload()

    console.print(f'<red>共刷新列表缓存：{count}</red>')


if __name__ == '__main__':
    from app import create_app

    app = create_app()

    with app.app_context():
        main()
