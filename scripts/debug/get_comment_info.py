#!/usr/bin/env python3
import os
import sys

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

import argparse

from rich.console import Console
from rich.table import Table
from app.common.constants import Language
from app.models.comment import Comment
from enum import Enum


def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='评论信息查询工具')
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument(
        '-cid', '--comment_id',
        type=int,
        help='评论ID'
    )
    group.add_argument(
        '-bid', '--business_id',
        type=str,
        help='业务ID，需要同时指定语言和排序方式'
    )
    parser.add_argument(
        '-b', '--business',
        type=str,
        default='COIN',
        help='业务类型，默认为COIN'
    )
    parser.add_argument(
        '-l', '--lang',
        type=str,
        default='EN_US',
        help='语言(与business_id一起使用)'
    )
    parser.add_argument(
        '-s', '--sort_type',
        type=str,
        choices=['NEW', 'TOP', 'HOT'],
        default='NEW',
        help='排序方式(与business_id一起使用)'
    )
    return parser.parse_args()


def print_comment_info(comment_id: int, console: Console):
    """打印评论详细信息"""
    # 从数据库获取评论
    from app.cache.comment_cache import CommentCache

    db_comment = Comment.query.get(comment_id)
    if not db_comment:
        console.print(f"[red]评论ID {comment_id} 在数据库中不存在[/red]")
        return

    # 从缓存获取评论
    comment_cache = CommentCache(comment_id)
    if comment_cache is not None:
        cache_data = comment_cache.read()

    # 动态获取 Comment 类的字段
    fields = [column.name for column in Comment.__table__.columns]

    # 创建表格显示比较结果
    table = Table(title=f"评论 {comment_id} 信息对比")
    table.add_column("字段", style="cyan")
    table.add_column("数据库值", style="green")
    table.add_column("缓存值", style="yellow")

    # 添加主要字段比较
    for field in fields:
        # 获取字段值，对于enum类型获取name
        db_value = getattr(db_comment, field, '')
        if isinstance(db_value, Enum):
            db_value = db_value.name
        db_value = str(db_value)        # 如果缓存中不存在该字段，标明其不存在
        if comment_cache is None:
            cache_value = f"[red]不存在[/red]"
        else:
            cache_value = str(cache_data.get(field, '')) if cache_data else ''

        table.add_row(field, db_value, cache_value)

    console.print(table)

    # 如果是一级评论，获取回复列表
    if not db_comment.root_id:
        replies = Comment.query.filter_by(root_id=comment_id).all()
        if replies:
            console.print("\n[bold]回复列表:[/bold]")
            for reply in replies:
                console.print(f"回复ID: {reply.id}, 内容: {reply.content}")


def compare_comment_lists(business: str, business_id: str, lang: str, sort_type: str, console: Console):
    from app.cache.comment_cache import CommentListCache

    """比较数据库和缓存中的评论列表"""
    # 从数据库获取评论ID列表
    db_comments = Comment.get_comments(
        business=business,
        business_id=business_id,
        lang=Language[lang],
        sort_type=Comment.SortType[sort_type],
        limit=1000,
    )
    db_ids = [str(c.id) for c in db_comments]

    # 从缓存获取评论ID列表
    comment_list_cache = CommentListCache(
        business=Comment.Business[business],  # 使用默认值
        business_id=business_id,
        lang=Language[lang],
        sort_type=Comment.SortType[sort_type]
    )
    cache_data = comment_list_cache.zrev_range(0, -1, withscores=True)
    cache_ids = [id_ for id_, _ in cache_data]
    cache_scores = {id_: score for id_, score in cache_data}

    # 比较差异
    only_in_db = set(db_ids) - set(cache_ids)
    only_in_cache = set(cache_ids) - set(db_ids)

    console.print(f"\n[bold]评论列表比较 (business_id={business_id}, lang={lang}, sort={sort_type})[/bold]")
    console.print(f"数据库评论数: {len(db_ids)}")
    console.print(f"缓存评论数: {len(cache_ids)}")

    if only_in_db:
        console.print("\n[red]仅在数据库中存在的评论ID:[/red]")
        console.print(", ".join(only_in_db))

    if only_in_cache:
        console.print("\n[red]仅在缓存中存在的评论ID:[/red]")
        console.print(", ".join(only_in_cache))

    # 比较顺序差异
    # 找出数据库和缓存中都存在的ID
    common_ids = set(db_ids) & set(cache_ids)

    if common_ids:
        # 创建两个字典，key是评论ID，value是它在列表中的位置
        db_order = {id_: idx for idx, id_ in enumerate(db_ids) if id_ in common_ids}
        cache_order = {id_: idx for idx, id_ in enumerate(cache_ids) if id_ in common_ids}

        # 检查每个共同ID，如果在两个列表中的位置不同，就记录下来
        order_diff = [(id_, db_order[id_], cache_order[id_])
                      for id_ in common_ids
                      if db_order[id_] != cache_order[id_]]

        # 如果发现有顺序不一致的评论，打印出来
        if order_diff:
            console.print("\n[yellow]顺序不一致的评论ID (ID, DB位置, Cache位置, DB分数, Cache分数):[/yellow]")
            for id_, db_pos, cache_pos in order_diff:
                # 获取数据库中的评论对象及其分数
                db_comment = next((c for c in db_comments if str(c.id) == id_), None)
                db_score = db_comment.get_score(Comment.SortType[sort_type]) if db_comment else None

                # 从之前获取的cache_scores中直接获取分数
                cache_score = cache_scores.get(id_)

                console.print(
                    f"{id_}: 位置 {db_pos} -> {cache_pos}, "
                    f"分数 {db_score or 'N/A'} -> {cache_score or 'N/A'}"
                )


def main():
    """主函数"""
    console = Console()
    
    try:
        args = parse_args()
        
        if args.comment_id:
            print_comment_info(args.comment_id, console)
        elif args.business_id:
            if not (args.lang and args.sort_type):
                console.print("[red]使用business_id参数时必须同时指定lang和sort-type[/red]")
                sys.exit(1)
            compare_comment_lists(args.business, args.business_id, args.lang, args.sort_type, console)
            
    except KeyboardInterrupt:
        console.print("\n操作已取消")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]错误: {str(e)}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    app.testing = True
    
    with app.app_context():
        main() 