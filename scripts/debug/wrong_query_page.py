#!/usr/bin/env python3
import os
import sys
import argparse

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)

if abspath.endswith('debug'):
    os.chdir('../../')
    sys.path.append(os.getcwd())

from app.models.comment import Comment

def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='评论信息查询工具')
    parser.add_argument(
        '-bid', '--business_id',
        type=str,
        help='业务ID，需要同时指定语言和排序方式'
    )
    parser.add_argument(
        '-b', '--business',
        type=str,
        default='COIN',
        help='业务类型，默认为COIN'
    )
    parser.add_argument(
        '-l', '--lang',
        type=str,
        default='EN_US',
        help='语言(与business_id一起使用)'
    )
    parser.add_argument(
        '-s', '--sort_type',
        type=str,
        choices=['NEW', 'TOP', 'HOT'],
        default='NEW',
        help='排序方式(与business_id一起使用)'
    )
    parser.add_argument(
        '-ls', '--last_score',
        type=float,
        help='上一页最后一条分数，需要同时last_id'
    )
    parser.add_argument(
        '-li', '--last_id',
        type=int,
        help='上一页最后一条id，需要同时last_score'
    )
    parser.add_argument(
        '-lm', '--limit',
        type=int,
        default=30,
        help='总数'
    )
    parser.add_argument(
        '-ui', '--user_id',
        type=int,
        help='用户id'
    )

    return parser.parse_args()


def _query_comments(*, sort_type: Comment.SortType, query_params: dict,
                    last_score: float = None, last_id: int = None,
                    limit: int = 20, user_id: int = None) -> list:
    """通用的评论查询方法
    Args:
        sort_type: 排序类型(必需)
        query_params: 查询参数,用于构造缓存和数据库查询
        last_score: 上一页最后一条评论的分数
        last_id: 上一页最后一条评论的ID
        limit: 每页数量
        user_id: 当前用户ID
    """
    # 1. 构造并获取缓存
    if (last_score is not None and last_id is None) or (last_score is None and last_id is not None):
        print('last_score 和 last_id 必须同时存在或同时不存在')

    from app.cache.comment_cache import CommentCache, CommentListCache, UserPendingCommentsCache

    cache = CommentListCache(sort_type=sort_type, **query_params)
    if not cache.exists():
        cache.reload()

    # 2. 获取分页数据
    comment_ids = cache.paginate(last_score=last_score, last_id=last_id, limit=limit)
    print(f'缓存中获取分页数据 {len(comment_ids)} 条，最后一条 id: {comment_ids[-1] if len(comment_ids)>0 else "N/A"}')

    comments_in_cache: list = CommentCache.get_comments_from_ids(comment_ids)
    # 同分时，comment_id是不按顺序排列的，这里要按id倒序排一下
    comments_in_cache.sort(key=lambda _cache: (_cache.get_score(sort_type), _cache.id), reverse=True)
    cache_len = len(comments_in_cache)
    print(f'重排序之后，最后一条 id: {comments_in_cache[-1].id if len(comments_in_cache)>0 else "N/A"}, '
          f'分数：{Comment.get_score(comments_in_cache[-1], sort_type) if len(comments_in_cache)>0 else "N/A"}')

    # 3. 如果缓存数据不足，从数据库补充
    comments_in_db = []
    if cache_len < limit:
        if cache_len > 0:
            last_comment_in_cache = comments_in_cache[-1]
            query_after_score = last_comment_in_cache.get_score(sort_type)
            query_after_id = last_comment_in_cache.id
        else:
            query_after_score = last_score
            query_after_id = last_id

        # 从数据库查询补充数据
        print(f'从数据库补充数据，last_score:{query_after_score}, last_id:{query_after_id}, limit:{limit - cache_len}')
        comments_in_db = Comment.get_comments(
            sort_type=sort_type,
            last_score=query_after_score,
            last_id=query_after_id,
            limit=limit - cache_len,
            **query_params
        )
        if len(comments_in_db) > 0:
            print(f'commends_in_db, 数量:{len(comments_in_db)}, 第一条：{comments_in_db[0].id}，最后一条：{comments_in_db[-1].id}')
        else:
            print(f'commends_in_db, 数量:{len(comments_in_db)}')

    # 4. 补充用户自己刚发布未审核评论
    pending_comments = []
    if not last_id and user_id:
        pending_cache = UserPendingCommentsCache(user_id)
        if not pending_cache.exists():
            pending_cache.reload()
        pending_comments = pending_cache.query_comments(
            sort_type=sort_type,
            **query_params
        )

    return pending_comments + comments_in_cache + comments_in_db


def main():
    """主函数"""
    from app.common.constants import Language
    try:
        args = parse_args()
        
        if not (args.lang and args.sort_type):
            print("使用business_id参数时必须同时指定lang和sort-type")
            sys.exit(1)
        query_params = {
            'business': Comment.Business[args.business],
            'business_id': args.business_id,
            'lang': Language[args.lang]
        }
        _query_comments(
            sort_type=Comment.SortType[args.sort_type],
            query_params=query_params,
            last_score=args.last_score,
            last_id=args.last_id,
            limit=args.limit,
            user_id=args.user_id
        )
            
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(0)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    app.testing = True
    
    with app.app_context():
        main()
