# 评论模拟器设计文档

## 1. 功能概述
创建一个评论模拟器,通过模拟多个用户角色在不同币种下的评论互动,帮助测试和验证评论系统的功能。

## 2. 用户角色设计
```python
class UserRole(Enum):
ENTHUSIAST = "enthusiast" # 热情用户：经常发表积极评论,频繁点赞
CRITIC = "critic" # 批评者：倾向于发表负面评论,经常点踩
QUESTIONER = "questioner" # 提问者：主要提出问题,较少评价
INFORMER = "informer" # 信息者：分享市场/技术信息,中立态度
CASUAL = "casual" # 随意者：行为模式随机
```


## 3. 评论内容生成
使用 Ollama 模型生成评论内容,prompt 模板如下:
```python
PROMPTS = {
"enthusiast": """
你是一位加密货币市场的乐观投资者。请针对{coin}用{lang}写一段不超过100字的评论。
评论要积极正面,但不要过分夸张。可以谈论技术进展、市场前景或最新发展。
""",
"critic": """
你是一位谨慎的加密货币观察者。请针对{coin}用{lang}写一段不超过100字的评论。
评论要客观理性地指出问题,但不要攻击或贬低。可以讨论风险、挑战或需要改进的地方。
""",
"questioner": """
你是一位加密货币市场的新手。请针对{coin}用{lang}写一段不超过100字的提问。
问题要具体且有建设性,表现出学习意愿。可以询问技术细节、应用场景或发展方向。
""",
"informer": """
你是一位加密货币行业的研究者。请针对{coin}用{lang}写一段不超过100字的分析。
内容要客观专业,包含数据或事实支持。可以分析市场数据、技术创新或行业动态。
""",
"casual": """
你是一位普通的加密货币用户。请针对{coin}用{lang}写一段不超过100字的评论。
内容可以是任何相关话题,语气要自然。可以分享个人经历、想法或观察。
"""
}
```


## 4. 命令行参数
```bash
bash
python -m scripts.interactive.comment_simulator \
--duration 3600 # 运行时长(秒),默认1小时
--max-comments 1000 # 最大评论数,默认1000
--languages zh,en # 支持的语言,默认中英文
--coins BTC:1,ETH:2 # 要评论的币种,默认BTC/ETH
--users 10 # 模拟用户数量,默认10个
```


## 5. 运行状态展示
使用 rich 库展示实时状态:
```
评论模拟器运行中... (按 Ctrl+C 终止)
运行时间: 00:15:23 / 01:00:00
评论数量: 234 / 1000
用户活动:
🟢 活跃用户: 7/10
📝 评论总数: 234
↩️ 回复数量: 89
👍 点赞数量: 156
👎 点踩数量: 43
最近活动:
[12:01:23] 用户23(enthusiast) 评论了 BTC
[12:01:25] 用户45(critic) 回复了 评论#123
[12:01:28] 用户12(casual) 点赞了 评论#89
```

## 6. 主要类设计
```python
class CommentSimulator:
"""评论模拟器主类"""
def init(self, config: dict):
self.config = config
self.users = self.init_users()
self.stats = SimulationStats()
self.content_generator = ContentGenerator()
def run(self):
"""运行模拟器"""
def simulate_user_action(self, user):
"""模拟单个用户行为"""
class SimulatedUser:
"""模拟用户类"""
def init(self, user_id: int, role: UserRole):
self.user_id = user_id
self.role = role
def take_action(self, simulator):
"""执行一次随机行为"""
class ContentGenerator:
"""评论内容生成器"""
def init(self):
self.ollama = self.init_ollama()
def generate(self, role: UserRole, coin: str, lang: str) -> str:
"""生成评论内容"""
class SimulationStats:
"""统计信息收集器"""
def init(self):
self.start_time = time.time()
self.comments = 0
self.replies = 0
self.upvotes = 0
self.downvotes = 0
```
