from enum import Enum
from dataclasses import dataclass
from typing import Protocol, Dict, Optional
from werkzeug.test import TestResponse

class ActionType(Enum):
    COMMENT = "comment"
    REPLY = "reply"
    UPVOTE = "upvote"
    DOWNVOTE = "downvote"

@dataclass
class ActionResult:
    """动作执行结果"""
    type: ActionType
    success: bool
    message: str
    target_id: Optional[str] = None  # 评论ID或币种代码

@dataclass
class ActionResponse:
    """API响应包装器"""
    result: ActionResult
    response: TestResponse  # 用于在 take_action 中检查请求是否成功

class ICommentSimulator(Protocol):
    """评论模拟器接口"""
    config: Dict
    def run(self) -> None: ... 