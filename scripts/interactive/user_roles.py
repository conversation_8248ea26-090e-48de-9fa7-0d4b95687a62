from enum import Enum
from typing import List
import random

class UserRole(Enum):
    ENTHUSIAST = "enthusiast"
    CRITIC = "critic"
    QUESTIONER = "questioner"
    INFORMER = "informer"
    CASUAL = "casual"

    @classmethod
    def random_role(cls) -> 'UserRole':
        return random.choice(list(cls))

    def get_action_weights(self) -> dict:
        """获取该角色的行为权重"""
        WEIGHTS = {
            UserRole.ENTHUSIAST: {'comment': 0.3, 'reply': 0.3, 'upvote': 0.35, 'downvote': 0.05},
            UserRole.CRITIC: {'comment': 0.3, 'reply': 0.3, 'upvote': 0.05, 'downvote': 0.35},
            UserRole.QUESTIONER: {'comment': 0.4, 'reply': 0.4, 'upvote': 0.1, 'downvote': 0.1},
            UserRole.INFORMER: {'comment': 0.4, 'reply': 0.3, 'upvote': 0.15, 'downvote': 0.15},
            UserRole.CASUAL: {'comment': 0.25, 'reply': 0.25, 'upvote': 0.25, 'downvote': 0.25},
        }
        return WEIGHTS[self] 