import time
from dataclasses import dataclass
from typing import List, Dict
from datetime import datetime
from scripts.interactive.types import ActionType, ActionResult

@dataclass
class Activity:
    timestamp: float
    user_id: int
    role: str
    action: str
    target: str

class SimulationStats:
    def __init__(self, max_activities: int = 100):
        self.start_time = time.time()
        self.comments = 0
        self.replies = 0
        self.upvotes = 0
        self.downvotes = 0
        self.max_activities = max_activities
        self.recent_activities: List[Activity] = []
        self.active_users: Dict[int, float] = {}  # user_id -> last_active_time

    def record_activity(self, user_id: int, role: str, result: ActionResult):
        """记录一次活动"""
        self.recent_activities.append(Activity(
            timestamp=time.time(),
            user_id=user_id,
            role=role,
            action=result.message,
            target=result.target_id or ''  # 使用 target_id 作为目标，如果为 None 则使用空字符串
        ))
        self.recent_activities = self.recent_activities[-5:]
        
        # 根据动作类型更新统计数据
        if result.type == ActionType.COMMENT:
            self.comments += 1
        elif result.type == ActionType.REPLY:
            self.replies += 1
        elif result.type == ActionType.UPVOTE:
            self.upvotes += 1
        elif result.type == ActionType.DOWNVOTE:
            self.downvotes += 1


    def get_active_users_count(self, within_seconds: int = 300) -> int:
        """获取最近活跃用户数"""
        now = time.time()
        return sum(1 for last_active in self.active_users.values()
                  if now - last_active <= within_seconds) 