#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# 将项目根目录添加到 Python 路径
abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())

import argparse
import yaml
from typing import Dict, Any

from rich.console import Console
from app.common.constants import Language

from scripts.interactive.simulator import CommentSimulator

def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='币种评论模拟器')
    parser.add_argument(
        '-c', '--config',
        type=str,
        default=str(Path(__file__).parent / 'config.yaml'),
        help='配置文件路径'
    )
    parser.add_argument(
        '-d', '--duration',
        type=int,
        help='运行时长(秒)'
    )
    parser.add_argument(
        '-m', '--max-comments',
        type=int,
        help='最大评论数'
    )
    parser.add_argument(
        '-u', '--users',
        type=int,
        help='模拟用户数量'
    )
    parser.add_argument(
        '-i', '--interval',
        type=float,
        help='操作间隔(秒)'
    )
    parser.add_argument(
        '-l', '--languages',
        type=str,
        help='支持的语言,逗号分隔'
    )
    parser.add_argument(
        '--coins',
        type=str,
        help='要评论的币种,逗号分隔'
    )
    return parser.parse_args()

def load_config(args: argparse.Namespace) -> Dict[str, Any]:
    """加载配置"""
    # 加载配置文件
    config_path = Path(args.config)
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
    with open(config_path) as f:
        config = yaml.safe_load(f)['simulator']
        
    # 命令行参数覆盖配置文件
    if args.duration is not None:
        config['duration'] = args.duration
    if args.max_comments is not None:
        config['max_comments'] = args.max_comments
    if args.users is not None:
        config['users'] = args.users
    if args.interval is not None:
        config['interval'] = args.interval
    if args.languages is not None:
        config['languages'] = [
            Language[lang.strip().upper()]
            for lang in args.languages.split(',')
        ]
    if args.coins is not None:
        # 解析命令行的币种参数，格式: "BTC:1,ETH:2"
        config['coins'] = [
            {'id': coin_pair.split(':')[1].strip(),
             'code': coin_pair.split(':')[0].strip().upper()}
            for coin_pair in args.coins.split(',')
        ]
        
    return config

def main():
    """主函数"""
    console = Console()
    
    try:
        # 解析参数和配置
        args = parse_args()
        config = load_config(args)
        
        # 创建并运行模拟器
        simulator = CommentSimulator(config)
        simulator.run()
        
    except KeyboardInterrupt:
        console.print("\n模拟已手动终止")
        sys.exit(0)
    except Exception as e:
        raise e
        console.print(f"[red]错误: {e}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    app.testing = True
    
    with app.app_context():
        main() 