import time
import random
from typing import List, Dict
from rich.live import Live
from rich.table import Table
from rich.console import Console
from datetime import datetime, timedelta

from app.common.constants import Language
from app.models.comment import Comment

from .user_roles import UserRole
from .simulated_user import SimulatedUser
from .content_generator import ContentGenerator
from .stats import SimulationStats

class CommentSimulator:
    """评论模拟器主类"""
    def __init__(self, config: dict):
        self.config = self._init_config(config)
        self.users = self._init_users()
        self.stats = SimulationStats()
        self.content_generator = ContentGenerator(
            config = self.config.get('ollama', None)
        )
        self.console = Console()

    def _init_config(self, config: dict) -> dict:
        """初始化配置"""
        default_config = {
            'duration': 3600,  # 1小时
            'max_comments': 1000,
            'languages': [Language.ZH_HANS_CN, Language.EN_US],
            'coins': ['BTC', 'ETH'],
            'users': 10,
            'interval': 1.0,  # 每次操作间隔(秒)
        }
        return {**default_config, **config}

    def _init_users(self) -> List[SimulatedUser]:
        """初始化模拟用户"""
        return [
            SimulatedUser(
                user_id=10000 + i,
                role=UserRole.random_role()
            )
            for i in range(self.config['users'])
        ]

    def _generate_status_table(self) -> Table:
        """生成状态表格"""
        table = Table(title="评论模拟器状态")
        
        # 添加运行时间信息
        elapsed = time.time() - self.stats.start_time
        duration = self.config['duration']
        table.add_row(
            f"运行时间: {timedelta(seconds=int(elapsed))} / {timedelta(seconds=duration)}"
        )
        table.add_row(
            f"评论数量: {self.stats.comments + self.stats.replies} / {self.config['max_comments']}"
        )
        
        # 添加用户活动统计
        table.add_row("\n[bold]用户活动:[/bold]")
        table.add_row(f"🟢 活跃用户: {self.stats.get_active_users_count()}/{len(self.users)}")
        table.add_row(f"📝 评论总数: {self.stats.comments}")
        table.add_row(f"↩️ 回复数量: {self.stats.replies}")
        table.add_row(f"👍 点赞数量: {self.stats.upvotes}")
        table.add_row(f"👎 点踩数量: {self.stats.downvotes}")
        
        # 添加最近活动
        if self.stats.recent_activities:
            table.add_row("\n[bold]最近活动:[/bold]")
            for activity in reversed(self.stats.recent_activities[-5:]):
                timestamp = datetime.fromtimestamp(activity.timestamp)
                table.add_row(
                    f"[{timestamp.strftime('%H:%M:%S')}] "
                    f"用户{activity.user_id}({activity.role}) {activity.action}"
                )
        
        return table

    def run(self):
        """运行模拟器"""
        start_time = time.time()
        
        with Live(self._generate_status_table(), refresh_per_second=1) as live:
            try:
                while True:
                    # 检查是否达到终止条件
                    if time.time() - start_time >= self.config['duration']:
                        break
                    if self.stats.comments + self.stats.replies >= self.config['max_comments']:
                        break
                        
                    # 随机选择一个用户执行操作
                    user = random.choice(self.users)
                    result = user.take_action(self)
                    
                    if result and result.success:  # 只记录成功的操作
                        self.stats.record_activity(
                            user.user_id,
                            user.role.value,
                            result  # 直接传入 ActionResult 对象
                        )
                    
                    # 更新状态显示
                    live.update(self._generate_status_table())
                    
                    # 等待一段时间
                    time.sleep(self.config['interval'])
                    
            except KeyboardInterrupt:
                self.console.print("\n模拟已手动终止")
            
        # 打印最终统计信息
        self.console.print(self._generate_status_table()) 