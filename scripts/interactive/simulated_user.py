import random
from typing import Optional, List
from flask import current_app
import json

from app.models.comment import Comment
from app.common.constants import Language
from app.models import db

from .user_roles import UserRole
from .types import ICommentSimulator, ActionType, ActionResult, ActionResponse

class SimulatedUser:
    """模拟用户类"""
    def __init__(self, user_id: int, role: UserRole):
        self.user_id = user_id
        self.role = role
        self.client = current_app.test_client()
        self.client.testing = True
        self.URL_ROOT = '/res'
        
    def take_action(self, simulator: ICommentSimulator) -> Optional[ActionResult]:
        """执行一次随机行为"""
        weights = self.role.get_action_weights()
        action = random.choices(
            list(weights.keys()),
            list(weights.values())
        )[0]
        
        try:
            if action == 'comment':
                action_response = self._post_comment(simulator)
            elif action == 'reply':
                action_response = self._post_reply(simulator)
            elif action == 'upvote':
                action_response = self._vote_comment(simulator, is_up=True)
            elif action == 'downvote':
                action_response = self._vote_comment(simulator, is_up=False)
            else:
                return None
                
            # 检查 HTTP 响应
            if action_response.response.status_code != 200:
                return ActionResult(
                    type=action_response.result.type,
                    success=False,
                    message=f"请求失败: {action_response.response.data}",
                    target_id=action_response.result.target_id
                )
                
            return action_response.result
                
        except Exception as e:
            print(f"Action failed: {e}")
            return None

    def _get_comments(self, simulator: ICommentSimulator, 
                     coin: dict,
                     lang: Language, 
                     sort_type: Comment.SortType = Comment.SortType.HOT,
                     limit: int = 20) -> List[dict]:
        """获取评论列表"""
        response = self.client.get(
            f'{self.URL_ROOT}/comment/comment-list',
            query_string={
                'business': Comment.Business.COIN.name,
                'business_id': coin['id'],
                'business_code': coin['code'],
                'lang': lang.name,
                'sort_type': sort_type.name,
                'limit': limit
            }
        )
        if response.status_code != 200:
            print(f"Get comments failed: {response.data}")
            return []
        
        return response.json.get('data', [])

    def _post_comment(self, simulator: ICommentSimulator) -> ActionResponse:
        """发表新评论"""
        try:
            coin = random.choice(simulator.config['coins'])
            lang = random.choice(simulator.config['languages'])
            
            content = simulator.content_generator.generate(self.role, coin['code'], lang)
            if not content:
                return ActionResponse(
                    result=ActionResult(
                        type=ActionType.COMMENT,
                        success=False,
                        message="生成内容失败",
                        target_id=coin['code']
                    ),
                    response=None
                )
            
            response = self.client.post(
                f'{self.URL_ROOT}/comment',
                headers=self._get_headers(),
                json={
                    'business': Comment.Business.COIN.name,
                    'business_id': coin['id'],
                    'business_code': coin['code'],
                    'content': content,
                    'lang': lang.name
                }
            )
            
            if response.status_code != 200:
                db.session.rollback()
            
            db.session.commit()
            return ActionResponse(
                result=ActionResult(
                    type=ActionType.COMMENT,
                    success=True,
                    message=f"评论了 {coin['code']}",
                    target_id=coin['code']
                ),
                response=response
            )
        except Exception as e:
            db.session.rollback()
            raise

    def _post_reply(self, simulator: ICommentSimulator) -> ActionResponse:
        """回复评论"""
        coin = random.choice(simulator.config['coins'])
        lang = random.choice(simulator.config['languages'])
        
        # 获取评论列表
        comments = self._get_comments(simulator, coin, lang)
        if not comments:
            return ActionResponse(
                result=ActionResult(
                    type=ActionType.REPLY,
                    success=False,
                    message="没有可回复的评论",
                    target_id=coin['code']
                ),
                response=None
            )
            
        comment = random.choice(comments)
        content = simulator.content_generator.generate(self.role, coin['code'], lang)
        if not content:
            return ActionResponse(
                result=ActionResult(
                    type=ActionType.REPLY,
                    success=False,
                    message="生成内容失败",
                    target_id=str(comment['id'])
                ),
                response=None
            )
            
        response = self.client.post(
            f'{self.URL_ROOT}/comment/{comment.get("root_id", comment["id"])}/reply',
            headers=self._get_headers(),
            json={
                'business': Comment.Business.COIN.name,
                'business_id': coin['id'],
                'business_code': coin['code'],
                'content': content,
                'lang': lang.name,
                'parent_id': comment['id']
            }
        )
        return ActionResponse(
            result=ActionResult(
                type=ActionType.REPLY,
                success=True,
                message=f"回复了 评论#{comment['id']}",
                target_id=str(comment['id'])
            ),
            response=response
        )

    def _vote_comment(self, simulator: ICommentSimulator, is_up: bool) -> ActionResponse:
        """对评论投票"""
        coin = random.choice(simulator.config['coins'])
        lang = random.choice(simulator.config['languages'])
        
        comments = self._get_comments(simulator, coin, lang)
        if not comments:
            return ActionResponse(
                result=ActionResult(
                    type=ActionType.UPVOTE if is_up else ActionType.DOWNVOTE,
                    success=False,
                    message="没有可投票的评论",
                    target_id=coin['code']
                ),
                response=None
            )
            
        comment = random.choice(comments)
        response = self.client.post(
            f'{self.URL_ROOT}/comment/{comment["id"]}/vote',
            headers=self._get_headers(),
            json={'vote': 1 if is_up else -1}
        )
        
        action = "点赞" if is_up else "点踩"
        return ActionResponse(
            result=ActionResult(
                type=ActionType.UPVOTE if is_up else ActionType.DOWNVOTE,
                success=True,
                message=f"{action}了 评论#{comment['id']}",
                target_id=str(comment['id'])
            ),
            response=response
        )

    def _get_headers(self) -> dict:
        """获取请求头"""
        return {
            'User-Id': str(self.user_id),
            'User-Name': f'Simulated {self.role}',
            'Account-Name': f'sim_account({self.user_id})',
            'Avatar': 'default_avatar'
        } 
