import os
from typing import Optional
import httpx
from .user_roles import UserRole

class ContentGenerator:
    PROMPTS = {
        "enthusiast": """
        你是一位加密货币市场的乐观投资者。请针对{coin}用{lang}写一段不超过20字的评论。
        评论要积极正面,但不要过分夸张。可以谈论技术进展、市场前景或最新发展。
        """,
        "critic": """
        你是一位谨慎的加密货币观察者。请针对{coin}用{lang}写一段不超过20字的评论。
        评论要客观理性地指出问题,但不要攻击或贬低。可以讨论风险、挑战或需要改进的地方。
        """,
        "questioner": """
        你是一位加密货币市场的新手。请针对{coin}用{lang}写一段不超过20字的提问。
        问题要具体且有建设性,表现出学习意愿。可以询问技术细节、应用场景或发展方向。
        """,
        "informer": """
        你是一位加密货币行业的研究者。请针对{coin}用{lang}写一段不超过20字的分析。
        内容要客观专业,包含数据或事实支持。可以分析市场数据、技术创新或行业动态。
        """,
        "casual": """
        你是一位普通的加密货币用户。请针对{coin}用{lang}写一段不超过20字的评论。
        内容可以是任何相关话题,语气要自然。可以分享个人经历、想法或观察。
        """
    }

    def __init__(self, config:dict = None):
        self.host = (config or {}).get('host', "http://localhost:11434")
        self.model = (config or {}).get('model', "mistral")
        self.client = httpx.Client(timeout=30.0)

    def generate(self, role: UserRole, coin: str, lang: str) -> Optional[str]:
        """生成评论内容"""
        prompt = self.PROMPTS[role.value].format(coin=coin, lang=lang)
        
        try:
            response = self.client.post(
                f"{self.host}/api/generate",
                json={
                    "model": self.model,
                    "prompt": prompt,
                    "stream": False
                }
            )
            response.raise_for_status()
            return response.json()["response"].strip()
        except Exception as e:
            print(f"Content generation failed: {e}")
            return None 