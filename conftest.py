import pytest
from _pytest.monkeypatch import MonkeyPatch

from app import create_app


@pytest.fixture(scope='session', autouse=True)
def tapp():
    return create_app()


@pytest.fixture(scope='session', autouse=True)
def tcontext(tapp):
    context = tapp.app_context()
    try:
        with context:
            setup_all()
        yield context
    finally:
        with context:
            teardown_all()


def setup_all():
    print('\nsetup_all')


def teardown_all():
    print('\nteardown_all')


@pytest.fixture(scope='session')
def monkeypatch_session():
    m = MonkeyPatch()
    yield m
    m.undo()


@pytest.fixture(scope='session', autouse=False)
def tdb(tapp, monkeypatch_session):
    # monkeypatch_session.setattr(db.session, 'commit', db.session.flush)
    # return db
    pass


@pytest.fixture(scope='function', autouse=True)
def common_setup_teardown(tapp):
    context = tapp.app_context()
    try:
        with context:
            setup_one()
        yield context
    finally:
        with context:
            teardown_one()


def setup_one():
    # print('\nsetup_one')
    pass


def teardown_one():
    # print('\nteardown_one')
    # db.session.rollback()
    pass
