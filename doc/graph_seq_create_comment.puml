@startuml 用户发表评论/回复序列图

skinparam sequenceMessageAlign center

'定义参与者
actor 用户 as User
participant "前端" as Frontend
participant "评论服务(API+Business)" as CommentService
participant "异步任务服务" as Scheduler
participant "排序服务" as SortService
participant "自动筛查服务(关键词+AI)" as InitFilterService
participant "通知服务" as NotificationService
database "缓存" as Cache
database "数据库" as DB

'开始序列
User -> Frontend : 输入评论/回复内容
activate Frontend

Frontend -> CommentService : 发起创建评论请求

CommentService -> CommentService : 检查发言频率
alt 超过频率限制
    CommentService --> Frontend : 返回频率限制提示
    Frontend --> User : 显示"操作频繁，请稍后再试"
end

CommentService -> DB : 检查账户信息完整性（账号名，头像等）
alt 不完整
    CommentService --> Frontend : 返回提示信息
    Frontend --> User : 跳转至账户信息维护
end

CommentService -> DB : 检查用户是否禁言
alt 正在禁言中
    CommentService --> Frontend : 返回提示信息
    Frontend --> User : 显示"您已被禁止发言"
end

'创建评论流程
CommentService -> DB: 新建评论记录（如需，处理标签）
CommentService -> Cache: Add to UserPendingCommentsCache

'初步筛查
Scheduler -> InitFilterService : 进行关键词+AI初步筛查
InitFilterService --> Scheduler : 返回筛查结果

Scheduler -> SortService : 计算得分
alt 初筛未通过
		Scheduler -> DB : 更新评论得分和状态
		Scheduler -> Cache : Remove from UserPendingCommentsCache
		alt 如果评分排名在 1000 名以内
			Scheduler -> Cache: remove from CommentListCache
			Scheduler -> Cache : update CommentCache State
		end
		Scheduler --> NotificationService : 发送审查结果提醒
else 初筛通过
	Scheduler -> SortService : 获取排名
	Scheduler -> DB : 更新评论得分和状态
	
	group 更新评论缓存
		Scheduler -> Cache : Remove from UserPendingCommentsCache
		alt 如果评分排名在 1000 名以内
			Scheduler -> Cache: Add to CommentListCache
			Scheduler -> Cache : Add to CommentCache
		end
	end
end


Scheduler -> NotificationService : 发送@用户提醒


@enduml
