## 需求分析
除了和币种相关，本项目基本上是一个独立的面向陌生人的社交平台（或者说论坛），第一期的产品形态，参考了 reddit、twitter、抖音和一些竞品。

### 系统用例
![](https://cdn.nlark.com/yuque/__puml/67dd9cae4cccd69890a79a95c48250f0.svg)

### 功能分解
详见《[币种评论工作量](https://viabtc.yuque.com/r.d/mibe45/mzvy6b5g23bpt8aq)》

### 关键状态流转


![](https://cdn.nlark.com/yuque/__puml/e1e2f6400845d0718aed1f2811e710f6.svg)



## 数据库设计
![](https://cdn.nlark.com/yuque/__puml/a375b514c08cae8da1ceedeacf5ac1cd.svg)

### 评论（Comment）
```python
class Comment(ModelBase):
    """评论表"""
    class CommentStatus(PyEnum):
        CREATED = 'created'     # 刚创建，待审核（仅自己可见）
        READY = 'ready'         # 审核通过，可公开展示（两级审核都可以通过）
        REJECTED = 'rejected'   # 审核失败，仅自己可见（两级审核都可以拒绝）


    class SortType(PyEnum):
        HOT = 'hot'            # 热度排序  
        NEW = 'new'            # 最新排序
        TOP = 'top'            # 互动排序


    business = Column(String, nullable=False)
    content = Column(String, nullable=False)
    content_participles = Column(db.PG_JSONB)  # 使用PostgreSQL的JSONB类型存储分词结果
    user_id = Column(String, nullable=False)
    parent_id = Column(Integer, ForeignKey('comments.id'))
    root_id = Column(Integer, ForeignKey('comments.id'))
    lang = Column(String, nullable=False)
    detected_lang = Column(String)
    status = Column(db.StringEnum(CommentStatus), default=CommentStatus.CREATED, nullable=False)
    up_count = Column(Integer, default=0)
    down_count = Column(Integer, default=0)
    report_count = Column(Integer, default=0)
    hot_score = Column(Integer, default=0)
    interact_score = Column(Integer, default=0)

    # 关系
    replies = relationship("Comment", 
                         backref="parent",
                         remote_side=[id],
                         foreign_keys=[parent_id])
    root_comment = relationship("Comment",
                              remote_side=[id],
                              foreign_keys=[root_id])

    # 索引
    __table_args__ = (
        Index('ix_comments_coin_lang_hot', business, lang, hot_score.desc(), ModelBase.created_at.desc()),
        Index('ix_comments_coin_lang_top', business, lang, top_score.desc(), ModelBase.created_at.desc()),
        Index('ix_comments_coin_lang_new', business, lang, new_score.desc()),
        Index('ix_comments_root_id_created', root_id, ModelBase.created_at.desc()),
        Index('ix_comments_user_id_created', user_id, ModelBase.created_at.desc()),
    )
```

### 评论翻译（CommentTranslation）
```python
class CommentTranslation(ModelBase):
    """评论翻译表"""

    class Status(PyEnum):
        NORMAL = 'normal'
        PENDING = 'pending'
        REJECTED = 'failed'

    comment_id = Column(Integer, ForeignKey('comments.id'), nullable=False)
    lang = Column(String, nullable=False)
    content = Column(String, nullable=False)
    translated_by = Column(Integer)
    status = Column(db.StringEnum(Status), default=Status.NORMAL)

    # 关系
    comment = relationship("Comment")

    __table_args__ = (
        UniqueConstraint('comment_id', 'lang', name='uq_translation_comment_lang'),
        Index('ix_translations_lang_status', lang, status),
        Index('ix_translations_translated_by', translated_by, ModelBase.created_at.desc()),
    )

```

### 用户投票-赞成/反对(CommentVote)
```python
class CommentVote(ModelBase):
    """评论投票表"""

    user_id = Column(String, nullable=False)
    comment_id = Column(Integer, ForeignKey('comments.id'), nullable=False)
    vote = Column(Integer, nullable=False)  # 1: up, -1: down

    # 关系
    comment = relationship("Comment")

    __table_args__ = (
        UniqueConstraint('user_id', 'comment_id', name='uq_vote_user_comment'),
        Index('ix_votes_comment_vote', comment_id, vote),
    )
```

### 用户举报(CommentReport)
```python
class CommentReport(ModelBase):
    """评论举报表"""

    class Type(PyEnum):
        FRAUD = 'fraud'           # 欺诈/诈骗
        MALICIOUS = 'malicious'   # 恶意/消极
        SPAM = 'spam'             # 垃圾
        FAKE = 'fake'             # 虚假互动
        OTHER = 'other'           # 其他

    user_id = Column(String, nullable=False)
    comment_id = Column(Integer, ForeignKey('comments.id'), nullable=False)
    type = Column(db.StringEnum(Type), nullable=False)
    reason = Column(String, nullable=False)
    status = Column(String, default='created')  # created/reserved/disabled

    # 关系
    comment = relationship("Comment")

    __table_args__ = (
        UniqueConstraint('user_id', 'comment_id', name='uq_report_user_comment'),
        Index('ix_reports_comment_status', comment_id, status),
    )

```

### 举报审核（CommentReportReview）
```python
class CommentReportReview(ModelBase):
    """评论举报审核表"""

    comment_id = Column(Integer, ForeignKey('comments.id'), nullable=False)
    status = Column(String, default='created')  # created/reserved/disabled
    operator_id = Column(Integer)

    # 关系
    comment = relationship("Comment")

    __table_args__ = (
        Index('ix_report_reviews_comment', comment_id),
        Index('ix_report_reviews_created_status', ModelBase.created_at.desc(), status),
    )

```

### 评论筛查记录（CommentModeration）
```python
class CommentModeration(ModelBase):
    """评论审核表"""

    class Type(PyEnum):
        INITIAL = 'initial'    # 初步筛查
        FULL = 'full'         # 全面筛查
    
    class Status(PyEnum):
        PROCESSING = 'processing'   # 进行中
        APPROVED = 'approved'       # 通过
        REJECTED = 'rejected'       # 拒绝
        
    class ModeratorType(PyEnum):
        KEYWORD = 'keyword'   # 关键词过滤器
        AI = 'ai'            # AI 审核员
        MANUAL = 'manual'    # 人工审核员

    class Trigger(PyEnum):
        AUTO = 'auto'        # 系统自动触发
        PATROL = 'patrol'    # 人工巡检触发
        REPORT = 'report'    # 举报触发
    
    comment_id = Column(Integer, ForeignKey('comments.id'), nullable=False)
    type = Column(db.StringEnum(Type), nullable=False)
    moderator_type = Column(db.StringEnum(ModeratorType), nullable=False)
    trigger = Column(db.StringEnum(Trigger), nullable=False)
    status = Column(db.StringEnum(Status), default=Status.PROCESSING)
    reason = Column(String)
    operator_id = Column(Integer)

    # 关系
    comment = relationship("Comment")

    __table_args__ = (
        Index('ix_moderations_comment_type', comment_id, type, ModelBase.created_at.desc()),
        Index('ix_moderations_operator', operator_id, ModelBase.created_at.desc()),
    )

    
```

### 评论标签表（CommentTag）
```python
class CommentTag(ModelBase):
    """评论标签表"""

    content = Column(String, nullable=False)
    business = Column(String, nullable=False)
    lang = Column(String, nullable=False)
    comment_ids = Column(db.PG_JSONB, default=list)  # 使用JSONB存储评论ID列表

    __table_args__ = (
        Index('ix_tags_content', content),
        Index('ix_tags_coin_lang_content', business, lang, content),
        # 为JSONB列创建GIN索引以支持高效查询
        Index('ix_tags_comment_ids_gin', comment_ids, postgresql_using='gin'),
    )

```

### 互动消息（CommentMessage）
```python
class CommentMessage(ModelBase):
    """互动消息表"""

    class MessageType(PyEnum):
        UP = 'up'           # 点赞
        DOWN = 'down'       # 踩
        COMMENT = 'comment' # 评论
        REPLY = 'reply'     # 回复
        AT = 'at'          # 提及
    
    class MessageSource(PyEnum):
        SELF = 'self'      # 自己的动作
        OTHER = 'other'    # 其他用户与我的互动

    class ReadStatus(PyEnum):
        UNREAD = 'unread'  # 未读状态
        READ = 'read'      # 已读状态

    user_id = Column(String, nullable=False)
    type = Column(db.StringEnum(MessageType), nullable=False)
    source = Column(db.StringEnum(MessageSource), nullable=False)
    comment_id = Column(Integer, ForeignKey('comments.id'), nullable=False)
    content = Column(String, nullable=False)
    read_status = Column(db.StringEnum(ReadStatus), default=ReadStatus.UNREAD)

    # 关系
    comment = relationship("Comment")

    __table_args__ = (
        Index('ix_messages_user_created', user_id, ModelBase.created_at.desc()),
        Index('ix_messages_user_status_created', user_id, read_status, ModelBase.created_at.desc()),
    )
```

### 评论统计(CommentStatus)
（待完善）

```python
class CommentStatistics(ModelBase):
    """评论统计表"""

    business = Column(String, nullable=False, unique=True)
    total_comments = Column(Integer, default=0)
    active_users = Column(Integer, default=0)
    # 使用PostgreSQL的JSONB类型存储统计数据，比普通JSON更高效
    lang_stats = Column(db.PG_JSONB, default=dict)    # 各语言评论数统计
    date_stats = Column(db.PG_JSONB, default=dict)    # 每日评论数统计

    __table_args__ = (
        # 为JSONB列创建GIN索引以支持高效查询
        Index('ix_statistics_lang_stats_gin', lang_stats, postgresql_using='gin'),
        Index('ix_statistics_date_stats_gin', date_stats, postgresql_using='gin'),
    )

```

### 用户评论统计（UserCommentStatus）
（待完善）

```python
class CommentUserStatus(ModelBase):
    """用户评论状态表"""

    user_id = Column(String, nullable=False, unique=True)
    ban_until = Column(db.PG_TIMESTAMP_TZ)
    warning_count = Column(Integer, default=0)
    report_count = Column(Integer, default=0)
    block_count = Column(Integer, default=0)


```

## 缓存设计
![](https://cdn.nlark.com/yuque/__puml/ddd9bc48d663ff4ba33c3151a7c8d13d.svg)

### 评论缓存（CommentCache）
记录列表缓存中的评论内容，Key 为 id，Value 为整个 comment 对象（HashSet 存储，以便更新部分动态数据，比如赞踩数，分数）



### 评论列表缓存（CommentListCache）
根据不同的排序方式，缓存多张评论列表，目前已知有 Hot/New/Top 三个列表缓存，用 SortedSet 存储，可以保持局部数据动态更新。

下面的几个操作，都已 Hot 列表为例（Top 列表和 Hot 列表的操作方式完全相同，仅仅计分不同；New 列表相对稳定

#### 查看评论——初始化
![](https://cdn.nlark.com/yuque/__mermaid_v3/008c11377e2578da96d3ead923006ea4.svg)

#### 发表评论——新增
经过初筛

![](https://cdn.nlark.com/yuque/__mermaid_v3/5ea402f649ab1075e820240d0276efc0.svg)

#### 赞成/反对评论——变更分数
![](https://cdn.nlark.com/yuque/__mermaid_v3/28a87bfa0f47bdab26ef3c735d15ba69.svg)

#### 用户删除/审核禁止评论——删除
![](https://cdn.nlark.com/yuque/__mermaid_v3/0a1d06f7c3b0060577dd63d1700e91b1.svg)

### 用户待审核评论缓存（UserPendingCommentsCache）
使用SortedSet存储待审核的评论ID，用户自己查看评论时，待审核评论总在最前；在这个列表中的评论，其他人是看不到的。通过一个异步任务，处理用户待审核的评论，每处理完一条，从这里删除，插入到评论列表缓存。

### 未处理的赞踩队列（UserUnprocessedVotesCache）
使用 ListCache 存储新的用户点赞点踩，通过一个定时任务，批量处理赞踩记录，统计计数，并存入数据库。

### 互动消息计数缓存（CommentUnreadCountCache）
使用 HashSet 记录每个用户未读消息计数。

## 核心流程
### 发表评论/回复
![](https://cdn.nlark.com/yuque/__puml/a32119a614c91991836a5c5ab468f388.svg)

### 用户赞同/反对（投票）
![](https://cdn.nlark.com/yuque/__puml/5baabd392e6868ed632837d021d7635e.svg)

### 用户举报评论
![](https://cdn.nlark.com/yuque/__puml/520cf691cbc150472cec7294233b5534.svg)

### 用户查看评论
![](https://cdn.nlark.com/yuque/__puml/d71207e6beac3f0641815e24a08420ee.svg)



## 关键实现
### Hot 排序算法
#### 调研
调研了 Reddit 和 HackNews 的帖子排序算法，最终决定采用 Reddit 故事排序：

![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1732158037097-a9a0cce2-ce99-4ae8-9c2f-29d03ec5b595.png)

1. Reddit 故事排序很好地平衡了用户参与度和时间衰减
2. 该算法巧妙地利用一个类似时间戳的功能，避免了时间衰减时动态更新所有帖子的 score
3. 有参数可以控制衰减力度
4. 但 sign 函数（图中的y）的作用有待商榷

#### 产品需求
1. 体现基本热度，热度=ups - downs
2. 热度越高，排名越靠前
3. 体现时间衰减，越新的帖子，排名越靠前
4. 时间衰减因子可调节

#### 技术约束
1. 帖子的排序得分，不需要因为时间衰减而调整（不然更新量太大，而且越来越大）

#### 预期实现
采用 Reddit 故事排序算法

```python
def calculate_score(comment) -> float:
    """Reddit 故事排序算法
    score = log10(max(1, ups - downs)) + seconds * sign/45000
    
    特点:
    - 新评论会获得时间加成，但不需要
    """
    ups = comment.ups
    downs = comment.downs
    x = ups - downs
    seconds = (comment.created_at - BASE_TIME).total_seconds()
    
    # 计算投票分数
    vote_score = math.log10(max(1, abs(x)))
        
    # 计算时间分数
    time_score = seconds*(1 if x>0 else 0 if x=0 else -1) / 45000
    
    return vote_score + time_score
```

+ **疑问**：<font style="background-color:rgba(247,245,237,1);">对于帖子的赞成、反对意见，我们应该怎么看待？(喜欢/不喜欢，和赞成/反对，表达的含义其实不同）</font>

      <font style="background-color:rgba(247,245,237,1);">解答（hai yang）：有一部分表示赞成/反对，大部分还是对帖子的喜欢/不喜欢，所以 ups - downs 应该表达了用户偏好。</font>

### 内容筛查
#### 关键词筛查
**分词处理**

app/utils/information.py 里有现成的 cut_words 方法，只有简中、繁中、泰文和日文需要分词，其他语言空格分词。

**根据关键词比对**

参考短信反欺诈项目，逐一比对关键词。

#### AI 筛查
+ **模型**：Claude
+ **版本**：Claude 3.0 Haiku or Claude 3.5 Haiku
+ **价格**：$1.5/百万 token or $6/百万 token
+ **性能**：2000 RPM and 4,000,000 TPM （aws CrossRegion 的前提下，两个模型的性能参数是一致的）

假设：

    - 有 1/4 的处理能力供给到币种评论，即 500 RPM & 1,000,000 TPM
    - 每条评论耗费 1000 token，10s 时间

推导：

    - 按照 RPM 约束，每分钟可以处理 500 条评论
    - 按照 TPM 约束，每分钟可以处理 1000 条评论

结论：

    - **处理评论数 <= 500条/分钟，即 8条/秒**

延伸：

    - 按异步任务排队实现，多 worker 并发处理
    - 初期参与者少，应该能满足要求，后期用户参与度提升之后，可以用自训练模型代替，实现更高的并发

（初筛 10s 完成，只能说在大部分情况下可以满足——AI 的时延和并发量受限）

+ **费用**：

假设：

    - 每条评论耗费 1000 token
    - **每分钟处理 10 条评论**（对于费用，应该估算的是平均值，而不是峰值）

推导：

    - 每分钟消耗 0.01M token
    - 两个模型分别消耗 $0.015/分钟 or $0.06/分钟

结论：

    - **使用费用为：$648/月 or $2592/月**
+ **后续优化**：
    1. 申请更多的并发数和 token 容量
    2. 向多个 Claude 服务商分流
    3. **训练自己的分类模型**（有现成的开源模型 BERT + 我们上线后逐步积累的数据，实现这一点并不是很难）
+ **容错**：
    - 当 ai 服务失败的时候，初筛超过 10 分钟自动通过，并发送通知到管理员

### AI 翻译
#### 语言识别
展示 ai翻译 按钮的前提条件，是识别到的帖子语言（而不是帖子所属语区），与用户当前全站设置的导航语言不同，因此这里需要系统自动识别帖子语言，这里可以使用三方库：`langid`+ `pycountry`实现，基于字符集识别，性能优良，识别率高。

示例代码如下：

```python
import langid
import pycountry

def detect_lang(text: str) -> str:
    lang_code, _ = langid.classify(text)
    lang_name = pycountry.languages.get(alpha_2=lang_code).name
    return lang_name

```

#### 翻译服务
沿用目前内部的 ai-translate-server 实现翻译，考虑到目前的产品设计，是用户点击一条翻译一条，翻译的量不会很大，对当前翻译服务的性能和费用都没有压力，但需要时效性，因此采用快速的 Claude Haiku 3.0 (或3.5，看成本) 模型，实时调用完成翻译。

翻译完成的内容，存入数据库，下次有针对同一个帖子的翻译需求，可以直接获取展示。

整体翻译的请求不会太多，这里不需要设计缓存机制。

此处注意如果只有空文本或者表情符号，就不要再翻译。

## 部署架构
本系统属于 UGC 内容管理，独立部署，数据库基于Postgresql，缓存采用 Redis。前端 http 接口通过 backend 项目转发，用户&账户&认证授权都由 backend 项目管理，需要登录的信息，转发时在请求头中设置 user_id，和资讯项目采用同样的处理方式。

![](https://cdn.nlark.com/yuque/__puml/2bff57ebe881d12fbadca113432372ef.svg)



## API设计
### 评论相关API
| 接口名 | URL | Method | 功能说明 |
| --- | --- | --- | --- |
| 获取评论列表 | /api/v1//comment-list | GET | 获取币种评论列表，支持分页、排序(Hot/New/Top)、语言筛选 |
| 创建评论 | /api/v1//comment | POST | 发表新评论 |
| 编辑评论 | /api/v1/comment/{id} | PUT | 编辑评论内容(仅支持待审核状态) |
| 回复评论 | /api/v1/comment/{id}/reply | POST | 回复指定评论 |
| 获取回复列表 | /api/v1/comment/{id}/replies | GET | 获取指定评论的回复列表，支持分页 |
| 点赞/踩 | /api/v1/comment/{id}/vote | POST | 评论点赞/踩操作，vote=1为点赞，vote=-1为踩 |
| 举报评论 | /api/v1/comment/{id}/report | POST | 举报违规评论 |
| 获取互动消息 | /api/v1/comment/messages | GET | 获取互动消息列表，支持状态筛选(已读/未读)、类型筛选(点赞/踩/评论/回复/提及)、时间筛选 |
| 标记消息已读 | /api/v1/comment/messages/mark-read | POST | 标记指定消息为已读 |
| 全部标记已读 | /api/v1/comment/messages/mark-all-read | POST | 标记所有消息为已读 |
| 获取未读数量 | /api/v1/comment/messages/unread-count | GET | 获取未读消息数量 |


### 管理后台API
| 接口名 | URL | Method | 功能说明 |
| --- | --- | --- | --- |
| 评论管理列表 | /api/admin/comments | GET | 获取评论管理列表，支持多维度筛选 |
| 更新评论状态 | /api/admin/comments/{id}/status | PUT | 更新评论状态(启用/禁用) |
| 用户禁言 | /api/admin/users/{id}/ban | POST | 对违规用户实施禁言 |
| 发送警告 | /api/admin/users/{id}/warn | POST | 向用户发送警告消息 |
| 统计数据 | /api/admin/statistics | GET | 获取评论统计数据 |


## 定时任务
### 赞成/反对处理任务
从 UserUnprocessedVotesCache 中读取赞踩缓存，清洗统计后，记入 CommentCachex、CommentListCache 和数据库表 Comment。

### 消息触达任务
定时统计互动消息中的未读消息，按照需求，以不同维度进行统计，并发送触达任务

### 评论统计任务
（待完善）

```python
@celery.task
def update_comment_statistics():
    """
    每小时更新评论统计数据
    1. 更新评论总数
    2. 更新活跃用户数
    3. 更新语言分布
    4. 更新日期统计
    """
    pass
```



### 列表缓存校正任务
（待完善）

（每天执行一次，用于校正 CommentListCache 的可能叠加失误）

```python
@celery.task
def refresh_comment_cache():
    """
    每天更新热门评论缓存
    1. 更新评论列表缓存
    2. 更新评论缓存
    """
    pass
```

## 性能
### 存储并发性
评论增删改查和排序都设定了对应的缓存，应该可以满足未来一段时间并发性能要求

+ 基础写操作性能：  
MongoDB写入：单机约 1000-2000 ops/s  
Redis缓存更新：单机约 50000-100000 ops/s
+ 基础读操作性能：  
Redis读取：单机约 80000-100000 ops/s

预估并发能力：

+ 读操作：

缓存命中率 80%

单次请求平均耗时 50ms

**预估并发：约 1600-2000 QPS**

+ 写操作：

评论发布平均耗时 200ms（不含审核）

**预估并发：约 400-500 QPS**

### AI 服务并发性
因为每一条评论都需要初审，审核本身应该是发布评论的性能瓶颈；

审核中有几个步骤：

1. 分词
2. 按初审关键词筛查
3. AI 初筛，因为一条 AI 调用的时间以秒记，显然 AI并发才是性能瓶颈的关键，AI 的性能估算，详见 《[4.2 AI 筛查](#LqM5Q)》一节

**预估并发：8 条评论/S，可通过排队平滑流量**

## 安全
### 防 XSS 攻击
对用户发表的评论/回复，进行内容保护过滤，移除 HTML标签和特殊转义字符，控制字符（注意保护 emoji）

### 防 SQL 注入攻击
本项目数据库基于 MongoDB，不是关系型数据库，不存在 SQL 注入，不过也可能有对应的 MongoDB 查询注入。

项目的查询基于 MongoEngine 的 ORM 实现，这样就避免了绝大部分注入可能性；另外API层使用webargs进行参数验证和类型转换，对于类似的攻击有很好的防范。

### 限频
限额的作用有两方面：

1. 防止刷接口造成性能压力；
2. 防止刷接口生产机器人内容

具体的限频次数：

+ 发表+回复评论
    - 5次/分钟（性能约束）
    - 200次/天（内容约束）
+ 点赞/踩
    - 20次/分钟（性能约束）
    - 1000次/天（内容约束）
+ 投诉
    - 10次/分钟（性能+内容约束）
+ 查看评论
    - 20次/分钟（性能约束）
+ 查看互动消息
    - 20次/分钟（性能约束）
+ 评论翻译
    - 10次/分钟（性能约束）
    - 2000次/天（性能约束）

