# 一、调研背景
评论排序为币种评论核心功能，币种评论不像新闻那样实效性很强，为满足热门内容没那么快沉下去且算法稳定合理，调研研究Reddit、Hack news及相关社交软件评论算法供产品设计提供思路。

# 二、排序算法说明
## 2.1 Reddit
### （1）故事排序
![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1731318614220-6dd340ad-f186-40d8-9ecb-b08f53c3baf3.png)

+ 可通过调整参数降低提交时间对评论的影响（45000这个参数越大，时间影响越小）
+ 技术反馈，无需根据时间重算每个帖子的最终得分（不用定时更新历史帖子）
+ 前 10 个赞同票与接下来的 100 个赞同票一样重要。例如，获得 10 个赞同票的故事和获得 50 个赞同票的故事将具有相似的排名，有争议的报道的排名会较低![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1731318614753-0b8ffa08-21cf-4f93-b145-bbb9f811c5c1.png)
+ 缺点：从公式中**未看到评论数量对排名的影响**

### （2）评论排序
![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1731318614173-814b8786-8f38-424a-84fa-8dbe010983d6.png)

+ 置信度排序中**完全无提交时间的影响**
+ 置信排序关心赞成票数与抽样大小的关系

![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1731318614018-2a110387-a672-41de-9212-452d6c5fa154.png)

```plain
举例：如果一条评论有 1 个赞成票和 0 个反对票，那么它的赞成率就是 100%，但由于数据不多，系统会将其保持在底部附近。但如果它有 10 个赞成票和 1 个反对票，系统可能有足够的信心将其置于有 40 个赞成票和 20 个反对票的评论之上——系统认为，当它也获得 40 个赞成票时，几乎可以肯定它的反对票会少于 20 个。最好的部分是，如果它错了（有 15% 的时间是错的），它会很快获得更多的数据，因为数据较少的评论位于顶部附近。
```

+ 缺点：从公式中**未看到评论数量对排名的影响**

相关链接：

[https://medium.com/hacking-and-gonzo/how-reddit-ranking-algorithms-work-ef111e33d0d9](https://medium.com/hacking-and-gonzo/how-reddit-ranking-algorithms-work-ef111e33d0d9)

## 2.2 Hack news
![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1731318613968-d140b55a-dce8-49c8-ae9c-2159ba598782.png)

+ 公式简单好理解
+ 技术反馈，需要根据时间重算，定时更新整体分数（这里比较浪费算力，不过可以优化）
+ 缺点：公式中**未看到评论数量对排名的影响**
+ 相关链接：[https://medium.com/hacking-and-gonzo/how-hacker-news-ranking-algorithm-works-1d9b0cf2c08d](https://medium.com/hacking-and-gonzo/how-hacker-news-ranking-algorithm-works-1d9b0cf2c08d)

## 2.3 CMC 
| 图一 | 图二 |
| --- | --- |
| ![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1731318616486-d1852c76-09d5-4234-b904-3c8dffde3a31.png) | ![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1731318616130-6e8ecbd7-737a-4a35-a755-6cdaf77634a0.png) |


+ **每次刷新看到的评论内容不同**
+ 图一排名顺序优于图2，从前端页面可以看出，**评论数量也是重要参考因素之一**
+ 目前CMC支持功能：评论、转发、表情、关注、分享、复制链接、**利好**、对帖子不感兴趣、举报帖子、举报用户、屏蔽用户

## 2.4 抖音/小红书
### （1）抖音
+ 抖音排序参考因素：提交时间、**评论数量**、赞同、反对（举报、屏蔽可能有影响，这里看不到）
+ 图一：7-19时间假设相同，**7条回复**&22点赞要比**5条**回复&100点赞更靠前

| 图一 |
| --- |
| ![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1731318616530-4a002606-3037-48b9-90f9-8ab195cb1856.png) |


### （2）小红书
+ 抖音排序参考因素：提交时间、**评论数量**、赞同、**作者回复**（举报、屏蔽可能有影响，这里看不到）
+ 如图图一所示，有2条2天前的，和一条1小时前：其中一条2天前排名更靠前，但整体点赞及评论数量不如第2个2天前，排名靠前原因：作者回复
+ 图二，提交时间最新，有评论的比没评论排序更靠前

| 图一 | 图二 |
| --- | --- |
| ![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1731318617225-5cfd51fe-94ec-4218-b6f7-ba54fce55681.png) | ![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1731318617853-705106b2-33b2-495d-bda6-defd454fc694.png) |


# 三、总结
1. 基于Reddit和Hack news调研我们需要考虑清楚：Reddit和Hack news都**未加入回复数量对排序的影响，我们是否将回复数量（包含表情）作为排序参数？**
+ 若无需考虑币种评论数量：币种一级评论参考Reddit 故事排序更合理，不过表情回复及回复数量将不参与排名
+ 个人想法：前期不考虑评论数量是可以的，千言万语结论要么是赞同或不赞同
1. 币种评论回复排序可参考Hack news，至于定时得分更新这里我们可以优化一下
+ 未选Reddit评论排序的原因：完全无提交时间影响、公式也比较复杂，正态分布存在预测性大概率是准确的
1. 币种评论定位与抖音/小红书有差异，可参考前端交互，后端逻辑可与之有差异
2. CMC每次刷新看到的评论内容不同，不确定是否有定时任务，可能存在观察误差

---

**<font style="color:blue;">结论：建议使用hack new公式作为参考</font>**

