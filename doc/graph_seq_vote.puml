@startuml 用户点赞/点踩（投票）

skinparam sequenceMessageAlign center

'定义参与者
actor 用户 as User
participant "前端" as Frontend
participant "评论服务(API+Business)" as CommentService
participant "定时任务" as Scheduler
participant "排序服务" as SortService
participant "通知服务" as NotificationService
database "缓存" as Cache
database "数据库" as DB

'开始序列
User -> Frontend : 针对一条评论点赞/踩

Frontend -> CommentService : 发起投票请求
activate CommentService

CommentService -> CommentService : 检查赞踩频率
alt 超过频率限制
    CommentService --> Frontend : 返回频率限制提示
    Frontend --> User : 显示"操作频繁，请稍后再试"
end

CommentService -> CommentService : 检查账户信息完整性（账号名，头像等）
alt 不完整
    CommentService --> Frontend : 返回提示信息
    Frontend --> User : "跳转至账户信息维护"
end

CommentService -> Cache: Add to UserUnprocessedVotesCache（用户未处理投票缓存）
CommentService --> Frontend: 投票成功
deactivate CommentService

group 定时任务：3分钟处理一次
	Scheduler -> Cache : Pop all from UserUnprocessedVotesCache

	Scheduler -> DB : 批量插入数据库 CommentVote
	alt 数据库操作失败
			Scheduler --> NotificationService : 通知管理员
			Scheduler -> Cache : Push back to UserUnprocessedVotesCache
	end

	Scheduler --> Scheduler : 统计投票数据

	group 更新评论缓存
		Scheduler -> SortService : 获取得分/排名数据
		Scheduler -> Cache : 更新 CommentCache 中的得分
		Scheduler -> Cache : 更新 CommentListCache 中的位置
	end

	Scheduler -> DB : 更新数据表 Comment 中的得分
end


@enduml
