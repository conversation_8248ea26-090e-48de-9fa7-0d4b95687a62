@startuml 查看币种评论序列图

skinparam sequenceMessageAlign center
skinparam responseMessageBelowArrow true

'定义参与者
actor 用户 as User
participant "前端界面" as Frontend
participant "评论服务" as CommentService
participant "系统服务" as SystemService
database "评论数据库" as CommentDB
database "Redis缓存" as Cache

'开始序列
User -> Frontend: 访问币种评论区

Frontend -> SystemService: 获取当前语言设置
activate SystemService
SystemService --> Frontend: 返回语言设置
deactivate SystemService

Frontend -> CommentService: 请求评论列表(币种ID/语言/排序方式/分页)
activate CommentService

group 缓存处理
	CommentService -> Cache: get CommentListCache by sort
	activate Cache
	Cache --> CommentService: 返回评论列表
	deactivate Cache
	
	CommentService -> Cache: get UserPendingCommentsCache
	activate Cache
	Cache --> CommentService: 返回当前用户未审核评论列表
	deactivate Cache
	
CommentService -> CommentService: 组装缓存结果(用户未审核 + 评论列表 - 被屏蔽用户)
end

alt 评论列表缓存未命中/过期/数量不足
    CommentService -> CommentDB: 查询评论数据
    activate CommentDB
    CommentDB --> CommentService: 返回评论数据
    deactivate CommentDB
end

CommentService -> CommentService: 组装评论数据（缓存+数据库查询结果）

CommentService --> Frontend: 返回评论列表数据
deactivate CommentService

alt 用户滚动到底部
    Frontend -> CommentService: 请求下一页数据
    activate CommentService
    CommentService --> Frontend: 返回下一页数据
    deactivate CommentService
    Frontend -> Frontend: 追加显示新数据
end

Frontend --> User: 展示评论内容
deactivate Frontend

@enduml
