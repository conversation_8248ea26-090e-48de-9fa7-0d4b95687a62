@startuml

' 样式定义
skinparam class {
    BackgroundColor White
    ArrowColor Black
    BorderColor Black
}

' 实体类定义
class Comment {
    评论
}

class CommentTranslation {
    评论翻译
}

class CommentVote {
    评论投票
}

class CommentReport {
    评论举报
}

class CommentReportReview {
    举报审核
}

class CommentModeration {
    评论审核
}

class CommentMessage {
    互动消息
}

' 关系定义
Comment "1" -- "0..*" CommentTranslation : 被翻译为 >
Comment "1" -- "0..*" CommentVote : 被投票 >
Comment "1" -- "0..*" CommentReport : 被举报 >
Comment "1" -- "0..*" CommentModeration : 被审核 >
CommentReport "1..*" -- "1" CommentReportReview : 触发审核 >
Comment "1" -- "0..*" Comment : 回复 >

' CommentMessage 的关系
Comment "1" -- "0..*" CommentMessage : 产生评论/回复消息 >
CommentVote "1" -- "0..*" CommentMessage : 产生投票消息 >
CommentModeration "1" -- "0..*" CommentMessage : 产生审核消息 >

@enduml
