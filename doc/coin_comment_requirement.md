# 一、需求背景
币种资料模块增加币种评论功能。

1. 业务目的：丰富币种信息的全面度（外部渠道接入+内部用户产出）；用户可查看并参与讨论，提高活跃度和粘性；**有价值评论后续还可支持人工审核后push**。
2. 用户层面：查看评论，通过评论了解币种动态，促进交易；发表评论，传播对币种见解，期望获得反馈及认同。
3. 系统层面：**将评论功能组件化**，后续可直接引用至其他功能，保证交互一致性，降低功能维护。

# 二、方案概述
## 2.1 功能概述
1. **评论功能组件化**
+ 评论区类别：**多向评论**（用户可以在评论区发表自己的看法、也可与他人进行交流互动）
+ 应用业务：**币种资料页面**（后续可低成本接入其他业务）
+ **包含模块：**账号信息展示（账户名、用户名、头像）、评论输入、评论展示、评论排序、评论互动、触达（站内信和push）
+ 高并发处理：支持每秒XXX次以上的评论请求，每个账户每分钟增加限频
1. **风控层面**
+ 关键词过滤：支持admin关键词过滤，并**接入第三方成熟AI机器人**，同时需要人工巡检
+ 安全性需求：防止 XSS 攻击、SQL 注入等安全漏洞
1. **Admin管理和审核**：过滤词管理、评论内容、待审核内容、评论统计、用户列表
+ 评论内容：支持评论内容启用/禁用/删除操作，支持对账号违规警告
+ 待审核内容：举报次数>10次进入待审核内容，支持对内容保留、删除
+ 评论数据：支持查看评论内容数据，账号数据
+ 用户列表：支持对用户进行禁言操作

## 2.2 名词说明
**评论**：针对当前币种的一级评论（说明：当前功能为币种评论，增加前缀作为区分）

**回复**：回复上一个用户言论（无层级概念），包含一级评论的回复和回复的回复

**语区**：发表评论时选择导航语区（非英文环境下，若切换为英文社区，此时提交为英文语区）

**语言**：发表评论本身的语言

**英文社区：**可在不改变导航语言的前提下，快速查看英文社区内容

## 2.3 相关链接
原型说明：[https://zobz7u.axshare.com/](https://zobz7u.axshare.com/)；

UI设计：待补充

# 三、业务说明
## 3.1 内容过滤流程
+ **<font style="color:blue;">用户评论后，仅自己可见，初步筛查完毕后&后端返回排序后前端所有用户可见</font>**，用户评论后优先进入初步筛查（admin关键词过滤&AI服务过滤）
+ 前端展示期间需进入全面筛查（admin关键词过滤&AI服务过滤&人工巡检）
    - 人工定期定时前端巡检，发现违规内容进行禁用，并补充admin关键词
+ **初步筛查&全面筛查均需按照分词逻辑进行过滤**（举例：黑**夜总会**过去，不会被过滤）

```plain
原则说明
初筛：快速，尽量避免误伤，把明显不良内容过滤掉
全面筛：完整，期望过滤掉所有不良内容，同时避免误伤（过滤逻辑逐渐加强）
```

设计思路（与技术讨论后优化）

考虑到用户评论后立刻过滤，若筛查逻辑后续逐渐复杂则影响前端展示；可以分2步进行检查，初步检查通过后可直接展示，若全面筛查发现违规，此时可以限制展示

## 3.2 评论功能
### **（1）评论输入与互动**
支持功能：评论/回复、赞同、反对、举报

| 功能 | 规则 |
| --- | --- |
| 评论/回复 | + 登录用户设置完账号名（全局唯一，取默认可变更一次）、用户名（取默认可变更）、头像（取默认可变更，不强制要求）后可发言--------**<font style="color:purple;">关联需求</font>**<br/>+ 支持文本评论，长度限制为 1000 字符，超出后无法键入<br/>    - 若用户输入超链接，则当作**纯文本**进行展示，不做可点击跳转操作<br/>    - 输入内容时支持输入法原生表情符号<br/>    - 内容评论/回复后无法修改<br/>    - 展示字体前后端均限制，仅展示平台支持字体无法自定义字体<br/>    - 支持@用户，根据用户输入内容匹配账户名&用户名称（范围全平台用户），用户匹配到且选中则高亮显示，未匹配到则普通字体展示；~~业务测限制如下~~**<font style="color:red;">（避免非法获取大量用户信息）</font>**<br/>        * 匹配优先优先级：账户名称全匹配>用户名称全匹配>账户名/用户名模糊匹配，一次最多返回50条<br/>        * ~~每分钟@用户前5次，支持弹出筛选框，超出后不再弹出~~<br/>        * ~~每天@用户前20次，支持弹出筛选框，超出后不再弹出~~<br/>    - 支持标签功能，输入#和内容后，点击发表后#和内容高亮展示，已空格为分隔<br/>        * 标签文本超过30字符后做为文本处理<br/>+ **用户评论/回复后**<br/>    - **仅自己可见，初步筛查完毕后&后端返回排序后所有用户可见**<br/>        * **自己可见未刷新期间**，评论位于第一位，回复展示被回复用户下方<br/>            + 自己可见刷新后，根据后端实际排序进行展示<br/>        * 评论/回复后端未返回结果前其他用户不展示，返回结果后所有用户可见<br/>    - 评论/回复期望**10s**内所有人可见 **<font style="background-color:#FFC0CB;">（重要）</font>**<br/>    - 一级评论删除/违规，币种资料页回复不展示 |
| 赞同&反对 | + 需用户登录<br/>+ 一个账户仅可点赞同或者反对，不可同时赞同&反对，展示赞同数（此处仅记录最终结果，先踩后赞此时仅保留赞）<br/>+ 点击赞同/反对时响应展示动效，赞同数量+1（此处无需等后端返回结果） |
| 举报 | + 需用户登录，才可举报当前内容<br/>+ **举报人数超过阈值>10次，进入人工审核（此时前端依旧可见）**<br/>    - 判定内容违规，举报内容前端不展示（所有用户看不到）<br/>    - 判定账号违规，账号10/30天/60天/180天/365天/永久内无法发表评论<br/>    - 可对账号进行警告触达，提醒用户注意发表言论 |


### **（2）评论展示**
1. 显示已有评论的区域，包含评论内容、时间、logo、用户&账户名、赞同数
+ 一级评论全部展示，回复全部收起
1. AI翻译
+ 展示逻辑：**识别到评论/回复本身语言与导航语区不一致**，则展示AI翻译按钮（一致/无法识别则不展示此按钮）
+ 底层翻译逻辑：优先进行AI翻译，若AI翻译服务异常则使用谷歌翻译服务

![](https://cdn.nlark.com/yuque/0/2024/png/25793846/1732713281973-cdf42314-4fca-47b4-8b1b-8779b114921a.png)

### **（3）多语言处理**
**内容根据语区进行隔离，用户评论内容仅展示当前语区下**

1. **内容提交**
+ 语区
    - 若未切换为英文社区，则以导航语区为准
    - 若切换为英文社区，则语区为英文
+ 语言：提交内容语言为发表评论本身的语言
1. **内容展示**
+ 默认展示用户设置导航语区的评论
1. **语区切换**
+ 切换导航语区，可看到对应语区下的评论
+ 切换英文社区（切换按钮，导航语区非英文时可见），点击后，仅评论社区展示英文语区内容
+ 互动消息，**点击跳转时判断此内容和评论社区“语言参数”是否一致？**
    - 一致，则无需切换（要么为导航语区，要么为英文社区）
    - 不一致，以一级评论语区为准切换导航语区

```plain
举例A，当前导航为中文，切换查看英文社区，互动消息一级评论为英文，则无需切换导航语区
举例B，当前导航为中文，未切换查看英文社区，互动消息一级评论为英文，则需切换导航语区
```

### **（4）评论排序**
一级评论排序方式可设置：Hot(默认)、New、Top，评论排序方式规则如下（公式确认后稍后补充）

1. Hot，同时考虑时间&评论互动的关系进行排序
+ **时间**，随着时间的推移，得分会受到时间衰减的影响，确保新帖子能够获得曝光；**时间因子可调整**，调整后增加/减少时间对排名的影响。（通常是一个指数函数，使得新帖子在短时间内更容易获得排名。）
+ **评论互动**
    - **得分**，得分=赞同-反对，得分决定帖子的基本热度
    - 回复，不影响整体排名（想法：回复多可能因为存在争议，而非热门导致）
1. New，按照发帖时间倒序排序
2. Top，按照评论互动进行排序
+ **评论互动**
    - **得分**，得分=赞同-反对，得分决定帖子的基本热度
    - 回复，不影响整体排名（想法：回复多可能因为存在争议，而非热门导致）

**1级评论下的回复采用TOP类型排序方式（不支持切换）**

### **（5）评论互动与消息触达**
1. 评论互动：我的评论、我的回复、我的赞、我的踩、收到回复、收到点赞/踩、被提及时，展示至评论互动
    - 当前账户删除评论后，评论不可见
    - 当前账户注销后，展示账户已注销，此时评论可见
1. 消息触达，触达内容详情见原型

| 场景 | 触发时机 | 站内信 | 邮件 | push | 弹窗 |
| --- | --- | --- | --- | --- | --- |
| 收到回复 | | | | ✅（3小时） | |
| 收到点赞 | | | | ✅（3小时） | |
| 被提及时 | | | | ✅（3小时） | |
| 账号违规警告（禁言前警告） | admin点击警告通知按钮 | ✅ | | ✅ | |
| 账号禁言 | admin提交禁言操作 | ✅ | | ✅ | |


逻辑说明：定时（3小时）获取、统计数量并通知用户

+ 收到点赞，统计点赞用户数
+ 收到回复，统计回复数量
+ 被提及时，统计提及用户数

### **（6）高并发处理**
+ 每个账户1分钟最多可评论10条，超出后toast提示：操作频繁，请稍后再试。（应对大量用户同时发表评论的情况，确保系统性能稳定）
+ 高并发场景，能够处理每秒XXX次以上的评论请求（多人同时评论，待与技术讨论）

# 四、风控规则
### （1）关键词过滤
+ Admin：通过通用模块过滤词过滤可能涉及的敏感信息
    - 区分是否为初步筛查
+ AI：通过机器学习模型过滤恶意、敏感评论（与技术沟通已有成熟大语言AI模型可直接付费使用）
    - 区分是否为初步筛查
+ 人工：运营巡检，保证内容的展示合理；发现违规内容及时禁用

### （2）**安全性需求**
+ 防止 XSS 攻击、SQL 注入等安全漏洞（这里需要技术具体方案）

# 五、**Admin管理和审核**
## （1）过滤词管理
+ 使用已有共用过滤词管理模块，检测不当内容
+ 新增字段：类型，单选，必填，选项：初步筛查、全面筛查
    - 资讯/币种资讯，初步筛查&全面筛查过滤后展示
    - 币种评论
        * 用户评论后仅自己可见，优先进入初步筛查（admin关键词过滤&AI服务过滤），初步筛查完毕后公开
            + 未通过初步筛查内容无需进入全面筛查
        * 前端展示期间需进入全面筛查（admin关键词过滤&AI服务过滤&人工巡检），筛查完毕若存在违规则admin禁用，前端不展示

## （2）评论内容
+ 评论管理展示字段：ID、语区、币种、内容、类型、用户名&账户名、状态、时间
    - 类型：评论（一级评论）、回复
    - **状态**：
        * **正常**，用户评论/回复进入数据库默认状态正常
            + 若举报次数>10次，则进入人工审核，前端正常展示，此数据仅展示在“待审核内容”
        * **违规**，被举报并审核认为违规，未通过筛查（admin/AI），巡检后“禁用”；
        * **已删除**，发言人删除
    - 时间：评论/回复发布时间
    - 操作项：详情、删除
        * 详情弹窗基本信息展示字段：ID、语区、币种、内容、类型、用户名&账户名、时间、互动总数、回复数、赞同数、反对数、举报数（相关数据针对当前内容）、警告备注
            + 操作项：警告通知、举报数（支持查看举报数据分类）、互动详情，展示互动全部内容，包含：评论、回复、用户名、账户名、赞同数、反对数、举报数

## （3）待审核内容
待审核内容与评论内容主要差异如下，其他展示字段与筛选逻辑与评论内容一致

+ 举报次数>10次，则进入人工审核，前端正常展示，此数据仅展示在“待审核内容”
+ 操作新增保留操作，点击后弹出弹窗，可选择添加备注，确认保留后此内容不再待审核内容展示，展示在评论内容列表，状态修改为正常（待完善）

## （4）用户列表
用户权限新增禁言，默认不限制，支持设置参数10/30天/60天/180天/365天/永久

+ 前端用户提交评论后，toast提示，无法发表评论，你被禁言，xxxx-xx-xx后可发表评论（xxxx-xx-xx为日期，永久类型不展示“，xxxx-xx-xx后可发表评论”）

## （5）评论内容数据
+ 展示字段：ID、语区、币种、类型、评论内容、用户名（账户名）、互动总数、赞同数、回复数、反对数、举报数
    - 语区，用户提交留言时前端设置语言
    - 币种，用户提交留言时所在币种资料页面
    - 类型：评论（一级评论）、回复（一级评论下的回复）
    - 互动总数=回复数+赞同数+反对数
    - 回复/赞同/反对/举报，针对当前内容回复/赞同/反对/举报数量
+ 数据统计更新时间，定时更新30min（待确认）
    - 禁止展示，数据停止数据更新
    - 超过2年数据停止更新

## （6）评论账户数据
+ 展示字段：ID、用户名（账户名）、账号状态（正常/禁言）、评论数、收到回复、赞同数、反对数、被举报数、警告次数、评论/回复禁止展示数
    - 评论数：当前账户发出评论（一级评论+发出回复）
    - 收到回复：当前账户收到回复数
    - 赞同/反对/被举报/警告/禁止展示数：当前账户收到赞同/反对/被举报/警告/禁止展示数
+ 数据统计更新时间，定时更新30min（待确认）
    - 注销账户停止数据更新

# **六、协作SOP**
1. **上线前功能同步：运营组/客服？**
2. **上线后币种评论维护**
+ 关于审核：_**------待确认：客服协助？？**_
    - 内容被举报超过阈值后，人工审核，并提交审核结果；若内容违规，可进行禁言操作，未违规评估后可进行相关警告
    - 账号被举报超过阈值后，人工审核，并提交审核结果；若内容违规，可进行禁言操作，未违规评估后可进行相关警告

**方案确认后，****<font style="color:blue;">补充评估标准</font>**

# **七、数据埋点**
[业务数据需求收集](https://docs.google.com/spreadsheets/d/1r1KVaGoBhn-2-OxvNST6-co6Jx7bmBpibrFYc99PjsE/edit?gid=1961447213#gid=1961447213)

# **八、后续版本支持规划（初步想法****💡****）**
### **1. 评论输入与互动**
支持表情

+ 需用户登录
+ 一个账户可回复多个不同表情，相同表情仅可点击一次
+ 单个表情直接展示一级评论下方，不做折叠处理（类似click up）

### **2.评论排序**
**表情，**分配正向权重(影响低，可理解为赞同一种形式)，权重占比影响比得分、时间低

    - 针对一条内容，一个账户记分一次（与评论表情数量无关）

### **3.评论个人主页（自己/他人视角）**
初步想法：放ta的评论；带单员数据（如果有）、p2p商家数据（如果有）；以及其他有社交属性的数据

### 4. 评论打赏功能
