### 问题列表：

1. 用户发表评论后,评论状态为"待审核",此时仅自己可见
系统立即进行关键词过滤和AI审核
是否需要设置初步筛查的超时时间?比如5分钟内未完成初步筛查,是否自动通过?（如果遇到网络或者 AI 故障呢）

2. 类似Reddit的热度算法: `score = log10(max(1, 赞同数-反对数)) + 创建时间/45000`
这跟 reddit 的公式有细微差异：`score = log10(max(1, ups - downs)) + (sign(ups - downs) * seconds / 45000)`，缺少一个sign函数。

为什么要用sign:

+ 当ups > downs时,sign=1,时间因子为正,评论随时间推移score增加
+ 当ups < downs时,sign=-1,时间因子为负,评论随时间推移score减少
+ 当ups = downs时,sign=0,时间不影响score

这样设计的目的是:

+ 好评论(正分)会随时间上升到合适位置
+ 差评论(负分)会随时间下沉到底部
+ 中性评论保持原位

这个设计是否合理?我的疑问是:

为什么要让负分评论随时间"更负"?
是否应该让所有评论都随时间自然衰减?
这种设计会不会导致早期的负分评论永远在最底部?


3. 待设计
评论互动功能:
    点赞/反对的实现
    举报功能的设计
    用户屏蔽机制
消息通知:
    评论回复通知
    点赞通知
    系统通知(如审核结果)

4. 屏蔽是指一个用户不看这一条评论么？还是不看这个用户的评论？一期需要实现么？
5. 举报是不是需要填写信息？

### 是否可以不做的需求
* 标签（#）
