@startuml 用户点赞/点踩（投票）

skinparam sequenceMessageAlign center

'定义参与者
actor 用户 as User
participant "前端" as Frontend
participant "评论服务(API+Business)" as CommentService
participant "定时任务" as Scheduler
actor "管理员" as Administrator
participant "Admin服务" as AdminService
participant "通知服务" as NotificationService
database "缓存" as Cache
database "数据库" as DB

'开始序列
User -> Frontend : 举报一条评论

Frontend -> CommentService : 发起举报请求
activate CommentService

CommentService -> DB: Add to CommentReport
CommentService --> Frontend: 举报成功
deactivate CommentService

group 定时任务：1小时处理一次
	Scheduler -> DB : 未处理的举报次数统计 from CommentReport
	alt 大于 10 次
		Scheduler -> DB : 生成待审核评论 CommentReportReview
	end
end

Administrator -> AdminService : 查看待审核评论
activate AdminService
AdminService -> DB : query from CommentReportReview
DB --> AdminService : return CommentReportReview list
AdminService --> Administrator : 显示评论
deactivate AdminService

Administrator -> AdminService : 删除评论
activate AdminService
AdminService -> DB : disable CommentReportReview
AdminService -> DB : disable related CommentReport
AdminService -> DB : update Comment Status

AdminService -> Cache : remove from CommentListCache
AdminService -> Cache : update CommentCache
AdminService --> Administrator : 删除成功
deactivate AdminService

@enduml
