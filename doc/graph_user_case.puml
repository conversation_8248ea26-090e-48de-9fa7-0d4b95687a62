@startuml 币种评论业务用例图

skinparam actorStyle awesome

'定义角色
actor 普通用户 as User
actor 管理员 as Admin
actor AI系统 as AI
actor 客服人员 as Service
actor 审核人员 as Auditor

'定义用例包
rectangle "币种评论系统" {
    '用户相关用例
    usecase "查看币种评论" as ViewComments
    usecase "评论排序" as SortComments
    usecase "发表评论/回复" as PostComment
    usecase "删除评论" as DeleteComment
    usecase "点赞/踩评论" as VoteComment
    usecase "举报评论" as ReportComment
    usecase "设置消息提醒" as SetNotification

    '管理员相关用例
    usecase "管理评论" as ManageComments
    usecase "管理过滤词" as ManageFilterWords
    usecase "管理用户" as ManageUsers
    usecase "查看数据统计" as ViewStats

    '审核相关用例
    usecase "处理举报" as HandleReport
    usecase "初步内容筛查" as InitialScreening
    usecase "全面内容筛查" as FullScreening
}

'定义关系
User --> ViewComments
User --> PostComment
User --> VoteComment
User --> ReportComment
User --> SetNotification
User --> DeleteComment

AI --> InitialScreening

Auditor --> FullScreening

Service --> FullScreening
Service --> HandleReport

Admin --> ManageComments
Admin --> ManageFilterWords
Admin --> ManageUsers
Admin --> ViewStats

'扩展关系
ReportComment ..> FullScreening : <<trigger>>
HandleReport ..> FullScreening : <<extend>>
PostComment ..> InitialScreening : <<trigger>>
InitialScreening ..> FullScreening : <<extend>>
SortComments ..> ViewComments : <<extend>>

@enduml
