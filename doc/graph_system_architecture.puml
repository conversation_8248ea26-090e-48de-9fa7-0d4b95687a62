@startuml
!include https://raw.githubusercontent.com/plantuml-stdlib/C4-PlantUML/master/C4_Component.puml

title 部署架构

Person(user, "用户", "系统用户")

Container_Boundary(frontend, "前端") {
    Component(web_client, "Web客户端", "浏览器", "提供用户界面")
}

Container_Boundary(backend, "Backend项目") {
    Component(auth_service, "认证授权服务", "", "管理用户认证和授权")
    Component(request_proxy, "请求转发服务", "", "转发请求并注入user_id")
}

Container_Boundary(information, "资讯项目") {
    Component(info_service, "资讯服务", "", "处理资讯相关业务")
    Component(ugc_service, "UGC内容服务", "", "处理用户生成内容")
}

ContainerDb(mongodb, "MongoDB", "数据库", "存储业务数据")
ContainerDb(redis, "Redis", "缓存", "提供数据缓存")

Rel(user, web_client, "使用", "HTTPS")
Rel(web_client, request_proxy, "请求", "HTTP")
Rel(request_proxy, auth_service, "验证身份")
Rel(request_proxy, info_service, "转发请求\n(带user_id)")
Rel(request_proxy, ugc_service, "转发请求\n(带user_id)")

Rel(info_service, mongodb, "读写数据")
Rel(info_service, redis, "读写缓存")
Rel(ugc_service, mongodb, "读写数据")
Rel(ugc_service, redis, "读写缓存")

@enduml
