@startuml
[*] --> created : 用户发表评论
created --> published : 通过初步筛查\n(关键词过滤+AI审核)
created --> disabled : 未通过初步筛查\n(发现违规内容)
published --> disabled : 未通过全面筛查\n(关键词/AI/人工发现违规)
created --> deleted : 用户删除
published --> deleted : 用户删除
disabled --> deleted : 用户删除

state created {
  state "仅自己可见" as visible_self
}

state published {
  state "所有人可见" as visible_all
}

state disabled {
  state "不允许展示" as not_visible
}

state deleted {
  state "已删除" as is_deleted
}

note right of created : 评论创建后立即进行初步筛查
note right of published : 持续进行全面筛查
note right of disabled : 违规内容,禁止展示
note right of deleted : 用户主动删除,不可恢复

@enduml
