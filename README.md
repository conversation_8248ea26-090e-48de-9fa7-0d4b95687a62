# coinex_comment
# coinex 评论服务

## 服务基础能力

1. [x] http服务
2. [x] pg orm
3. [x] redis cache
4. [x] celery
5. [x] gevent (celery 和flask 进程中均已启用)
6. [x] gray log
7. [x] pg 自动迁移
8. [x] ai client
9. [ ] 单元测试 （后续按需迁移依赖的基础组件；其中，patch_mysql其实是patch_sqlalchemy，其完全可以兼容pg）
10. [ ] 多语言 （涉及到配合crowdin的工作流，探讨将多语言管理在backend服务）

## 本地测试

1. ### 按配置在本地部署pg及redis

2. ### 数据库迁移

    确保数据库配置正确后执行
    
    生成迁移文件
    
    `python3 manage.py db migrate`
    
    执行迁移
    
    `python3 manage.py db upgrade {new version}`
    
    *注意：* 示例迁移文件的版本不应带入到正式迁移文件中

3. ### 本地启动http服务
    ` ./venv/bin/flask run -h 127.0.0.1 -p 5100`

4. ### 本地启动celery
    启动beat (注意，这会在根目录生成celery-beat-schedule和coinex-comment-celery-beat.pid 文件，注意不要提交)
    
    `./venv/bin/python patch_celery.py -A run_celery.celery beat -l info --schedule=./celery-beat-schedule --pidfile=./coinex-comment-celery-beat.pid`
    
    启动worker
    
    `./venv/bin/python patch_celery.py -A run_celery.celery worker -l INFO -Q "default" -c 20 --without-gossip --without-mingle`
