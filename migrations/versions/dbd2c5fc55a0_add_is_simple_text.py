"""add: is_simple_text

Revision ID: dbd2c5fc55a0
Revises: a00f1d77f51c
Create Date: 2025-05-15 18:38:03.331843

"""
from alembic import op
import sqlalchemy as sa

import app.models

# revision identifiers, used by Alembic.
revision = 'dbd2c5fc55a0'
down_revision = 'a00f1d77f51c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('comment', schema=None) as batch_op:
        batch_op.add_column(sa.Column('is_simple', sa.<PERSON><PERSON>an(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('comment', schema=None) as batch_op:
        batch_op.drop_column('is_simple')

    # ### end Alembic commands ###
