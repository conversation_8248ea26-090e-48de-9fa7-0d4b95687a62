"""content-hash

Revision ID: b24fdac57129
Revises: dbd2c5fc55a0
Create Date: 2025-06-04 16:16:32.070855

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import app.models

# revision identifiers, used by Alembic.
revision = 'b24fdac57129'
down_revision = 'dbd2c5fc55a0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('comment', schema=None) as batch_op:
        batch_op.add_column(sa.Column('content_hash', sa.String(length=64), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('comment', schema=None) as batch_op:
        batch_op.drop_column('content_hash')
    # ### end Alembic commands ###
