"""empty message

Revision ID: a00f1d77f51c
Revises: 
Create Date: 2025-02-10 17:13:15.863330

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import app.models

# revision identifiers, used by Alembic.
revision = 'a00f1d77f51c'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('comment',
    sa.Column('business', app.models.base.StringEnum('COIN'), nullable=False),
    sa.Column('business_id', sa.String(length=128), nullable=False),
    sa.Column('business_code', sa.String(length=128), nullable=True),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('root_id', sa.Integer(), nullable=True),
    sa.Column('lang', app.models.base.StringEnum('EN_US', 'ZH_HANS_CN', 'ZH_HANT_HK', 'JA_JP', 'RU_KZ', 'KO_KP', 'ID_ID', 'ES_ES', 'FA_IR', 'TR_TR', 'VI_VN', 'AR_AE', 'FR_FR', 'PT_PT', 'DE_DE', 'TH_TH', 'IT_IT', 'PL_PL'), nullable=False),
    sa.Column('detected_lang', app.models.base.StringEnum('EN_US', 'ZH_HANS_CN', 'ZH_HANT_HK', 'JA_JP', 'RU_KZ', 'KO_KP', 'ID_ID', 'ES_ES', 'FA_IR', 'TR_TR', 'VI_VN', 'AR_AE', 'FR_FR', 'PT_PT', 'DE_DE', 'TH_TH', 'IT_IT', 'PL_PL'), nullable=True),
    sa.Column('at_users', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('status', app.models.base.StringEnum('CREATED', 'PUBLISHED', 'DISABLED', 'DELETED'), nullable=False),
    sa.Column('up_count', sa.Integer(), nullable=True),
    sa.Column('down_count', sa.Integer(), nullable=True),
    sa.Column('report_count', sa.Integer(), nullable=True),
    sa.Column('reply_count', sa.Integer(), nullable=True),
    sa.Column('hot_score', sa.Float(), nullable=True),
    sa.Column('top_score', sa.Float(), nullable=True),
    sa.Column('new_score', sa.Float(), nullable=True),
    sa.Column('remark', sa.Text(), nullable=True),
    sa.Column('status_updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('comment', schema=None) as batch_op:
        batch_op.create_index('ix_comments_business_lang_created', ['business', 'business_id', 'lang', 'new_score'], unique=False)
        batch_op.create_index('ix_comments_business_lang_hot', ['business', 'business_id', 'lang', 'hot_score', 'created_at'], unique=False)
        batch_op.create_index('ix_comments_business_lang_interact', ['business', 'business_id', 'lang', 'top_score', 'created_at'], unique=False)
        batch_op.create_index('ix_comments_root_id_created', ['root_id', 'created_at'], unique=False)
        batch_op.create_index('ix_comments_status_updated_parent_id', ['status_updated_at', 'parent_id'], unique=False)
        batch_op.create_index('ix_comments_user_id_created', ['user_id', 'created_at'], unique=False)

    op.create_table('comment_event',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('other_user_id', sa.Integer(), nullable=False),
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('type', app.models.base.StringEnum('UP', 'DOWN', 'COMMENT', 'REPLY', 'AT'), nullable=False),
    sa.Column('source', app.models.base.StringEnum('SELF', 'OTHERS'), nullable=False),
    sa.Column('lang', app.models.base.StringEnum('EN_US', 'ZH_HANS_CN', 'ZH_HANT_HK', 'JA_JP', 'RU_KZ', 'KO_KP', 'ID_ID', 'ES_ES', 'FA_IR', 'TR_TR', 'VI_VN', 'AR_AE', 'FR_FR', 'PT_PT', 'DE_DE', 'TH_TH', 'IT_IT', 'PL_PL'), nullable=False),
    sa.Column('read_status', app.models.base.StringEnum('UNREAD', 'READ'), nullable=True),
    sa.Column('self_interact', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('comment_event', schema=None) as batch_op:
        batch_op.create_index('ix_events_user_created', ['user_id', 'created_at'], unique=False)
        batch_op.create_index('ix_events_user_status_created', ['user_id', 'read_status', 'created_at'], unique=False)

    op.create_table('comment_full_text_search',
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('lang', sa.String(length=16), nullable=False),
    sa.Column('ts_content', app.models.base.FullTextSearch(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('comment_id', name='uq_comment_id')
    )
    with op.batch_alter_table('comment_full_text_search', schema=None) as batch_op:
        batch_op.create_index('ix_ts_content', ['ts_content'], unique=False, postgresql_using='gin')

    op.create_table('comment_moderation',
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('phase', app.models.base.StringEnum('INITIAL', 'FULL'), nullable=False),
    sa.Column('moderator_type', app.models.base.StringEnum('KEYWORD', 'AI', 'MANUAL'), nullable=False),
    sa.Column('trigger', app.models.base.StringEnum('AUTO', 'PATROL', 'REPORT'), nullable=False),
    sa.Column('status', app.models.base.StringEnum('PROCESSING', 'APPROVED', 'AUTO_APPROVED', 'REJECTED', 'FAILED'), nullable=True),
    sa.Column('rejected_type', app.models.base.StringEnum('ABUSE', 'ILLEGAL', 'FRAUD', 'PORN', 'OTHER', 'VIOLENCE', 'DISCRIMINATION'), nullable=True),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('remark', sa.Text(), nullable=True),
    sa.Column('operator_id', sa.Integer(), nullable=True),
    sa.Column('moderation_detail', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('comment_moderation', schema=None) as batch_op:
        batch_op.create_index('ix_moderations_comment_type', ['comment_id', 'phase', 'created_at'], unique=False)
        batch_op.create_index('ix_moderations_operator', ['operator_id', 'created_at'], unique=False)

    op.create_table('comment_report',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('comment_user_id', sa.Integer(), nullable=False),
    sa.Column('type', app.models.base.StringEnum('FRAUD', 'MALICIOUS', 'SPAM', 'FAKE', 'OTHER'), nullable=False),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('comment_report', schema=None) as batch_op:
        batch_op.create_index('ix_reports_comment_id', ['comment_id'], unique=False)
        batch_op.create_index('ix_reports_comment_user_id', ['comment_user_id'], unique=False)
        batch_op.create_index('ix_reports_created_at_comment_id', ['created_at', 'comment_id'], unique=False)

    op.create_table('comment_report_review',
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('status', app.models.base.StringEnum('CREATED', 'RESERVED', 'DISABLED'), nullable=True),
    sa.Column('operator_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('comment_report_review', schema=None) as batch_op:
        batch_op.create_index('ix_report_reviews_comment', ['comment_id'], unique=False)
        batch_op.create_index('ix_report_reviews_created_status', ['created_at', 'status'], unique=False)

    op.create_table('comment_tag',
    sa.Column('name', sa.String(length=256), nullable=False),
    sa.Column('business', sa.String(length=128), nullable=False),
    sa.Column('business_id', sa.String(length=128), nullable=False),
    sa.Column('lang', sa.String(), nullable=False),
    sa.Column('comment_ids', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('comment_tag', schema=None) as batch_op:
        batch_op.create_index('ix_tags_business_lang_name', ['business', 'business_id', 'lang', 'name'], unique=False)
        batch_op.create_index('ix_tags_comment_ids_gin', ['comment_ids'], unique=False, postgresql_using='gin')
        batch_op.create_index('ix_tags_name', ['name'], unique=False)

    op.create_table('comment_translation',
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('lang', sa.String(length=16), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('translator', sa.String(length=16), nullable=False),
    sa.Column('translated_by', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('comment_id', 'lang', name='uq_translation_comment_lang')
    )
    with op.batch_alter_table('comment_translation', schema=None) as batch_op:
        batch_op.create_index('ix_translations_translated_by', ['translated_by', 'created_at'], unique=False)

    op.create_table('comment_user_statistics',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('comment_count', sa.Integer(), nullable=False),
    sa.Column('reply_count', sa.Integer(), nullable=False),
    sa.Column('up_count', sa.Integer(), nullable=False),
    sa.Column('down_count', sa.Integer(), nullable=False),
    sa.Column('report_count', sa.Integer(), nullable=False),
    sa.Column('warning_count', sa.Integer(), nullable=False),
    sa.Column('block_count', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('comment_user_status',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('banned', sa.Boolean(), nullable=True),
    sa.Column('ban_duration', app.models.base.StringEnum('TEN_DAYS', 'THIRTY_DAYS', 'SIXTY_DAYS', 'ONE_EIGHTY_DAYS', 'THREE_SIXTY_FIVE_DAYS', 'FOREVER'), nullable=True),
    sa.Column('banned_until', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('banned_reason', sa.String(length=256), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id'),
    sa.UniqueConstraint('user_id', name='uq_user_comment_status')
    )
    op.create_table('comment_vote',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('comment_user_id', sa.Integer(), nullable=False),
    sa.Column('vote', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'comment_id', name='uq_vote_user_comment')
    )
    with op.batch_alter_table('comment_vote', schema=None) as batch_op:
        batch_op.create_index('ix_comment_comment_user_id', ['comment_user_id'], unique=False)
        batch_op.create_index('ix_comment_vote_all', ['user_id', 'comment_id', 'vote'], unique=False)
        batch_op.create_index('ix_comment_vote_comment_id', ['comment_id', 'vote'], unique=False)
        batch_op.create_index('ix_comment_vote_updated_at_comment_id', ['updated_at', 'comment_id'], unique=False)
        batch_op.create_index('ix_comment_vote_updated_at_comment_user_id', ['updated_at', 'comment_user_id'], unique=False)

    op.create_table('comment_warning',
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=256), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('comment_warning_translation',
    sa.Column('title', sa.String(length=256), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('comment_warning_id', sa.Integer(), nullable=False),
    sa.Column('lang', app.models.base.StringEnum('EN_US', 'ZH_HANS_CN', 'ZH_HANT_HK', 'JA_JP', 'RU_KZ', 'KO_KP', 'ID_ID', 'ES_ES', 'FA_IR', 'TR_TR', 'VI_VN', 'AR_AE', 'FR_FR', 'PT_PT', 'DE_DE', 'TH_TH', 'IT_IT', 'PL_PL'), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_info',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=True),
    sa.Column('account_name', sa.String(length=64), nullable=False),
    sa.Column('avatar', sa.String(length=128), nullable=True),
    sa.Column('signed_off', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', name='uq_user_id')
    )
    with op.batch_alter_table('user_info', schema=None) as batch_op:
        batch_op.create_index('ix_account_name_user_id', ['account_name', 'user_id'], unique=False)
        batch_op.create_index('ix_name_user_id', ['name', 'user_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_info', schema=None) as batch_op:
        batch_op.drop_index('ix_name_user_id')
        batch_op.drop_index('ix_account_name_user_id')

    op.drop_table('user_info')
    op.drop_table('comment_warning_translation')
    op.drop_table('comment_warning')
    with op.batch_alter_table('comment_vote', schema=None) as batch_op:
        batch_op.drop_index('ix_comment_vote_updated_at_comment_user_id')
        batch_op.drop_index('ix_comment_vote_updated_at_comment_id')
        batch_op.drop_index('ix_comment_vote_comment_id')
        batch_op.drop_index('ix_comment_vote_all')
        batch_op.drop_index('ix_comment_comment_user_id')

    op.drop_table('comment_vote')
    op.drop_table('comment_user_status')
    op.drop_table('comment_user_statistics')
    with op.batch_alter_table('comment_translation', schema=None) as batch_op:
        batch_op.drop_index('ix_translations_translated_by')

    op.drop_table('comment_translation')
    with op.batch_alter_table('comment_tag', schema=None) as batch_op:
        batch_op.drop_index('ix_tags_name')
        batch_op.drop_index('ix_tags_comment_ids_gin', postgresql_using='gin')
        batch_op.drop_index('ix_tags_business_lang_name')

    op.drop_table('comment_tag')
    with op.batch_alter_table('comment_report_review', schema=None) as batch_op:
        batch_op.drop_index('ix_report_reviews_created_status')
        batch_op.drop_index('ix_report_reviews_comment')

    op.drop_table('comment_report_review')
    with op.batch_alter_table('comment_report', schema=None) as batch_op:
        batch_op.drop_index('ix_reports_created_at_comment_id')
        batch_op.drop_index('ix_reports_comment_user_id')
        batch_op.drop_index('ix_reports_comment_id')

    op.drop_table('comment_report')
    with op.batch_alter_table('comment_moderation', schema=None) as batch_op:
        batch_op.drop_index('ix_moderations_operator')
        batch_op.drop_index('ix_moderations_comment_type')

    op.drop_table('comment_moderation')
    with op.batch_alter_table('comment_full_text_search', schema=None) as batch_op:
        batch_op.drop_index('ix_ts_content', postgresql_using='gin')

    op.drop_table('comment_full_text_search')
    with op.batch_alter_table('comment_event', schema=None) as batch_op:
        batch_op.drop_index('ix_events_user_status_created')
        batch_op.drop_index('ix_events_user_created')

    op.drop_table('comment_event')
    with op.batch_alter_table('comment', schema=None) as batch_op:
        batch_op.drop_index('ix_comments_user_id_created')
        batch_op.drop_index('ix_comments_status_updated_parent_id')
        batch_op.drop_index('ix_comments_root_id_created')
        batch_op.drop_index('ix_comments_business_lang_interact')
        batch_op.drop_index('ix_comments_business_lang_hot')
        batch_op.drop_index('ix_comments_business_lang_created')

    op.drop_table('comment')
    # ### end Alembic commands ###
