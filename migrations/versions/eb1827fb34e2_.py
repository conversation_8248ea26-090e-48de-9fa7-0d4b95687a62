"""empty message

Revision ID: eb1827fb34e2
Revises: b24fdac57129
Create Date: 2025-05-22 07:50:33.561133

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import app.models

# revision identifiers, used by Alembic.
revision = 'eb1827fb34e2'
down_revision = 'b24fdac57129'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('comment_statistics',
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('tip_count', sa.Integer(), nullable=True),
    sa.Column('tip_user_count', sa.Integer(), nullable=True),
    sa.Column('tip_amount', sa.DECIMAL(precision=26, scale=8), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('comment_id')
    )
    op.create_table('comment_tip',
    sa.Column('send_user_id', sa.Integer(), nullable=False),
    sa.Column('receive_user_id', sa.Integer(), nullable=False),
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('amount', sa.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('price', sa.DECIMAL(precision=26, scale=12), nullable=False),
    sa.Column('asset', sa.String(length=32), nullable=False),
    sa.Column('asset_id', sa.Integer(), nullable=False),
    sa.Column('balance_transfer_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('comment_tip', schema=None) as batch_op:
        # batch_op.create_index(batch_op.f('ix_comment_tip_balance_transfer_id'), ['balance_transfer_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_comment_tip_comment_id'), ['comment_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_comment_tip_receive_user_id'), ['receive_user_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_comment_tip_send_user_id'), ['send_user_id'], unique=False)
        batch_op.create_unique_constraint(None, ['balance_transfer_id'])

    op.create_table('daily_comment_tip_report',
    sa.Column('report_date', sa.Date(), nullable=False),
    sa.Column('send_tip_count', sa.Integer(), nullable=True),
    sa.Column('send_tip_user_count', sa.Integer(), nullable=True),
    sa.Column('send_tip_amount', sa.DECIMAL(precision=26, scale=8), nullable=True),
    sa.Column('send_tip_usd', sa.DECIMAL(precision=26, scale=8), nullable=True),
    sa.Column('receive_tip_count', sa.Integer(), nullable=True),
    sa.Column('receive_tip_user_count', sa.Integer(), nullable=True),
    sa.Column('receive_tip_amount', sa.DECIMAL(precision=26, scale=8), nullable=True),
    sa.Column('receive_tip_usd', sa.DECIMAL(precision=26, scale=8), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('report_date')
    )
    op.create_table('monthly_comment_tip_report',
    sa.Column('report_date', sa.Date(), nullable=False),
    sa.Column('send_tip_count', sa.Integer(), nullable=True),
    sa.Column('send_tip_user_count', sa.Integer(), nullable=True),
    sa.Column('send_tip_amount', sa.DECIMAL(precision=26, scale=8), nullable=True),
    sa.Column('send_tip_usd', sa.DECIMAL(precision=26, scale=8), nullable=True),
    sa.Column('receive_tip_count', sa.Integer(), nullable=True),
    sa.Column('receive_tip_user_count', sa.Integer(), nullable=True),
    sa.Column('receive_tip_amount', sa.DECIMAL(precision=26, scale=8), nullable=True),
    sa.Column('receive_tip_usd', sa.DECIMAL(precision=26, scale=8), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.Column('updated_at', app.models.base.TimeStampUTC(timezone=True, precision=6), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('report_date')
    )
    with op.batch_alter_table('comment_user_statistics', schema=None) as batch_op:
        batch_op.add_column(sa.Column('send_tip_count', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('send_tip_user_count', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('send_tip_amount', sa.DECIMAL(precision=26, scale=8), nullable=True))
        batch_op.add_column(sa.Column('receive_tip_count', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('receive_tip_user_count', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('receive_tip_amount', sa.DECIMAL(precision=26, scale=8), nullable=True))
    

    with op.batch_alter_table('comment_event', schema=None) as batch_op:
        batch_op.add_column(sa.Column('extra', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('comment_tip', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='unique')
        batch_op.create_index('ix_comment_tip_balance_transfer_id', ['balance_transfer_id'], unique=False)

    with op.batch_alter_table('comment_event', schema=None) as batch_op:
        batch_op.drop_column('extra')

    with op.batch_alter_table('comment_user_statistics', schema=None) as batch_op:
        batch_op.drop_column('receive_tip_amount')
        batch_op.drop_column('receive_tip_user_count')
        batch_op.drop_column('receive_tip_count')
        batch_op.drop_column('send_tip_amount')
        batch_op.drop_column('send_tip_user_count')
        batch_op.drop_column('send_tip_count')

    op.drop_table('monthly_comment_tip_report')
    op.drop_table('daily_comment_tip_report')
    with op.batch_alter_table('comment_tip', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_comment_tip_send_user_id'))
        batch_op.drop_index(batch_op.f('ix_comment_tip_receive_user_id'))
        batch_op.drop_index(batch_op.f('ix_comment_tip_comment_id'))
        batch_op.drop_index(batch_op.f('ix_comment_tip_balance_transfer_id'))

    op.drop_table('comment_tip')
    op.drop_table('comment_statistics')
    # ### end Alembic commands ###