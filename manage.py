# -*- coding: utf-8 -*-
import gevent.monkey; gevent.monkey.patch_all()

# noinspection PyPep8
import click
from flask.cli import FlaskGroup


def create_app():
    from app import create_app as c
    return c()


@click.group(cls=FlaskGroup, create_app=create_app)
def cli():
    """This is a management script for the application."""

@cli.command()
def update_admin_permissions():
    import requests
    from flask import current_app
    from app import config
    from app.utils.net import url_join
    from app.api.admin import is_admin_white_resource

    base_url = config['BACKEND_INTERNAL_CONFIG']['url']
    app = 'COMMENT'

    view_func = current_app.view_functions
    rules = []
    for rule in list(current_app.url_map.iter_rules()):
        if not rule.rule.startswith('/admin'):
            continue
        if rule.rule.endswith('swagger.json'):
            continue
        for method in rule.methods:
            if method in {'HEAD', 'OPTIONS'}:
                continue
            if is_admin_white_resource(rule.rule, method):
                continue
            if not hasattr(view_func[rule.endpoint], 'view_class'):
                continue
            view_cls = getattr(view_func[rule.endpoint], 'view_class')
            if not hasattr(view_cls, method.lower()):
                continue
            view_method = getattr(view_cls, method.lower())
            doc = view_method.__doc__
            if not doc:
                continue
            doc = doc.replace('(login required)', '')
            doc = doc.replace('(admin required)', '')
            doc = doc.replace('(super admin required)', '')
            doc = doc.replace('(TOTP required)', '')
            doc = doc.strip()
            doc = doc[:64] if doc else None

            r = rule.rule
            if rule.rule.startswith('/admin'):
                r = rule.rule.replace('/admin', '/admin/comment')
            rules.append({
                'name': doc,
                'rule': r,
                'endpoint': rule.endpoint,
                'method': method,
            })

    requests.post(url=url_join(base_url, '/system/admin/permissions'), json={
        'app': app,
        'rules': rules,
    })



@cli.command(
    short_help="Runs an IPython shell in current_app context with some "
               "pre-imported modules.")
def shell():
    """
    overwrite default shell command with ipython support
    """
    import sys
    import IPython
    from traitlets.config import Config
    from flask import current_app

    c = Config()
    c.InteractiveShellEmbed.colors = 'linux'
    c.InteractiveShell.banner1 = '\n'.join([
        '=' * 64,
        f'Python {sys.version.splitlines()[0].strip()} on {sys.platform}',
        f'IPython: {IPython.__version__}',
        f'App: {current_app.import_name}',
        f'Instance: {current_app.instance_path}'
    ])
    c.InteractiveShellApp.exec_lines = [
        'print("\\n")',
        'def print_and_exec(cmd): print(cmd); exec(cmd, globals())',
        'print_and_exec("from datetime import *")',
        'print_and_exec("from decimal import Decimal")',
        'print_and_exec("from flask import current_app, g")',
        'print_and_exec("g.lang = \'en_US\'")',
        'del print_and_exec'
    ]
    c.InteractiveShell.confirm_exit = False

    IPython.start_ipython(
        argv=(),
        user_ns=current_app.make_shell_context(),
        config=c,
    )


if __name__ == '__main__':
    cli()
